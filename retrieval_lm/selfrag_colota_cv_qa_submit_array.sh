#!/bin/bash
#SBATCH --job-name=colota_selfrag_array
#SBATCH --output=slurm_outputs/colota_selfrag_%A_%a_output.txt
#SBATCH --error=slurm_outputs/colota_selfrag_%A_%a_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=48:00:00
#SBATCH --partition=ampere
# Run 4 jobs: 2 datasets x 2 models
#SBATCH --array=0-3

## Dataset and model lists. Index mapping below creates the 4 combinations.
datasets=(
  "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/colota/colota_cv_selfrag_format.json"
  "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/colota/colota_qa_selfrag_format.json"
)
dataset_names=(colota_cv colota_qa)

models=(
  "selfrag/selfrag_llama2_7b"
  "selfrag/selfrag_llama2_13b"
)
model_names=(selfrag7b selfrag13b)

# Compute indices: data_idx iterates slower (every 2 jobs change model)
idx=${SLURM_ARRAY_TASK_ID}
model_idx=$(( idx % ${#models[@]} ))
data_idx=$(( idx / ${#models[@]} ))

input_file=${datasets[$data_idx]}
dataset_name=${dataset_names[$data_idx]}
model_name=${models[$model_idx]}
model_short=${model_names[$model_idx]}

DOWNLOAD_DIR="/home/<USER>/.cache/hf_weights"

# Ensure the download/cache directory exists on the compute node
mkdir -p "${DOWNLOAD_DIR}"

output_file=experiments/colota/${dataset_name}_selfrag_${model_short}_ndocs5_standard_settings.jsonl

echo "Running job ${SLURM_ARRAY_JOB_ID}_${SLURM_ARRAY_TASK_ID}: model=${model_name}, input=${input_file}, output=${output_file}"

python run_short_form.py \
  --model_name ${model_name} \
  --input_file ${input_file} \
  --mode adaptive_retrieval --max_new_tokens 100 \
  --threshold 0.2 \
  --output_file ${output_file} \
  --download_dir ${DOWNLOAD_DIR} \
  --metric match --ndocs 5 --use_groundness --use_utility --use_seqscore \
  --dtype half
