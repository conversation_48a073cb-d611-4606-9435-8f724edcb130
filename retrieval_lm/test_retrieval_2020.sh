#!/bin/bash
#SBATCH --job-name=test_ret_bulk
#SBATCH --output=test_ret_bulk_%j.out
#SBATCH --error=test_ret_bulk_%j.err
#SBATCH --cpus-per-task=8      
#SBATCH --mem=32G             
#SBATCH --partition=ampere
#SBATCH --nodes=1
#SBATCH --gpus-per-node=1
#SBATCH --time=48:00:00     


export PATH="/home/<USER>/.conda/bin:$PATH"
eval "$(conda shell.bash hook)"
conda activate selfrag

#Remember to use the correct embeddings . not just 2020 but contriever 2020.

python passage_retrieval.py \
--model_name_or_path facebook/contriever \
--passages /home/<USER>/atlas/dataset/corpora/wiki/enwiki-dec2020/text-list-100-sec.jsonl \
--passages_embeddings "wikipedia_embeddings_2020/*" \
--data /home/<USER>/selfrag_project/self-rag/retrieval_lm/test_queries.json \
--n_docs 20 \
--output_file testing_contrievermsm_2020_henry_occupation.jsonl