Loading model from: facebook/contriever-msmarco
Indexing passages from files ['wikipedia_embeddings/passages_00', 'wikipedia_embeddings/passages_01', 'wikipedia_embeddings/passages_02', 'wikipedia_embeddings/passages_03', 'wikipedia_embeddings/passages_04', 'wikipedia_embeddings/passages_05', 'wikipedia_embeddings/passages_06', 'wikipedia_embeddings/passages_07', 'wikipedia_embeddings/passages_08', 'wikipedia_embeddings/passages_09', 'wikipedia_embeddings/passages_10', 'wikipedia_embeddings/passages_11', 'wikipedia_embeddings/passages_12', 'wikipedia_embeddings/passages_13', 'wikipedia_embeddings/passages_14', 'wikipedia_embeddings/passages_15']
Loading file wikipedia_embeddings/passages_00
Total data indexed 1000000
Loading file wikipedia_embeddings/passages_01
Total data indexed 2000000
Loading file wikipedia_embeddings/passages_02
Total data indexed 3000000
Loading file wikipedia_embeddings/passages_03
Total data indexed 4000000
Total data indexed 5000000
Loading file wikipedia_embeddings/passages_04
Total data indexed 6000000
Loading file wikipedia_embeddings/passages_05
Total data indexed 7000000
Loading file wikipedia_embeddings/passages_06
Total data indexed 8000000
Total data indexed 9000000
Loading file wikipedia_embeddings/passages_07
Total data indexed 10000000
Loading file wikipedia_embeddings/passages_08
Total data indexed 11000000
Loading file wikipedia_embeddings/passages_09
Total data indexed 12000000
Total data indexed 13000000
Loading file wikipedia_embeddings/passages_10
Total data indexed 14000000
Loading file wikipedia_embeddings/passages_11
Total data indexed 15000000
Loading file wikipedia_embeddings/passages_12
Total data indexed 16000000
Total data indexed 17000000
Loading file wikipedia_embeddings/passages_13
Total data indexed 18000000
Loading file wikipedia_embeddings/passages_14
Total data indexed 19000000
Loading file wikipedia_embeddings/passages_15
Total data indexed 20000000
Total data indexed 21000000
Total data indexed 21015324
Data indexing completed.
Indexing time: 138.5 s.
loading passages
passages have been loaded
Questions embeddings shape: torch.Size([1, 768])
Search time: 7.3 s.
[{'id': '1777071', 'title': 'Machine learning', 'text': 'Machine learning Machine learning (ML) is the study of algorithms and statistical models that computer systems use to progressively improve their performance on a specific task. Machine learning algorithms build a mathematical model of sample data, known as "training data", in order to make predictions or decisions without being explicitly programmed to perform the task. Machine learning algorithms are used in the applications of email filtering, detection of network intruders, and computer vision, where it is infeasible to develop an algorithm of specific instructions for performing the task. Machine learning is closely related to computational statistics, which focuses on making'}, {'id': '1777086', 'title': 'Machine learning', 'text': 'the ability of a learning machine to perform accurately on new, unseen examples/tasks after having experienced a learning data set. The training examples come from some generally unknown probability distribution (considered representative of the space of occurrences) and the learner has to build a general model about this space that enables it to produce sufficiently accurate predictions in new cases. The computational analysis of machine learning algorithms and their performance is a branch of theoretical computer science known as computational learning theory. Because training sets are finite and the future is uncertain, learning theory usually does not yield guarantees of'}, {'id': '1777072', 'title': 'Machine learning', 'text': 'predictions using computers. The study of mathematical optimization delivers methods, theory and application domains to the field of machine learning. Data mining is a field of study within machine learning, and focuses on exploratory data analysis through unsupervised learning. In its application across business problems, machine learning is also referred to as predictive analytics. The name "machine learning" was coined in 1959 by Arthur Samuel. Tom M. Mitchell provided a widely quoted, more formal definition of the algorithms studied in the machine learning field: "A computer program is said to learn from experience "E" with respect to some class of'}, {'id': '12599914', 'title': 'Online machine learning', 'text': 'Online machine learning In computer science, online machine learning is a method of machine learning in which data becomes available in a sequential order and is used to update our best predictor for future data at each step, as opposed to batch learning techniques which generate the best predictor by learning on the entire training data set at once. Online learning is a common technique used in areas of machine learning where it is computationally infeasible to train over the entire dataset, requiring the need of out-of-core algorithms. It is also used in situations where it is necessary for the'}, {'id': '1538498', 'title': 'Learning', 'text': 'landscapes subject to change. In these environments, learning is favored because the fish are predisposed to learn the specific spatial cues where they live. Machine learning, a branch of artificial intelligence, concerns the construction and study of systems that can learn from data. For example, a machine learning system could be trained on email messages to learn to distinguish between spam and non-spam messages. Learning Learning is the process of acquiring new, or modifying existing, knowledge, behaviors, skills, values, or preferences. The ability to learn is possessed by humans, animals, and some machines; there is also evidence for some kind'}, {'id': '11598665', 'title': 'Artificial intelligence marketing', 'text': 'play in this stage as well. Ultimately in an unsupervised model the machine would take the decision and act accordingly to the information it receives at the "collect" stage. "Machine learning is concerned with the design and development of algorithms and techniques that allow computers to "learn"." As defined above machine learning is one of the techniques that can be employed to enable more effective behavioral targeting As mentioned in the behavioral targeting article : ""Many online users & advocacy groups are concerned about privacy issues around doing this type of targeting. This is an area that the behavioral targeting'}, {'id': '11791', 'title': 'Artificial intelligence', 'text': "intelligence. Machine learning, a fundamental concept of AI research since the field's inception, is the study of computer algorithms that improve automatically through experience. Unsupervised learning is the ability to find patterns in a stream of input, without requiring a human to label the inputs first. Supervised learning includes both classification and numerical regression, which requires a human to label the input data first. Classification is used to determine what category something belongs in, after seeing a number of examples of things from several categories. Regression is the attempt to produce a function that describes the relationship between inputs and"}, {'id': '1777104', 'title': 'Machine learning', 'text': 'goal, assuming the set of data is large enough, is to help a machine mimic the human brain’s feature extraction and abstract association capabilities for data that has not been categorized. Rule-based machine learning is a general term for any machine learning method that identifies, learns, or evolves "rules" to store, manipulate or apply knowledge. The defining characteristic of a rule-based machine learning algorithm is the identification and utilization of a set of relational rules that collectively represent the knowledge captured by the system. This is in contrast to other machine learning algorithms that commonly identify a singular model that'}, {'id': '2139772', 'title': 'Theoretical computer science', 'text': 'a scientific discipline that deals with the construction and study of algorithms that can learn from data. Such algorithms operate by building a model based on inputs and using that to make predictions or decisions, rather than following only explicitly programmed instructions. Machine learning can be considered a subfield of computer science and statistics. It has strong ties to artificial intelligence and optimization, which deliver methods, theory and application domains to the field. Machine learning is employed in a range of computing tasks where designing and programming explicit, rule-based algorithms is infeasible. Example applications include spam filtering, optical character recognition'}, {'id': '1777117', 'title': 'Machine learning', 'text': 'a machine learning algorithm had been applied in the field of art history to study fine art paintings, and that it may have revealed previously unrecognized influences between artists. Although machine learning has been transformative in some fields, effective machine learning is difficult because finding patterns is hard and often not enough training data are available; as a result, many machine-learning programs often fail to deliver the expected value. Reasons for this are numerous: lack of (suitable) data, lack of access to the data, data bias, privacy problems, badly chosen tasks and algorithms, wrong tools and people, lack of resources,'}, {'id': '8128895', 'title': 'Machine Learning (journal)', 'text': 'Machine Learning (journal) Machine Learning is a peer-reviewed scientific journal, published since 1986. It should be distinguished from the journal "Machine intelligence" which was established in the mid-1960s. In 2001, forty editors and members of the editorial board of "Machine Learning" resigned in order to support the "Journal of Machine Learning Research" (JMLR), saying that in the era of the internet, it was detrimental for researchers to continue publishing their papers in expensive journals with pay-access archives. Instead, they wrote, they supported the model of "JMLR", in which authors retained copyright over their papers and archives were freely available on'}, {'id': '1777089', 'title': 'Machine learning', 'text': 'a mathematical model of a set of data that contains both the inputs and the desired outputs. The data is known as training data, and consists of a set of training examples. Each training example has one or more inputs and a desired output, also known as a supervisory signal. In the case of semi-supervised learning algorithms, some of the training examples are missing the desired output. In the mathematical model, each training example is represented by an array or vector, and the training data by a matrix. Through iterative optimization of an objective function, supervised learning algorithms learn a'}, {'id': '1777079', 'title': 'Machine learning', 'text': 'computer gaming and artificial intelligence, coined the term "Machine Learning" in 1959 while at IBM. As a scientific endeavour, machine learning grew out of the quest for artificial intelligence. Already in the early days of AI as an academic discipline, some researchers were interested in having machines learn from data. They attempted to approach the problem with various symbolic methods, as well as what were then termed "neural networks"; these were mostly perceptrons and other models that were later found to be reinventions of the generalized linear models of statistics. Probabilistic reasoning was also employed, especially in automated medical diagnosis.'}, {'id': '1777078', 'title': 'Machine learning', 'text': 'program is given a set of natural language documents and finds other documents that cover similar topics. Machine learning algorithms can be used to find the unobservable probability density function in density estimation problems. Meta learning algorithms learn their own inductive bias based on previous experience. In developmental robotics, robot learning algorithms generate their own sequences of learning experiences, also known as a curriculum, to cumulatively acquire new skills through self-guided exploration and social interaction with humans. These robots use guidance mechanisms such as active learning, maturation, motor synergies, and imitation. Arthur Samuel, an American pioneer in the field of'}, {'id': '1777084', 'title': 'Machine learning', 'text': 'are formulated as minimization of some loss function on a training set of examples. Loss functions express the discrepancy between the predictions of the model being trained and the actual problem instances (for example, in classification, one wants to assign a label to instances, and models are trained to correctly predict the pre-assigned labels of a set of examples). The difference between the two fields arises from the goal of generalization: while optimization algorithms can minimize the loss on a training set, machine learning is concerned with minimizing the loss on unseen samples. Machine learning and statistics are closely related'}, {'id': '1777097', 'title': 'Machine learning', 'text': 'reshaping them into higher-dimensional vectors. Deep learning algorithms discover multiple levels of representation, or a hierarchy of features, with higher-level, more abstract features defined in terms of (or generating) lower-level features. It has been argued that an intelligent machine is one that learns a representation that disentangles the underlying factors of variation that explain the observed data. Feature learning is motivated by the fact that machine learning tasks such as classification often require input that is mathematically and computationally convenient to process. However, real-world data such as images, video, and sensory data has not yielded to attempts to algorithmically define'}, {'id': '8128896', 'title': 'Machine Learning (journal)', 'text': 'the internet. Following the mass resignation, Kluwer changed their publishing policy to allow authors to self-archive their papers online after peer-review. Machine Learning (journal) Machine Learning is a peer-reviewed scientific journal, published since 1986. It should be distinguished from the journal "Machine intelligence" which was established in the mid-1960s. In 2001, forty editors and members of the editorial board of "Machine Learning" resigned in order to support the "Journal of Machine Learning Research" (JMLR), saying that in the era of the internet, it was detrimental for researchers to continue publishing their papers in expensive journals with pay-access archives. Instead, they'}, {'id': '1777077', 'title': 'Machine learning', 'text': '"features", or inputs, in a set of data. Active learning algorithms access the desired outputs (training labels) for a limited set of inputs based on a budget, and optimize the choice of inputs for which it will acquire training labels. When used interactively, these can be presented to a human user for labeling. Reinforcement learning algorithms are given feedback in the form of positive or negative reinforcement in a dynamic environment, and are used in autonomous vehicles or in learning to play a game against a human opponent. Other specialized algorithms in machine learning include topic modeling, where the computer'}, {'id': '8128712', 'title': 'Journal of Machine Learning Research', 'text': 'leading machine learning conferences including the International Conference on Machine Learning, COLT, AISTATS, and workshops held at the Conference on Neural Information Processing Systems. Journal of Machine Learning Research The Journal of Machine Learning Research is a peer-reviewed open access scientific journal covering machine learning. It was established in 2000 and the first editor-in-chief was Leslie Kaelbling. The current editors-in-chief are Francis Bach (Inria), David Blei (Columbia University) and Bernhard Schölkopf (Max Planck Institute for Intelligent Systems). The journal was established as an open-access alternative to the journal "Machine Learning". In 2001, forty editorial board members of "Machine Learning" resigned,'}, {'id': '1777118', 'title': 'Machine learning', 'text': 'and evaluation problems. In 2018, a self-driving car from Uber failed to detect a pedestrian, who got killed in the accident. Attempts to use machine learning in healthcare with the IBM Watson system failed to deliver even after years of time and billions of investment. Machine learning approaches in particular can suffer from different data biases. In healthcare data, measurement errors can often result in bias of machine learning applications. A machine learning system trained on your current customers only may not be able to predict the needs of new customer groups that are not represented in the training data.'}]
