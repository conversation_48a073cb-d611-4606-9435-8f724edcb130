#!/bin/bash
#SBATCH --job-name=popqa_llama2_7b-13b_baseline
#SBATCH --output=slurm_outputs/popqa_llama2_7b_13b_baseline_output.txt
#SBATCH --error=slurm_outputs/popqa_llama2_7b_13b_baseline_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=32G
#SBATCH --gpus=1
#SBATCH --time=48:00:00
#SBATCH --partition=ampere  

# First login to HuggingFace
huggingface-cli login --token *************************************

python run_baseline_lm.py \
--model_name meta-llama/Llama-2-7b-chat-hf \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_longtail.json \
--max_new_tokens 100 --metric match \
--result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_vanilla_llama2_7b_instruct_no_retrieval.json \
--task qa --prompt_name "prompt_no_input"


python run_baseline_lm.py \
--model_name meta-llama/Llama-2-13b-chat-hf \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_longtail.json \
--max_new_tokens 100 --metric match \
--result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_vanilla_llama2_13b_instruct_no_retrieval.json \
--task qa --prompt_name "prompt_no_input"