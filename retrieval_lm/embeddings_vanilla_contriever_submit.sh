#!/bin/bash
#SBATCH --job-name=gen_base_0
#SBATCH --output=gen_base_0_%j.out
#SBATCH --error=gen_base_0_%j.err
#SBATCH --cpus-per-task=16
#SBATCH --mem=64G
#SBATCH --partition=ampere
#SBATCH --nodes=1
#SBATCH --gpus-per-node=4

export PATH="/home/<USER>/.conda/bin:$PATH"
eval "$(conda shell.bash hook)"
conda activate selfrag

python generate_passage_embeddings.py \
--model_name_or_path facebook/contriever \
--output_dir wikipedia_embeddings_2020_base \
--passages /home/<USER>/atlas/dataset/corpora/wiki/enwiki-dec2020/text-list-100-sec.jsonl \
--shard_id 0 \
--num_shards 4 &

python generate_passage_embeddings.py \
--model_name_or_path facebook/contriever \
--output_dir wikipedia_embeddings_2020_base \
--passages /home/<USER>/atlas/dataset/corpora/wiki/enwiki-dec2020/text-list-100-sec.jsonl \
--shard_id 1 \
--num_shards 4 &

python generate_passage_embeddings.py \
--model_name_or_path facebook/contriever \
--output_dir wikipedia_embeddings_2020_base \
--passages /home/<USER>/atlas/dataset/corpora/wiki/enwiki-dec2020/text-list-100-sec.jsonl \
--shard_id 2 \
--num_shards 4 &

python generate_passage_embeddings.py \
--model_name_or_path facebook/contriever \
--output_dir wikipedia_embeddings_2020_base \
--passages /home/<USER>/atlas/dataset/corpora/wiki/enwiki-dec2020/text-list-100-sec.jsonl \
--shard_id 3 \
--num_shards 4

wait 


