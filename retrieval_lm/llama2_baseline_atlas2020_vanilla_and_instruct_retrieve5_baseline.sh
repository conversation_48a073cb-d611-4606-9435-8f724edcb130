#!/bin/bash
#SBATCH --job-name=popqa_llama2_all_retrieval_v2
#SBATCH --output=slurm_outputs/popqa_llama2_all_retrieval_v2_output.txt
#SBATCH --error=slurm_outputs/popqa_llama2_all_retrieval_v2_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=32G
#SBATCH --gpus=1
#SBATCH --time=48:00:00
#SBATCH --partition=ampere

# First login to HuggingFace
huggingface-cli login --token *************************************

# Disambig dataset runs
# Vanilla Llama2 7B with retrieval
python run_baseline_lm.py \
--model_name meta-llama/Llama-2-7b-hf \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--max_new_tokens 100 --metric match \
--result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_llama2_7b_vanilla_with_retrieval_5docs.json \
--task qa \
--mode retrieval \
--top_n 5 \
--prompt_name "prompt_no_input_retrieval"

# Vanilla Llama2 13B with retrieval 
python run_baseline_lm.py \
--model_name meta-llama/Llama-2-13b-hf \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--max_new_tokens 100 --metric match \
--result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_llama2_13b_vanilla_with_retrieval_5docs.json \
--task qa \
--mode retrieval \
--top_n 5 \
--prompt_name "prompt_no_input_retrieval"

# Chat-tuned Llama2 7B with retrieval
python run_baseline_lm.py \
--model_name meta-llama/Llama-2-7b-chat-hf \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--max_new_tokens 100 --metric match \
--result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_llama2_7b_instruct_with_retrieval_5docs.json \
--task qa \
--mode retrieval \
--top_n 5 \
--prompt_name "prompt_no_input_retrieval"

# Chat-tuned Llama2 13B with retrieval
python run_baseline_lm.py \
--model_name meta-llama/Llama-2-13b-chat-hf \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--max_new_tokens 100 --metric match \
--result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_llama2_13b_instruct_with_retrieval_5docs.json \
--task qa \
--mode retrieval \
--top_n 5 \
--prompt_name "prompt_no_input_retrieval"

# Original Atlas dataset runs
# Vanilla Llama2 7B with retrieval
python run_baseline_lm.py \
--model_name meta-llama/Llama-2-7b-hf \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_original_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--max_new_tokens 100 --metric match \
--result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_original_atlas_llama2_7b_vanilla_with_retrieval_5docs.json \
--task qa \
--mode retrieval \
--top_n 5 \
--prompt_name "prompt_no_input_retrieval"

# Vanilla Llama2 13B with retrieval 
python run_baseline_lm.py \
--model_name meta-llama/Llama-2-13b-hf \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_original_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--max_new_tokens 100 --metric match \
--result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_original_atlas_llama2_13b_vanilla_with_retrieval_5docs.json \
--task qa \
--mode retrieval \
--top_n 5 \
--prompt_name "prompt_no_input_retrieval"

# Chat-tuned Llama2 7B with retrieval
python run_baseline_lm.py \
--model_name meta-llama/Llama-2-7b-chat-hf \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_original_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--max_new_tokens 100 --metric match \
--result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_original_atlas_llama2_7b_instruct_with_retrieval_5docs.json \
--task qa \
--mode retrieval \
--top_n 5 \
--prompt_name "prompt_no_input_retrieval"

# Chat-tuned Llama2 13B with retrieval
python run_baseline_lm.py \
--model_name meta-llama/Llama-2-13b-chat-hf \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_original_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--max_new_tokens 100 --metric match \
--result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_original_atlas_llama2_13b_instruct_with_retrieval_5docs.json \
--task qa \
--mode retrieval \
--top_n 5 \
--prompt_name "prompt_no_input_retrieval"