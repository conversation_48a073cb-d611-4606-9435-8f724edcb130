#!/bin/bash
#SBATCH --job-name=popqa_all_disambiguated_selfrag7B_selfrag_13B_short_form
#SBATCH --output=slurm_outputs/popqa_all_disambiguated_Selfrag_short_form_output.txt
#SBATCH --error=slurm_outputs/popqa_all_disambiguated_selfrag_short_form_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=24:00:00
#SBATCH --partition=ampere  


python run_short_form.py \
--model_name selfrag/selfrag_llama2_7b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_with_contriever2020atlas_ready_short_form.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_all_disambiguated_SR7B_top5_results.jsonl \
--metric match --ndocs 5 --use_groundness --use_utility --use_seqscore \
--dtype half 

python run_short_form.py \
--model_name selfrag/selfrag_llama2_13b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_with_contriever2020atlas_ready_short_form.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_all_disambiguated_SR13B_top5_results.jsonl \
--metric match --ndocs 5 --use_groundness --use_utility --use_seqscore \
--dtype half 

python run_short_form.py \
--model_name selfrag/selfrag_llama2_7b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_with_contriever2020atlas_ready_short_form.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_all_disambiguated_SR7B_top10_results.jsonl \
--metric match --ndocs 10 --use_groundness --use_utility --use_seqscore \
--dtype half 

python run_short_form.py \
--model_name selfrag/selfrag_llama2_13b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_with_contriever2020atlas_ready_short_form.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_all_disambiguated_SR13B_top10_results.jsonl \
--metric match --ndocs 10 --use_groundness --use_utility --use_seqscore \
--dtype half 

python run_short_form.py \
--model_name selfrag/selfrag_llama2_7b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_with_contriever2020atlas_ready_short_form.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_all_disambiguated_SR7B_top20_results.jsonl \
--metric match --ndocs 20 --use_groundness --use_utility --use_seqscore \
--dtype half 

python run_short_form.py \
--model_name selfrag/selfrag_llama2_13b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_with_contriever2020atlas_ready_short_form.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_all_disambiguated_SR13B_top20_results.jsonl \
--metric match --ndocs 20 --use_groundness --use_utility --use_seqscore \
--dtype half 

