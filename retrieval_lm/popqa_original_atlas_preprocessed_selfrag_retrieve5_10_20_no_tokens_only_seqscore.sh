#!/bin/bash
#SBATCH --job-name=slurm_outputs/popqa_selfrag13b_original_retrieve5_10_20_only_seqscore_short_form
#SBATCH --output=slurm_outputs/popqa_selfrag13b_original_retrieve5_10_20_short_form_only_seqscore_output.txt
#SBATCH --error=slurm_outputs/popqa_selfrag13b_original_retrieve5_10_20_short_form_only_seqscore_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=48:00:00
#SBATCH --partition=ampere  


source /opt/conda/bin/conda
conda activate selfrag

python run_short_form.py \
--model_name selfrag/selfrag_llama2_13b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_original_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_selfrag13b_original_retrieve5_no_tokens_only_seqscore.jsonl \
--metric match --ndocs 5 --use_seqscore \
--dtype half 

python run_short_form.py \
--model_name selfrag/selfrag_llama2_13b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_original_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_selfrag13b_original_retrieve10_no_tokens_only_seqscore.jsonl \
--metric match --ndocs 10 --use_seqscore \
--dtype half 

python run_short_form.py \
--model_name selfrag/selfrag_llama2_13b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_original_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_selfrag13b_original_retrieve20_no_tokens_only_seqscore.jsonl \
--metric match --ndocs 20 --use_seqscore \
--dtype half 

python run_short_form.py \
--model_name selfrag/selfrag_llama2_7b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_original_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_selfrag7b_original_retrieve5_no_tokens_only_seqscore.jsonl \
--metric match --ndocs 5 --use_seqscore \
--dtype half

python run_short_form.py \
--model_name selfrag/selfrag_llama2_7b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_original_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_selfrag7b_original_retrieve10_no_tokens_only_seqscore.jsonl \
--metric match --ndocs 10 --use_seqscore \
--dtype half 

python run_short_form.py \
--model_name selfrag/selfrag_llama2_7b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_original_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_selfrag7b_original_retrieve20_no_tokens_only_seqscore.jsonl \
--metric match --ndocs 20 --use_seqscore \
--dtype half 

