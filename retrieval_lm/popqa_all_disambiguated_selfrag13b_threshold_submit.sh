#!/bin/bash
#SBATCH --job-name=popqa_all_disambiguated_selfrag13B_short_form
#SBATCH --output=popqa_all_disambiguated_short_form_output.txt
#SBATCH --error=popqa_all_disambiguated_short_form_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=18:00:00
#SBATCH --partition=ampere  


python run_short_form.py \
--model_name selfrag/selfrag_llama2_13b \
--input_file experiments/popqa/popqa_all_disambiguated_ready_for_selfrag.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_all_disambiguated_SR13B_results.jsonl \
--metric match --ndocs 5 --use_groundness --use_utility --use_seqscore \
--dtype half 

