#!/bin/bash
#SBATCH --job-name=testing_falcon
#SBATCH --output=slurm_outputs/popqa_testing_falcon_form_output.txt
#SBATCH --error=slurm_outputs/popqa_testing_falcon_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=32G
#SBATCH --gpus=1
#SBATCH --time=24:00:00
#SBATCH --partition=ampere  

conda activate selfrag2

python run_short_form.py \
--model_name tiiuae/Falcon3-10B-Instruct  \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/falcon/testing.jsonl \
--metric match --ndocs 10 --use_groundness --use_utility --use_seqscore \
--dtype half 
