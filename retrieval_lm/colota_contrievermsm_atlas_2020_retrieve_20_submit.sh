#!/bin/bash
#SBATCH --job-name=colota_retrieval          # A descriptive name for the job array
#SBATCH --output=logs/colota_retrieval_%A_%a.out # Separate log file for each array task (%A=array_id, %a=task_id)
#SBATCH --error=logs/colota_retrieval_%A_%a.err  # Separate error file for each array task
#SBATCH --nodes=1                              # Request one node per task
#SBATCH --cpus-per-task=4                        # 4 CPUs for data loading
#SBATCH --mem=16G                              # 16GB memory
#SBATCH --partition=ampere                     # Using a GPU partition as specified
#SBATCH --gpus-per-node=1                        # Request one GPU per task
#SBATCH --time=24:00:00                          # Set a 24-hour time limit (adjust if needed)
#SBATCH --array=0-1                            # Create a job array with two tasks (indices 0 and 1)

# --- Define File Paths and Model ---
MODEL_NAME="facebook/contriever-msmarco"
PASSAGES_PATH="/home/<USER>/atlas/dataset/corpora/wiki/enwiki-dec2020/text-list-100-sec.jsonl"
EMBEDDINGS_PATH="wikipedia_embeddings_2020/*"
N_DOCS=20 # Number of documents to retrieve

# Define the input data directory and output directory
INPUT_DIR="/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/colota"
OUTPUT_DIR="/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/colota"

# Create bash arrays to hold the specific file names for each job
# Task 0 will use the first file in each array, Task 1 will use the second.
INPUT_FILES=(
  "colota_qa_contriever_format.json"
  "colota_cv_contriever_format.json"
)

OUTPUT_FILES=(
  "colota_qa_retrieval_results.json"
  "colota_cv_retrieval_results.json"
)

# --- Select the correct files for this specific array task ---
# SLURM_ARRAY_TASK_ID will be 0 for the first job and 1 for the second.
CURRENT_INPUT_FILE="${INPUT_DIR}/${INPUT_FILES[$SLURM_ARRAY_TASK_ID]}"
CURRENT_OUTPUT_FILE="${OUTPUT_DIR}/${OUTPUT_FILES[$SLURM_ARRAY_TASK_ID]}"

# --- Setup Environment ---
echo "--- JOB START ---"
echo "SLURM JOB ID: ${SLURM_JOB_ID}"
echo "SLURM ARRAY JOB ID: ${SLURM_ARRAY_JOB_ID}"
echo "SLURM ARRAY TASK ID: ${SLURM_ARRAY_TASK_ID}"
echo "Running on host: $(hostname)"
echo "Using GPU: ${CUDA_VISIBLE_DEVICES}"

# Create a directory for logs if it doesn't exist
mkdir -p logs

# Activate Conda environment
export PATH="/home/<USER>/.conda/bin:$PATH"
eval "$(conda shell.bash hook)"
conda activate selfrag
echo "Conda environment 'selfrag' activated."

echo "Model: ${MODEL_NAME}"
echo "Input data: ${CURRENT_INPUT_FILE}"
echo "Output file: ${CURRENT_OUTPUT_FILE}"

# --- Run the Retrieval Script ---
python passage_retrieval.py \
  --model_name_or_path "${MODEL_NAME}" \
  --passages "${PASSAGES_PATH}" \
  --passages_embeddings "${EMBEDDINGS_PATH}" \
  --data "${CURRENT_INPUT_FILE}" \
  --n_docs "${N_DOCS}" \
  --output_file "${CURRENT_OUTPUT_FILE}"

echo "--- JOB END ---"