#!/bin/bash
#SBATCH --job-name=popqa_all_disambiguated_selfrag7B_selfrag_13B_ablation_missing_short_form
#SBATCH --output=slurm_outputs/popqa_all_disambiguated_Selfrag_ablation_missing_short_form_output.txt
#SBATCH --error=slurm_outputs/popqa_all_disambiguated_selfrag_ablation_missing_short_form_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=48:00:00
#SBATCH --partition=ampere  

# Ablation: Minus grounding + utility tokens
python run_short_form.py \
--model_name selfrag/selfrag_llama2_7b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_all_disambiguated_SR7B_minus_grounding_utility.jsonl \
--metric match --ndocs 5 --use_seqscore \
--dtype half

# Ablation: Minus grounding + utility tokens
python run_short_form.py \
--model_name selfrag/selfrag_llama2_13b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_all_disambiguated_SR13B_minus_grounding_utility.jsonl \
--metric match --ndocs 5 --use_seqscore \
--dtype half
