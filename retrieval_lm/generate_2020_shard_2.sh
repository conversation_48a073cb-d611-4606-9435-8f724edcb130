#!/bin/bash
#SBATCH --job-name=gen_embed_1
#SBATCH --output=gen_embed_1_%j.out
#SBATCH --error=gen_embed_1_%j.err
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --partition=ampere
#SBATCH --nodes=1
#SBATCH --gpus-per-node=1

export PATH="/home/<USER>/.conda/bin:$PATH"
eval "$(conda shell.bash hook)"
conda activate selfrag

python generate_passage_embeddings.py \
--model_name_or_path facebook/contriever-msmarco \
--output_dir wikipedia_embeddings_2020 \
--passages /home/<USER>/atlas/dataset/corpora/wiki/enwiki-dec2020/text-list-100-sec.jsonl \
--shard_id 2 \
--num_shards 4