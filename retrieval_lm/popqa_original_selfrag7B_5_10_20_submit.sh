#!/bin/bash
#SBATCH --job-name=popqa_original_no_gs_SR7B_5_10_20_short_form
#SBATCH --output=popqa_original_no_gs_SR7B_5_10_20_output.txt
#SBATCH --error=popqa_original_no_gs_SR7B_5_10_20_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=24:00:00
#SBATCH --partition=ampere  

# Activate conda environment
source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

python run_short_form.py \
    --model_name selfrag/selfrag_llama2_7b \
    --input_file /home/<USER>/self-rag-llama3/self-rag/eval_data_dup/popqa_longtail.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file experiments/popqa/popqa_original_retrieve5_no_gs_SR7B_results.jsonl \
    --metric match --ndocs 5 --use_groundness --use_utility --use_seqscore \
    --dtype half

python run_short_form.py \
    --model_name selfrag/selfrag_llama2_7b \
    --input_file /home/<USER>/self-rag-llama3/self-rag/eval_data_dup/popqa_longtail.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file experiments/popqa/popqa_original_retrieve10_no_gs_SR7B_results.jsonl \
    --metric match --ndocs 10 --use_groundness --use_utility --use_seqscore \
    --dtype half

python run_short_form.py \
    --model_name selfrag/selfrag_llama2_7b \
    --input_file /home/<USER>/self-rag-llama3/self-rag/eval_data_dup/popqa_longtail.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file experiments/popqa/popqa_original_retrieve20_no_gs_SR7B_results.jsonl \
    --metric match --ndocs 20 --use_groundness --use_utility --use_seqscore \
    --dtype half


