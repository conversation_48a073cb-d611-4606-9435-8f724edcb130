from vllm import LLM, SamplingParams
from passage_retrieval import Retriever

def format_prompt(input, paragraph=None):
    prompt = "### Instruction:\n{0}\n\n### Response:\n".format(input)
    if paragraph is not None:
        prompt += "[Retrieval]<paragraph>{0}</paragraph>".format(paragraph)
    return prompt

# Initialize SELF-RAG model
model = LLM("selfrag/selfrag_llama2_7b", dtype="half")
sampling_params = SamplingParams(
    temperature=0.0,
    top_p=1.0,
    max_tokens=100,
    skip_special_tokens=False
)

# Initialize retriever (using the one we set up)
retriever = Retriever({})
retriever.setup_retriever_demo(
    "facebook/contriever-msmarco",
    "psgs_w100.tsv",
    "wikipedia_embeddings/*",
    n_docs=5
)

# Test query
query = "What is machine learning?"

# First try without retrieval
preds = model.generate([format_prompt(query)], sampling_params)
for pred in preds:
    print("Model prediction without retrieval:", pred.outputs[0].text)

# Now with retrieval
retrieved_docs = retriever.search_document(query, 5)
prompts_with_docs = [format_prompt(query, doc["text"]) for doc in retrieved_docs]
preds = model.generate(prompts_with_docs, sampling_params)
for pred in preds:
    print("\nModel prediction with retrieval:", pred.outputs[0].text)