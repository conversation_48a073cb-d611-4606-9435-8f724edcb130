#!/bin/bash
#SBATCH --job-name=popqa_llama_baselines
#SBATCH --output=slurm_outputs/popqa_llama_baselines_output.txt
#SBATCH --error=slurm_outputs/popqa_llama_baselines_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=48:00:00
#SBATCH --partition=ampere

# Base paths
INPUT_FILE="/home/<USER>/self-rag-llama3/self-rag/eval_data_dup/popqa_longtail.json"
OUTPUT_DIR="experiments/popqa"

#######################################
# Vanilla Llama2-7B with Retrieval (5 docs)
#######################################
echo "Starting Llama2-7B retrieval run..."

python run_baseline_lm.py \
--model_name meta-llama/Llama-2-7b-hf \
--input_file ${INPUT_FILE} \
--max_new_tokens 100 \
--metric match \
--result_fp ${OUTPUT_DIR}/popqa_llama7b_retrieve5_author_provided.jsonl \
--task qa \
--mode retrieval \
--top_n 5 \
--prompt_name "prompt_no_input_retrieval"

#######################################
# Vanilla Llama2-13B with Retrieval (5 docs)
#######################################
echo "Starting Llama2-13B retrieval run..."

python run_baseline_lm.py \
--model_name meta-llama/Llama-2-13b-hf \
--input_file ${INPUT_FILE} \
--max_new_tokens 100 \
--metric match \
--result_fp ${OUTPUT_DIR}/popqa_llama13b_retrieve5_author_provided.jsonl \
--task qa \
--mode retrieval \
--top_n 5 \
--prompt_name "prompt_no_input_retrieval"

echo "All baseline runs completed."