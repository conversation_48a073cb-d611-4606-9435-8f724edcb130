==========================================
SLURM_JOB_ID = 73932
SLURM_NODELIST = gpunode02
==========================================
Loading model from: facebook/contriever-msmarco
Indexing passages from files ['wikipedia_embeddings_2020/passages_00', 'wikipedia_embeddings_2020/passages_01', 'wikipedia_embeddings_2020/passages_02', 'wikipedia_embeddings_2020/passages_03']
Loading file wikipedia_embeddings_2020/passages_00
Total data indexed 1000000
Total data indexed 2000000
Total data indexed 3000000
Total data indexed 4000000
Total data indexed 5000000
Total data indexed 6000000
Total data indexed 7000000
Loading file wikipedia_embeddings_2020/passages_01
Total data indexed 8000000
Total data indexed 9000000
Total data indexed 10000000
Total data indexed 11000000
Total data indexed 12000000
Total data indexed 13000000
Total data indexed 14000000
Total data indexed 15000000
Loading file wikipedia_embeddings_2020/passages_02
Total data indexed 16000000
Total data indexed 17000000
Total data indexed 18000000
Total data indexed 19000000
Total data indexed 20000000
Total data indexed 21000000
Total data indexed 22000000
Total data indexed 23000000
Loading file wikipedia_embeddings_2020/passages_03
Total data indexed 24000000
Total data indexed 25000000
Total data indexed 26000000
Total data indexed 27000000
Total data indexed 28000000
Total data indexed 29000000
Total data indexed 30000000
Total data indexed 31000000
Total data indexed 31531283
Data indexing completed.
Indexing time: 269.9 s.
loading passages
passages have been loaded
Loading queries from /home/<USER>/dataset/entity_questions/entity_questions_long_tail_disambiguated_filtered_contrieverready.json
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.4 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.0 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 7.7 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.0 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.0 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.8 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.0 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.3 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.1 s.
Questions embeddings shape: torch.Size([1, 768])
Search time: 6.2 s.
Saving results to /home/<USER>/dataset/entity_questions/entity_questions_long_tail_disambiguated_retrieve20_contrievermsm.json
Results saved to /home/<USER>/dataset/entity_questions/entity_questions_long_tail_disambiguated_retrieve20_contrievermsm.json
