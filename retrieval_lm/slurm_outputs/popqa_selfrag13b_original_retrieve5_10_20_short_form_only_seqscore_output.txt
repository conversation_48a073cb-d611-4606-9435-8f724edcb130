==========================================
SLURM_JOB_ID = 51301
SLURM_NODELIST = gpunode02
==========================================
WARNING 12-17 19:07:48 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-17 19:07:48 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-17 19:09:07 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 12-17 19:09:10 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-17 19:09:16 model_runner.py:437] Graph capturing finished in 5 secs.
average: 0.0
average: 0.7272727272727273
average: 0.8095238095238095
average: 0.7741935483870968
average: 0.7560975609756098
average: 0.803921568627451
average: 0.819672131147541
average: 0.8028169014084507
average: 0.8024691358024691
average: 0.8021978021978022
average: 0.8217821782178217
average: 0.8288288288288288
average: 0.8429752066115702
average: 0.8473282442748091
average: 0.8297872340425532
average: 0.8211920529801324
average: 0.8136645962732919
average: 0.8011695906432749
average: 0.7845303867403315
average: 0.7696335078534031
average: 0.7611940298507462
average: 0.7535545023696683
average: 0.746606334841629
average: 0.7359307359307359
average: 0.7302904564315352
average: 0.7290836653386454
average: 0.7318007662835249
average: 0.7306273062730627
average: 0.7366548042704626
average: 0.7353951890034365
average: 0.7242524916943521
average: 0.7106109324758842
average: 0.6947040498442367
average: 0.6797583081570997
average: 0.6656891495601173
average: 0.6495726495726496
average: 0.6426592797783933
average: 0.6361185983827493
average: 0.6272965879265092
average: 0.6265984654731458
average: 0.6184538653366584
average: 0.6155717761557178
average: 0.6128266033254157
average: 0.6148491879350348
average: 0.6167800453514739
average: 0.6208425720620843
average: 0.6225596529284165
average: 0.6242038216560509
average: 0.6257796257796258
average: 0.6293279022403259
average: 0.6307385229540918
average: 0.6320939334637965
average: 0.6353166986564299
average: 0.64030131826742
average: 0.6395563770794824
average: 0.6442831215970962
average: 0.6488413547237076
average: 0.6514886164623468
average: 0.6557659208261618
average: 0.6598984771573604
average: 0.6655574043261231
average: 0.6693944353518821
average: 0.6698872785829307
average: 0.6719492868462758
average: 0.6755070202808112
average: 0.6758832565284179
average: 0.680786686838124
average: 0.684053651266766
average: 0.6886930983847284
average: 0.6903039073806078
average: 0.6904422253922967
average: 0.6891701828410689
average: 0.680998613037448
average: 0.6771545827633378
average: 0.6693657219973009
average: 0.6644474034620506
average: 0.6609724047306176
average: 0.6562905317769131
average: 0.6568501920614597
average: 0.649810366624526
average: 0.6429463171036205
average: 0.6362515413070283
average: 0.633373934226553
average: 0.6293622141997594
average: 0.629013079667063
average: 0.6251468860164512
average: 0.6225319396051103
average: 0.6176808266360505
average: 0.6140749148694665
average: 0.6094276094276094
average: 0.607103218645949
average: 0.6048298572996706
average: 0.6036916395222585
average: 0.59828141783029
average: 0.594048884165781
average: 0.5878023133543638
average: 0.5816857440166493
average: 0.576725025746653
average: 0.5769622833843018
average: 0.579212916246216
average: 0.5834165834165834
average: 0.5855588526211671
average: 0.5876591576885406
average: 0.5897187196896218
average: 0.5926993275696446
average: 0.5946717411988582
average: 0.5947219604147032
average: 0.5966386554621849
average: 0.599444958371878
average: 0.6003666361136571
average: 0.6003633060853769
average: 0.6021602160216022
average: 0.6057091882247992
average: 0.6056587091069849
average: 0.6082383873794917
average: 0.6090356211989574
average: 0.6098191214470284
average: 0.6114432109308283
average: 0.6121930567315834
average: 0.6112510495382032
average: 0.611157368859284
average: 0.6118909991742362
average: 0.6109746109746109
average: 0.6100731112916328
average: 0.6116035455278002
average: 0.6107114308553158
average: 0.6098334655035687
average: 0.6073957513768686
average: 0.6057767369242779
average: 0.6041828040278854
average: 0.6041506533435819
average: 0.6033562166285278
average: 0.6040878122634368
average: 0.6048084147257701
average: 0.604772557792692
average: 0.6032568467801629
average: 0.6017634092578986
average: 0.5981035740335522
average: 0.6002896451846488
average: 0.6031631919482386
Final result: 0.6032880629020729
Retrieval Frequencies: 155.44444444444446
WARNING 12-17 19:37:54 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-17 19:37:54 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-17 19:38:10 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 12-17 19:38:14 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-17 19:38:19 model_runner.py:437] Graph capturing finished in 5 secs.
average: 0.0
average: 0.7272727272727273
average: 0.8095238095238095
average: 0.7419354838709677
average: 0.7560975609756098
average: 0.803921568627451
average: 0.819672131147541
average: 0.8028169014084507
average: 0.8024691358024691
average: 0.8021978021978022
average: 0.8217821782178217
average: 0.8288288288288288
average: 0.8429752066115702
average: 0.8473282442748091
average: 0.8297872340425532
average: 0.8211920529801324
average: 0.8074534161490683
average: 0.783625730994152
average: 0.7624309392265194
average: 0.7539267015706806
average: 0.7412935323383084
average: 0.7393364928909952
average: 0.7285067873303167
average: 0.7186147186147186
average: 0.7136929460580913
average: 0.7131474103585658
average: 0.7164750957854407
average: 0.7158671586715867
average: 0.7224199288256228
average: 0.7216494845360825
average: 0.7142857142857143
average: 0.7009646302250804
average: 0.6822429906542056
average: 0.6676737160120846
average: 0.6539589442815249
average: 0.6410256410256411
average: 0.6343490304709142
average: 0.628032345013477
average: 0.6246719160104987
average: 0.6240409207161125
average: 0.6159600997506235
average: 0.6131386861313869
average: 0.6128266033254157
average: 0.6148491879350348
average: 0.6167800453514739
average: 0.6208425720620843
average: 0.6225596529284165
average: 0.6242038216560509
average: 0.6257796257796258
average: 0.6293279022403259
average: 0.6307385229540918
average: 0.6320939334637965
average: 0.6353166986564299
average: 0.64030131826742
average: 0.6395563770794824
average: 0.6442831215970962
average: 0.6470588235294118
average: 0.649737302977233
average: 0.6523235800344234
average: 0.6531302876480541
average: 0.6589018302828619
average: 0.662847790507365
average: 0.6634460547504025
average: 0.6656101426307448
average: 0.6692667706708268
average: 0.6697388632872504
average: 0.6747352496217852
average: 0.676602086438152
average: 0.6813509544787077
average: 0.683068017366136
average: 0.6847360912981455
average: 0.6821378340365682
average: 0.6740638002773925
average: 0.6689466484268126
average: 0.6626180836707153
average: 0.6577896138482024
average: 0.6530880420499343
average: 0.6472114137483788
average: 0.646606914212548
average: 0.6396965865992414
average: 0.6329588014981273
average: 0.625154130702836
average: 0.6236297198538368
average: 0.6197352587244284
average: 0.619500594530321
average: 0.6169212690951822
average: 0.6132404181184669
average: 0.6084959816303099
average: 0.6038592508513053
average: 0.5993265993265994
average: 0.5971143174250833
average: 0.5949506037321625
average: 0.5939196525515744
average: 0.5886143931256713
average: 0.5844845908607864
average: 0.5783385909568874
average: 0.5723204994797086
average: 0.5674562306900103
average: 0.5688073394495413
average: 0.5701311806256307
average: 0.5744255744255744
average: 0.5766567754698319
average: 0.5798237022526934
average: 0.5819592628516004
average: 0.5830931796349664
average: 0.5832540437678402
average: 0.5824693685202639
average: 0.5835667600373483
average: 0.5855689176688251
average: 0.5866177818515124
average: 0.5858310626702997
average: 0.5877587758775877
average: 0.5914362176628011
average: 0.5915119363395226
average: 0.5924627519719544
average: 0.5916594265855778
average: 0.5917312661498708
average: 0.5935098206660974
average: 0.5927180355630821
average: 0.5919395465994962
average: 0.5928393005828476
average: 0.5937241948802643
average: 0.5937755937755937
average: 0.5930138099106418
average: 0.5946817082997583
average: 0.5939248601119105
average: 0.5923869944488501
average: 0.5900865460267506
average: 0.5886026541764247
average: 0.5855925639039504
average: 0.5857033051498847
average: 0.5842868039664378
average: 0.5836487509462528
average: 0.5830202854996244
average: 0.5824011931394482
average: 0.5810510732790526
average: 0.5789860396767083
average: 0.5754923413566739
average: 0.5778421433743663
average: 0.5801581595974119
Final result: 0.5804145818441744
Retrieval Frequencies: 155.44444444444446
WARNING 12-17 20:14:15 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-17 20:14:15 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-17 20:14:29 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 12-17 20:14:33 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-17 20:14:38 model_runner.py:437] Graph capturing finished in 5 secs.
average: 0.0
average: 0.7272727272727273
average: 0.7619047619047619
average: 0.7096774193548387
average: 0.7560975609756098
average: 0.7843137254901961
average: 0.8032786885245902
average: 0.7746478873239436
average: 0.7777777777777778
average: 0.7802197802197802
average: 0.7920792079207921
average: 0.7927927927927928
average: 0.8099173553719008
average: 0.816793893129771
average: 0.8014184397163121
average: 0.7947019867549668
average: 0.782608695652174
average: 0.7602339181286549
average: 0.7458563535911602
average: 0.7382198952879581
average: 0.7263681592039801
average: 0.7251184834123223
average: 0.7149321266968326
average: 0.7012987012987013
average: 0.6929460580912863
average: 0.6932270916334662
average: 0.6973180076628352
average: 0.7011070110701108
average: 0.708185053380783
average: 0.7079037800687286
average: 0.7009966777408638
average: 0.6881028938906752
average: 0.6697819314641744
average: 0.6555891238670695
average: 0.6422287390029325
average: 0.6296296296296297
average: 0.6232686980609419
average: 0.6172506738544474
average: 0.6141732283464567
average: 0.6086956521739131
average: 0.6034912718204489
average: 0.5985401459854015
average: 0.5985748218527316
average: 0.6009280742459396
average: 0.6031746031746031
average: 0.6097560975609756
average: 0.6095444685466378
average: 0.6114649681528662
average: 0.6133056133056133
average: 0.6171079429735234
average: 0.6207584830339321
average: 0.62426614481409
average: 0.6276391554702495
average: 0.6308851224105462
average: 0.6303142329020333
average: 0.6352087114337568
average: 0.6381461675579323
average: 0.6409807355516638
average: 0.6419965576592083
average: 0.6446700507614214
average: 0.6505823627287853
average: 0.6513911620294599
average: 0.6521739130434783
average: 0.652931854199683
average: 0.6536661466458659
average: 0.6559139784946236
average: 0.6611195158850227
average: 0.6631892697466468
average: 0.6681350954478708
average: 0.6700434153400868
average: 0.6718972895863052
average: 0.6680731364275668
average: 0.6615811373092927
average: 0.655266757865937
average: 0.6491228070175439
average: 0.644474034620506
average: 0.6399474375821288
average: 0.6342412451361867
average: 0.6286811779769527
average: 0.6207332490518331
average: 0.6142322097378277
average: 0.6066584463625154
average: 0.6053593179049939
average: 0.6040914560770156
average: 0.6040428061831153
average: 0.600470035252644
average: 0.5958188153310104
average: 0.5901262916188289
average: 0.5845629965947786
average: 0.5802469135802469
average: 0.5793562708102109
average: 0.5762897914379802
average: 0.5732899022801303
average: 0.5671321160042965
average: 0.563230605738576
average: 0.5573080967402734
average: 0.5515088449531738
average: 0.5468589083419155
average: 0.5474006116207951
average: 0.5479313824419778
average: 0.5504495504495505
average: 0.552917903066271
average: 0.555337904015671
average: 0.5577109602327837
average: 0.5600384245917387
average: 0.560418648905804
average: 0.5589066918001885
average: 0.5592903828197946
average: 0.5615171137835338
average: 0.5609532538955087
average: 0.5603996366939146
average: 0.5616561656165616
average: 0.5655664585191793
average: 0.5667550839964633
average: 0.5670464504820333
average: 0.5664639443961772
average: 0.5658914728682171
average: 0.5670367207514945
average: 0.5664690939881456
average: 0.5659109991603695
average: 0.5653621981681932
average: 0.5664739884393064
average: 0.5667485667485668
average: 0.5645816409423233
average: 0.5656728444802579
average: 0.5651478816946442
average: 0.5622521808088818
average: 0.5601888276947286
average: 0.5589383294301327
average: 0.5561580170410535
average: 0.5549577248270561
average: 0.5537757437070938
average: 0.5533686601059803
average: 0.5529676934635612
average: 0.5510812826249067
average: 0.5492227979274611
average: 0.547391623806025
average: 0.5441283734500365
average: 0.5467052860246199
average: 0.5492451473759885
Final result: 0.5496783416726233
Retrieval Frequencies: 155.44444444444446
WARNING 12-17 21:04:01 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-17 21:04:01 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-17 21:04:43 llm_engine.py:223] # GPU blocks: 2806, # CPU blocks: 512
INFO 12-17 21:04:46 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-17 21:04:51 model_runner.py:437] Graph capturing finished in 5 secs.
average: 0.0
average: 0.6363636363636364
average: 0.6666666666666666
average: 0.6774193548387096
average: 0.6585365853658537
average: 0.6666666666666666
average: 0.6885245901639344
average: 0.7183098591549296
average: 0.7160493827160493
average: 0.7362637362637363
average: 0.7524752475247525
average: 0.7387387387387387
average: 0.7520661157024794
average: 0.7633587786259542
average: 0.75177304964539
average: 0.7417218543046358
average: 0.7391304347826086
average: 0.7309941520467836
average: 0.7182320441988951
average: 0.7120418848167539
average: 0.7164179104477612
average: 0.7156398104265402
average: 0.7149321266968326
average: 0.7056277056277056
average: 0.6929460580912863
average: 0.6892430278884463
average: 0.6934865900383141
average: 0.6974169741697417
average: 0.7046263345195729
average: 0.7010309278350515
average: 0.6943521594684385
average: 0.6784565916398714
average: 0.6666666666666666
average: 0.649546827794562
average: 0.6422287390029325
average: 0.6267806267806267
average: 0.6204986149584487
average: 0.6064690026954178
average: 0.6010498687664042
average: 0.5907928388746803
average: 0.5810473815461347
average: 0.5717761557177615
average: 0.5676959619952494
average: 0.5707656612529002
average: 0.5736961451247166
average: 0.5809312638580931
average: 0.5835140997830802
average: 0.5902335456475584
average: 0.5945945945945946
average: 0.6008146639511202
average: 0.6027944111776448
average: 0.6086105675146771
average: 0.6161228406909789
average: 0.6214689265536724
average: 0.6229205175600739
average: 0.6279491833030852
average: 0.6327985739750446
average: 0.637478108581436
average: 0.6402753872633391
average: 0.6446700507614214
average: 0.6505823627287853
average: 0.6546644844517185
average: 0.6586151368760065
average: 0.6624405705229794
average: 0.6661466458658346
average: 0.6682027649769585
average: 0.6732223903177005
average: 0.676602086438152
average: 0.6798825256975036
average: 0.6801736613603473
average: 0.68188302425107
average: 0.680731364275668
average: 0.6726768377253814
average: 0.667578659370725
average: 0.659919028340081
average: 0.6551264980026631
average: 0.6517739816031537
average: 0.6459143968871596
average: 0.645326504481434
average: 0.6396965865992414
average: 0.6329588014981273
average: 0.6263871763255241
average: 0.6248477466504263
average: 0.6209386281588448
average: 0.6206896551724138
average: 0.6169212690951822
average: 0.6120789779326364
average: 0.6073478760045924
average: 0.6004540295119183
average: 0.5970819304152637
average: 0.5948945615982242
average: 0.5916575192096597
average: 0.5895765472312704
average: 0.5853920515574651
average: 0.5812964930924548
average: 0.5751840168243953
average: 0.5691987513007284
average: 0.564366632337796
average: 0.563710499490316
average: 0.5681130171543896
average: 0.5704295704295704
average: 0.5727002967359051
average: 0.5739471106758081
average: 0.5761396702230844
average: 0.5782901056676273
average: 0.5813510941960038
average: 0.5834118755890669
average: 0.5854341736694678
average: 0.5874190564292322
average: 0.5875343721356554
average: 0.5885558583106267
average: 0.5913591359135913
average: 0.5950044603033007
average: 0.596816976127321
average: 0.5994741454864154
average: 0.6012163336229366
average: 0.6020671834625323
average: 0.6037574722459437
average: 0.6037256562235394
average: 0.6020151133501259
average: 0.6003330557868443
average: 0.6011560693641619
average: 0.6011466011466011
average: 0.6011372867587328
average: 0.6019339242546333
average: 0.601119104716227
average: 0.598731165741475
average: 0.5955940204563336
average: 0.5940671350507416
average: 0.5933384972889233
average: 0.5933897002305919
average: 0.5919145690312738
average: 0.5919757759273278
average: 0.5935386927122465
average: 0.5943325876211782
average: 0.5943745373797187
average: 0.5922116091109478
average: 0.5893508388037928
average: 0.5916002896451846
average: 0.5945363048166786
Final result: 0.5947105075053609
Retrieval Frequencies: 155.44444444444446
WARNING 12-17 21:26:35 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-17 21:26:35 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-17 21:26:45 llm_engine.py:223] # GPU blocks: 2806, # CPU blocks: 512
INFO 12-17 21:26:48 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-17 21:26:53 model_runner.py:437] Graph capturing finished in 5 secs.
average: 0.0
average: 0.6363636363636364
average: 0.6666666666666666
average: 0.6451612903225806
average: 0.6341463414634146
average: 0.6470588235294118
average: 0.6721311475409836
average: 0.704225352112676
average: 0.7037037037037037
average: 0.7252747252747253
average: 0.7425742574257426
average: 0.7297297297297297
average: 0.743801652892562
average: 0.7557251908396947
average: 0.7446808510638298
average: 0.7350993377483444
average: 0.7329192546583851
average: 0.7251461988304093
average: 0.712707182320442
average: 0.7068062827225131
average: 0.7064676616915423
average: 0.7061611374407583
average: 0.7013574660633484
average: 0.696969696969697
average: 0.6846473029045643
average: 0.6812749003984063
average: 0.6896551724137931
average: 0.6937269372693727
average: 0.701067615658363
average: 0.697594501718213
average: 0.6943521594684385
average: 0.6784565916398714
average: 0.6666666666666666
average: 0.649546827794562
average: 0.6422287390029325
average: 0.6267806267806267
average: 0.6177285318559557
average: 0.6037735849056604
average: 0.5984251968503937
average: 0.5882352941176471
average: 0.5785536159600998
average: 0.5693430656934306
average: 0.5676959619952494
average: 0.5707656612529002
average: 0.5736961451247166
average: 0.5809312638580931
average: 0.5835140997830802
average: 0.5902335456475584
average: 0.5945945945945946
average: 0.6008146639511202
average: 0.6027944111776448
average: 0.6086105675146771
average: 0.6161228406909789
average: 0.6214689265536724
average: 0.6229205175600739
average: 0.6279491833030852
average: 0.6327985739750446
average: 0.637478108581436
average: 0.6402753872633391
average: 0.6446700507614214
average: 0.6505823627287853
average: 0.6546644844517185
average: 0.6586151368760065
average: 0.6608557844690967
average: 0.6645865834633385
average: 0.6666666666666666
average: 0.6717095310136157
average: 0.6751117734724292
average: 0.6798825256975036
average: 0.678726483357453
average: 0.68188302425107
average: 0.680731364275668
average: 0.6726768377253814
average: 0.667578659370725
average: 0.659919028340081
average: 0.6551264980026631
average: 0.6517739816031537
average: 0.6433203631647212
average: 0.6427656850192062
average: 0.6371681415929203
average: 0.630461922596754
average: 0.623921085080148
average: 0.6224116930572473
average: 0.618531889290012
average: 0.6171224732461356
average: 0.6133960047003525
average: 0.6085946573751452
average: 0.6039035591274398
average: 0.5970488081725313
average: 0.5925925925925926
average: 0.5904550499445061
average: 0.5872667398463227
average: 0.5852334419109664
average: 0.5810955961331902
average: 0.5770456960680127
average: 0.5709779179810726
average: 0.5650364203954215
average: 0.5592173017507724
average: 0.5586136595310908
average: 0.562058526740666
average: 0.5634365634365635
average: 0.5647873392680515
average: 0.5661116552399609
average: 0.5683802133850631
average: 0.5696445725264169
average: 0.570884871550904
average: 0.5721017907634307
average: 0.5742296918767507
average: 0.5753931544865865
average: 0.5756186984417965
average: 0.5767484105358764
average: 0.5796579657965797
average: 0.5834076717216771
average: 0.5853227232537578
average: 0.5880806310254163
average: 0.5890529973935708
average: 0.5900086132644272
average: 0.591801878736123
average: 0.5918712955122777
average: 0.5910999160369438
average: 0.5903413821815154
average: 0.5912469033856317
average: 0.5913185913185913
average: 0.5905767668562144
average: 0.5906526994359388
average: 0.5899280575539568
average: 0.5876288659793815
average: 0.5853658536585366
average: 0.5839188134270101
average: 0.5817195972114639
average: 0.5810914681014604
average: 0.5797101449275363
average: 0.579106737320212
average: 0.580015026296018
average: 0.5801640566741237
average: 0.5803108808290155
average: 0.5775165319617928
average: 0.574033552151714
average: 0.5763939174511223
average: 0.5787203450754853
Final result: 0.5789849892780558
Retrieval Frequencies: 155.44444444444446
WARNING 12-17 21:53:57 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-17 21:53:57 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-17 21:54:29 llm_engine.py:223] # GPU blocks: 2806, # CPU blocks: 512
INFO 12-17 21:54:32 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-17 21:54:37 model_runner.py:437] Graph capturing finished in 5 secs.
average: 0.0
average: 0.5454545454545454
average: 0.6190476190476191
average: 0.6129032258064516
average: 0.6097560975609756
average: 0.6274509803921569
average: 0.6557377049180327
average: 0.6901408450704225
average: 0.691358024691358
average: 0.7142857142857143
average: 0.7326732673267327
average: 0.7207207207207207
average: 0.7355371900826446
average: 0.7404580152671756
average: 0.7304964539007093
average: 0.7218543046357616
average: 0.7204968944099379
average: 0.7134502923976608
average: 0.7071823204419889
average: 0.7015706806282722
average: 0.6965174129353234
average: 0.6966824644549763
average: 0.6877828054298643
average: 0.683982683982684
average: 0.6680497925311203
average: 0.6653386454183267
average: 0.6743295019157088
average: 0.6826568265682657
average: 0.6903914590747331
average: 0.6872852233676976
average: 0.6843853820598007
average: 0.6688102893890675
average: 0.6573208722741433
average: 0.6404833836858006
average: 0.6304985337243402
average: 0.6153846153846154
average: 0.6066481994459834
average: 0.5929919137466307
average: 0.5879265091863517
average: 0.578005115089514
average: 0.571072319201995
average: 0.5620437956204379
average: 0.5605700712589073
average: 0.5638051044083526
average: 0.5668934240362812
average: 0.5742793791574279
average: 0.5748373101952278
average: 0.5817409766454352
average: 0.5862785862785863
average: 0.5926680244399185
average: 0.5968063872255489
average: 0.6027397260273972
average: 0.6103646833013435
average: 0.615819209039548
average: 0.6173752310536045
average: 0.6225045372050817
average: 0.6274509803921569
average: 0.6322241681260946
average: 0.6351118760757315
average: 0.6395939086294417
average: 0.6455906821963394
average: 0.6497545008183306
average: 0.6537842190016103
average: 0.6545166402535658
average: 0.656786271450858
average: 0.6574500768049155
average: 0.6626323751891074
average: 0.6661698956780924
average: 0.671071953010279
average: 0.6700434153400868
average: 0.6733238231098431
average: 0.6722925457102672
average: 0.6643550624133149
average: 0.6580027359781122
average: 0.650472334682861
average: 0.6458055925432756
average: 0.6425755584756899
average: 0.6342412451361867
average: 0.6325224071702945
average: 0.6270543615676359
average: 0.6204744069912609
average: 0.6140567200986436
average: 0.6114494518879415
average: 0.6101083032490975
average: 0.6099881093935791
average: 0.6063454759106933
average: 0.6004645760743321
average: 0.5958668197474167
average: 0.5902383654937571
average: 0.5858585858585859
average: 0.58157602663707
average: 0.5773874862788145
average: 0.5765472312703583
average: 0.5714285714285714
average: 0.5674814027630181
average: 0.5615141955835962
average: 0.5556711758584808
average: 0.5499485066941298
average: 0.5484199796126402
average: 0.5519677093844602
average: 0.5534465534465535
average: 0.5539070227497527
average: 0.5563173359451518
average: 0.5567410281280311
average: 0.5571565802113353
average: 0.5575642245480494
average: 0.5579641847313855
average: 0.5611577964519141
average: 0.5624421831637373
average: 0.5627864344637947
average: 0.5640326975476839
average: 0.5670567056705671
average: 0.5709188224799286
average: 0.5720601237842617
average: 0.5740578439964943
average: 0.575152041702867
average: 0.5762273901808785
average: 0.578138343296328
average: 0.5783234546994073
average: 0.5776658270361041
average: 0.5770191507077436
average: 0.5772089182493807
average: 0.5765765765765766
average: 0.5743298131600325
average: 0.5737308622078968
average: 0.5723421262989609
average: 0.5693893735130848
average: 0.5672698662470496
average: 0.5659640905542545
average: 0.5646785437645236
average: 0.563412759415834
average: 0.562929061784897
average: 0.5624526873580621
average: 0.5634861006761833
average: 0.5637583892617449
average: 0.5640266469282014
average: 0.5613519470977223
average: 0.5579868708971554
average: 0.560463432295438
average: 0.5629043853342919
Final result: 0.5632594710507506
Retrieval Frequencies: 155.44444444444446
