Traceback (most recent call last):
  File "run_short_form.py", line 4, in <module>
    import spacy
  File "/home/<USER>/.conda/envs/selfrag2/lib/python3.8/site-packages/spacy/__init__.py", line 16, in <module>
    from .cli.info import info  # noqa: F401
  File "/home/<USER>/.conda/envs/selfrag2/lib/python3.8/site-packages/spacy/cli/__init__.py", line 3, in <module>
    from ._util import app, setup_cli  # noqa: F401
  File "/home/<USER>/.conda/envs/selfrag2/lib/python3.8/site-packages/spacy/cli/_util.py", line 28, in <module>
    from weasel import app as project_cli
  File "/home/<USER>/.conda/envs/selfrag2/lib/python3.8/site-packages/weasel/__init__.py", line 1, in <module>
    from .cli import app
  File "/home/<USER>/.conda/envs/selfrag2/lib/python3.8/site-packages/weasel/cli/__init__.py", line 3, in <module>
    from .assets import project_assets
  File "/home/<USER>/.conda/envs/selfrag2/lib/python3.8/site-packages/weasel/cli/assets.py", line 7, in <module>
    import requests
  File "/home/<USER>/.conda/envs/selfrag2/lib/python3.8/site-packages/requests/__init__.py", line 48, in <module>
    from charset_normalizer import __version__ as charset_normalizer_version
  File "/home/<USER>/.conda/envs/selfrag2/lib/python3.8/site-packages/charset_normalizer/__init__.py", line 24, in <module>
    from .api import from_bytes, from_fp, from_path, normalize
  File "/home/<USER>/.conda/envs/selfrag2/lib/python3.8/site-packages/charset_normalizer/api.py", line 7, in <module>
    from .cd import (
  File "/home/<USER>/.conda/envs/selfrag2/lib/python3.8/site-packages/charset_normalizer/cd.py", line 9, in <module>
    from .md import is_suspiciously_successive_range
AttributeError: partially initialized module 'charset_normalizer' has no attribute 'md__mypyc' (most likely due to a circular import)
