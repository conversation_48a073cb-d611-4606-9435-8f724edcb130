==========================================
SLURM_JOB_ID = 221935
SLURM_NODELIST = gpunode04
==========================================
Running job 221935_3: model=selfrag/selfrag_llama2_13b, input=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/colota/colota_qa_selfrag_format.json, output=experiments/colota/colota_qa_selfrag_selfrag13b_ndocs5_standard_settings.jsonl
WARNING 08-23 08:34:48 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
WARNING 08-23 08:34:55 config.py:1668] Casting torch.bfloat16 to torch.float16.
INFO 08-23 08:35:00 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='selfrag/selfrag_llama2_13b', speculative_config=None, tokenizer='selfrag/selfrag_llama2_13b', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=selfrag/selfrag_llama2_13b, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 08-23 08:35:02 model_runner.py:1056] Starting to load model selfrag/selfrag_llama2_13b...
INFO 08-23 08:35:03 weight_utils.py:243] Using model weights format ['*.bin']
