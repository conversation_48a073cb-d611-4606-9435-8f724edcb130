==========================================
SLURM_JOB_ID = 56319
SLURM_NODELIST = gpunode05
==========================================
Starting Llama2-7B retrieval run...
INFO 01-19 10:47:34 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-7b-hf', tokenizer='meta-llama/Llama-2-7b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-19 10:48:13 llm_engine.py:223] # GPU blocks: 7387, # CPU blocks: 512
INFO 01-19 10:48:14 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-19 10:48:18 model_runner.py:437] Graph capturing finished in 4 secs.
overall result: 0.34524660471765545
Starting Llama2-13B retrieval run...
INFO 01-19 10:55:26 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-13b-hf', tokenizer='meta-llama/Llama-2-13b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-19 10:56:35 llm_engine.py:223] # GPU blocks: 3758, # CPU blocks: 327
INFO 01-19 10:56:37 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-19 10:56:41 model_runner.py:437] Graph capturing finished in 4 secs.
overall result: 0.3738384560400286
All baseline runs completed.
