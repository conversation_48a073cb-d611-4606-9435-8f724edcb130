==========================================
SLURM_JOB_ID = 51229
SLURM_NODELIST = gpunode03
==========================================
WARNING 12-17 09:57:17 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-17 09:57:17 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-17 09:58:22 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 12-17 09:58:24 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-17 09:58:29 model_runner.py:437] Graph capturing finished in 4 secs.
average: 1.0
average: 0.9090909090909091
average: 0.9047619047619048
average: 0.8387096774193549
average: 0.8048780487804879
average: 0.803921568627451
average: 0.819672131147541
average: 0.8169014084507042
average: 0.8271604938271605
average: 0.8241758241758241
average: 0.8316831683168316
average: 0.8378378378378378
average: 0.8512396694214877
average: 0.8549618320610687
average: 0.8439716312056738
average: 0.8278145695364238
average: 0.8136645962732919
average: 0.7953216374269005
average: 0.7734806629834254
average: 0.7643979057591623
average: 0.7562189054726368
average: 0.7535545023696683
average: 0.7420814479638009
average: 0.7316017316017316
average: 0.7219917012448133
average: 0.7211155378486056
average: 0.7203065134099617
average: 0.7158671586715867
average: 0.7188612099644128
average: 0.718213058419244
average: 0.7043189368770764
average: 0.6913183279742765
average: 0.6728971962616822
average: 0.6586102719033232
average: 0.6451612903225806
average: 0.6324786324786325
average: 0.6260387811634349
average: 0.6118598382749326
average: 0.6062992125984252
average: 0.59846547314578
average: 0.5910224438902744
average: 0.5888077858880778
average: 0.5843230403800475
average: 0.5846867749419954
average: 0.5895691609977324
average: 0.5942350332594235
average: 0.596529284164859
average: 0.6029723991507431
average: 0.6070686070686071
average: 0.6130346232179226
average: 0.6147704590818364
average: 0.6164383561643836
average: 0.6199616122840691
average: 0.623352165725047
average: 0.6229205175600739
average: 0.6297640653357531
average: 0.6327985739750446
average: 0.6339754816112084
average: 0.6385542168674698
average: 0.6446700507614214
average: 0.64891846921797
average: 0.6513911620294599
average: 0.6553945249597424
average: 0.6576862123613312
average: 0.6614664586583463
average: 0.663594470046083
average: 0.6671709531013615
average: 0.669150521609538
average: 0.6725403817914831
average: 0.6743849493487699
average: 0.6733238231098431
average: 0.6722925457102672
average: 0.665742024965326
average: 0.6593707250341997
average: 0.6531713900134952
average: 0.6471371504660453
average: 0.6425755584756899
average: 0.6381322957198443
average: 0.6363636363636364
average: 0.6308470290771175
average: 0.6254681647940075
average: 0.6189889025893958
average: 0.6163215590742996
average: 0.6113116726835138
average: 0.6111771700356718
average: 0.6098707403055229
average: 0.6062717770034843
average: 0.6016073478760046
average: 0.5970488081725313
average: 0.5925925925925926
average: 0.5915649278579356
average: 0.5894621295279913
average: 0.5884907709011944
average: 0.5832438238453276
average: 0.5791710945802337
average: 0.573080967402734
average: 0.5671175858480749
average: 0.5623069001029866
average: 0.5616717635066258
average: 0.5640766902119072
average: 0.5684315684315684
average: 0.5707220573689417
average: 0.574926542605289
average: 0.577109602327837
average: 0.5811719500480308
average: 0.5851569933396765
average: 0.586239396795476
average: 0.5882352941176471
average: 0.5920444033302498
average: 0.5939505041246563
average: 0.594913714804723
average: 0.5985598559855986
average: 0.6021409455842998
average: 0.6030061892130858
average: 0.6064855390008764
average: 0.6081668114682884
average: 0.6098191214470284
average: 0.6131511528608027
average: 0.6130397967823878
average: 0.6120906801007556
average: 0.6119900083263947
average: 0.6127167630057804
average: 0.6134316134316135
average: 0.6141348497156783
average: 0.6140209508460919
average: 0.6131095123900879
average: 0.6114195083267249
average: 0.6089693154996066
average: 0.6081186572989852
average: 0.6057319907048799
average: 0.6049192928516526
average: 0.6033562166285278
average: 0.6048448145344436
average: 0.6055597295266717
average: 0.6055182699478001
average: 0.6039970392301999
average: 0.6017634092578986
average: 0.5981035740335522
average: 0.6002896451846488
average: 0.6024442846872753
Final result: 0.6025732666190136
Retrieval Frequencies: 155.44444444444446
WARNING 12-17 10:33:42 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-17 10:33:42 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-17 10:33:54 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 12-17 10:33:57 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-17 10:34:01 model_runner.py:437] Graph capturing finished in 4 secs.
average: 1.0
average: 0.9090909090909091
average: 0.8571428571428571
average: 0.7741935483870968
average: 0.7560975609756098
average: 0.7647058823529411
average: 0.7868852459016393
average: 0.7746478873239436
average: 0.7901234567901234
average: 0.7912087912087912
average: 0.801980198019802
average: 0.8108108108108109
average: 0.8264462809917356
average: 0.8320610687022901
average: 0.8226950354609929
average: 0.8079470198675497
average: 0.8012422360248447
average: 0.783625730994152
average: 0.7679558011049724
average: 0.7591623036649214
average: 0.7562189054726368
average: 0.7535545023696683
average: 0.7420814479638009
average: 0.7272727272727273
average: 0.7136929460580913
average: 0.7131474103585658
average: 0.7126436781609196
average: 0.7084870848708487
average: 0.7117437722419929
average: 0.711340206185567
average: 0.6976744186046512
average: 0.684887459807074
average: 0.6666666666666666
average: 0.6525679758308157
average: 0.6363636363636364
average: 0.6267806267806267
average: 0.6204986149584487
average: 0.6064690026954178
average: 0.5984251968503937
average: 0.5907928388746803
average: 0.5860349127182045
average: 0.583941605839416
average: 0.5795724465558195
average: 0.580046403712297
average: 0.5850340136054422
average: 0.5920177383592018
average: 0.5943600867678959
average: 0.6008492569002123
average: 0.604989604989605
average: 0.6109979633401222
average: 0.6127744510978044
average: 0.6164383561643836
average: 0.6199616122840691
average: 0.623352165725047
average: 0.6229205175600739
average: 0.6279491833030852
average: 0.6327985739750446
average: 0.6339754816112084
average: 0.6385542168674698
average: 0.6412859560067682
average: 0.6455906821963394
average: 0.646481178396072
average: 0.6505636070853462
average: 0.652931854199683
average: 0.6552262090483619
average: 0.6589861751152074
average: 0.6626323751891074
average: 0.6646795827123696
average: 0.6681350954478708
average: 0.6700434153400868
average: 0.6690442225392297
average: 0.6680731364275668
average: 0.6601941747572816
average: 0.6538987688098495
average: 0.6477732793522267
average: 0.6418109187749668
average: 0.6373193166885677
average: 0.6329442282749675
average: 0.6299615877080665
average: 0.6245259165613148
average: 0.6192259675405742
average: 0.6128236744759556
average: 0.610231425091352
average: 0.6064981949458483
average: 0.6064209274673008
average: 0.6051703877790834
average: 0.6016260162601627
average: 0.5970149253731343
average: 0.5902383654937571
average: 0.5847362514029181
average: 0.5837957824639289
average: 0.5817782656421515
average: 0.5808903365906624
average: 0.5757250268528464
average: 0.5717321997874601
average: 0.5657202944269191
average: 0.5598335067637877
average: 0.5550978372811535
average: 0.5545361875637105
average: 0.557013118062563
average: 0.5614385614385614
average: 0.5637982195845698
average: 0.5680705190989226
average: 0.5703200775945684
average: 0.574447646493756
average: 0.5784966698382493
average: 0.5796418473138548
average: 0.5816993464052288
average: 0.5855689176688251
average: 0.5884509624197983
average: 0.5894641235240691
average: 0.5931593159315932
average: 0.5967885816235504
average: 0.5985853227232537
average: 0.6021034180543383
average: 0.6038227628149435
average: 0.6055124892334195
average: 0.6088812980358668
average: 0.6088060965283658
average: 0.6078925272879933
average: 0.6061615320566195
average: 0.6069364161849711
average: 0.6068796068796068
average: 0.6068237205523964
average: 0.6067687348912167
average: 0.6059152677857714
average: 0.6034892942109437
average: 0.6011014948859166
average: 0.5987509758001561
average: 0.5964368706429125
average: 0.5949269792467333
average: 0.593440122044241
average: 0.5942467827403483
average: 0.594290007513148
average: 0.5943325876211782
average: 0.5921539600296077
average: 0.5900073475385745
average: 0.5864332603938731
average: 0.5887038377986966
average: 0.590941768511862
Final result: 0.5911365260900643
Retrieval Frequencies: 155.44444444444446
WARNING 12-17 11:22:21 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-17 11:22:21 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-17 11:22:56 llm_engine.py:223] # GPU blocks: 2806, # CPU blocks: 512
INFO 12-17 11:22:59 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-17 11:23:02 model_runner.py:437] Graph capturing finished in 4 secs.
average: 1.0
average: 0.7272727272727273
average: 0.7142857142857143
average: 0.6774193548387096
average: 0.6585365853658537
average: 0.6862745098039216
average: 0.7213114754098361
average: 0.7464788732394366
average: 0.7407407407407407
average: 0.7582417582417582
average: 0.7623762376237624
average: 0.7567567567567568
average: 0.7768595041322314
average: 0.7862595419847328
average: 0.7730496453900709
average: 0.7615894039735099
average: 0.7577639751552795
average: 0.7426900584795322
average: 0.7292817679558011
average: 0.7225130890052356
average: 0.7164179104477612
average: 0.7156398104265402
average: 0.7058823529411765
average: 0.696969696969697
average: 0.6887966804979253
average: 0.6852589641434262
average: 0.6819923371647509
average: 0.6900369003690037
average: 0.693950177935943
average: 0.6941580756013745
average: 0.6843853820598007
average: 0.6688102893890675
average: 0.6542056074766355
average: 0.6404833836858006
average: 0.6363636363636364
average: 0.6210826210826211
average: 0.6149584487534626
average: 0.601078167115903
average: 0.5958005249343832
average: 0.5907928388746803
average: 0.5810473815461347
average: 0.5742092457420924
average: 0.5700712589073634
average: 0.568445475638051
average: 0.5691609977324263
average: 0.5764966740576497
average: 0.579175704989154
average: 0.5859872611464968
average: 0.5925155925155925
average: 0.5987780040733197
average: 0.6007984031936128
average: 0.6066536203522505
average: 0.6122840690978887
average: 0.6177024482109228
average: 0.6155268022181146
average: 0.6206896551724138
average: 0.6238859180035651
average: 0.6252189141856392
average: 0.6282271944922547
average: 0.6328257191201354
average: 0.6389351081530782
average: 0.6448445171849427
average: 0.6489533011272142
average: 0.6497622820919176
average: 0.6536661466458659
average: 0.6559139784946236
average: 0.659606656580938
average: 0.6616989567809239
average: 0.6651982378854625
average: 0.6657018813314037
average: 0.6690442225392297
average: 0.6694796061884669
average: 0.6615811373092927
average: 0.655266757865937
average: 0.6477732793522267
average: 0.6431424766977364
average: 0.6412614980289093
average: 0.6355382619974059
average: 0.6350832266325224
average: 0.629582806573957
average: 0.6217228464419475
average: 0.6152897657213316
average: 0.6138855054811205
average: 0.6101083032490975
average: 0.6087990487514863
average: 0.6063454759106933
average: 0.6004645760743321
average: 0.5958668197474167
average: 0.5891032917139614
average: 0.5847362514029181
average: 0.5837957824639289
average: 0.5817782656421515
average: 0.5787187839305103
average: 0.5746509129967776
average: 0.5706695005313497
average: 0.5646687697160884
average: 0.558792924037461
average: 0.553038105046344
average: 0.5514780835881753
average: 0.5560040363269425
average: 0.5584415584415584
average: 0.5618199802176064
average: 0.5661116552399609
average: 0.5683802133850631
average: 0.5715658021133525
average: 0.5756422454804948
average: 0.5768143261074458
average: 0.5788982259570495
average: 0.5818686401480111
average: 0.5838680109990834
average: 0.5849227974568574
average: 0.5886588658865887
average: 0.592328278322926
average: 0.5950486295313882
average: 0.5985977212971078
average: 0.6003475238922676
average: 0.602928509905254
average: 0.605465414175918
average: 0.6045723962743438
average: 0.6028547439126785
average: 0.6019983347210658
average: 0.6028075970272502
average: 0.601965601965602
average: 0.6011372867587328
average: 0.6011281224818694
average: 0.5987210231814548
average: 0.5971451229183188
average: 0.5948072383949646
average: 0.5925058548009368
average: 0.5910147172734315
average: 0.5895465026902382
average: 0.5873379099923722
average: 0.5866767600302801
average: 0.5860255447032306
average: 0.5846383296047726
average: 0.5817912657290896
average: 0.5811903012490816
average: 0.5791393143690736
average: 0.5814627081824765
average: 0.5837526959022286
Final result: 0.583988563259471
Retrieval Frequencies: 155.44444444444446
WARNING 12-17 11:49:13 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-17 11:49:13 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-17 11:49:40 llm_engine.py:223] # GPU blocks: 2806, # CPU blocks: 512
INFO 12-17 11:49:42 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-17 11:49:46 model_runner.py:437] Graph capturing finished in 4 secs.
average: 1.0
average: 0.6363636363636364
average: 0.7142857142857143
average: 0.6774193548387096
average: 0.6585365853658537
average: 0.6666666666666666
average: 0.7049180327868853
average: 0.7323943661971831
average: 0.7283950617283951
average: 0.7472527472527473
average: 0.7524752475247525
average: 0.7477477477477478
average: 0.768595041322314
average: 0.7786259541984732
average: 0.7659574468085106
average: 0.7549668874172185
average: 0.7515527950310559
average: 0.7309941520467836
average: 0.7237569060773481
average: 0.7172774869109948
average: 0.7114427860696517
average: 0.7156398104265402
average: 0.7013574660633484
average: 0.6926406926406926
average: 0.6846473029045643
average: 0.6812749003984063
average: 0.6781609195402298
average: 0.6863468634686347
average: 0.6903914590747331
average: 0.6907216494845361
average: 0.6777408637873754
average: 0.662379421221865
average: 0.6479750778816199
average: 0.6344410876132931
average: 0.6275659824046921
average: 0.6125356125356125
average: 0.6038781163434903
average: 0.5902964959568733
average: 0.5853018372703412
average: 0.5805626598465473
average: 0.571072319201995
average: 0.5669099756690997
average: 0.5629453681710214
average: 0.5614849187935035
average: 0.562358276643991
average: 0.5698447893569845
average: 0.5704989154013015
average: 0.5774946921443737
average: 0.5841995841995842
average: 0.5906313645621182
average: 0.5968063872255489
average: 0.6007827788649707
average: 0.6065259117082533
average: 0.6120527306967984
average: 0.609981515711645
average: 0.6152450090744102
average: 0.6185383244206774
average: 0.6199649737302977
average: 0.6247848537005164
average: 0.6294416243654822
average: 0.6356073211314476
average: 0.6382978723404256
average: 0.642512077294686
average: 0.6450079239302694
average: 0.6489859594383776
average: 0.6513056835637481
average: 0.6550680786686838
average: 0.6572280178837556
average: 0.6607929515418502
average: 0.6599131693198264
average: 0.6647646219686163
average: 0.6652601969057665
average: 0.6574202496532594
average: 0.6511627906976745
average: 0.6437246963562753
average: 0.6391478029294274
average: 0.6373193166885677
average: 0.6316472114137484
average: 0.6286811779769527
average: 0.6232616940581542
average: 0.6154806491885143
average: 0.6091245376078915
average: 0.607795371498173
average: 0.6040914560770156
average: 0.6028537455410226
average: 0.599294947121034
average: 0.5923344947735192
average: 0.5866819747416763
average: 0.5800227014755959
average: 0.5757575757575758
average: 0.5749167591564928
average: 0.5718990120746432
average: 0.5678610206297503
average: 0.5628356605800214
average: 0.5589798087141339
average: 0.5531019978969506
average: 0.5473465140478668
average: 0.5417095777548918
average: 0.5402650356778798
average: 0.544904137235116
average: 0.5474525474525475
average: 0.5509396636993076
average: 0.555337904015671
average: 0.5577109602327837
average: 0.5609990393852066
average: 0.5651760228353948
average: 0.5664467483506126
average: 0.5695611577964519
average: 0.572617946345976
average: 0.5747021081576535
average: 0.5758401453224341
average: 0.5796579657965797
average: 0.5834076717216771
average: 0.5853227232537578
average: 0.588957055214724
average: 0.5916594265855778
average: 0.5943152454780362
average: 0.5969257045260461
average: 0.5961049957662997
average: 0.5936188077246012
average: 0.5928393005828476
average: 0.5937241948802643
average: 0.592956592956593
average: 0.5905767668562144
average: 0.5906526994359388
average: 0.5883293365307753
average: 0.5868358445678034
average: 0.5845790715971676
average: 0.5823575331772053
average: 0.5809450038729667
average: 0.5787855495772483
average: 0.5766590389016019
average: 0.5760787282361847
average: 0.574755822689707
average: 0.5734526472781506
average: 0.5706883789785344
average: 0.5701689933872153
average: 0.5674690007293947
average: 0.5698769007965243
average: 0.5722501797268152
Final result: 0.5725518227305219
Retrieval Frequencies: 155.44444444444446
