==========================================
SLURM_JOB_ID = 54417
SLURM_NODELIST = gpunode03
==========================================
Token will not been saved to git credential helper. Pass `add_to_git_credential=True` if you want to set the git credential as well.
Token is valid (permission: fineGrained).
Your token has been saved to /home/<USER>/.cache/huggingface/token
Login successful
INFO 01-07 15:09:54 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-7b-hf', tokenizer='meta-llama/Llama-2-7b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-07 15:10:31 llm_engine.py:223] # GPU blocks: 2807, # CPU blocks: 512
INFO 01-07 15:10:33 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-07 15:10:37 model_runner.py:437] Graph capturing finished in 4 secs.
overall result: 0.11937097927090778
INFO 01-07 15:16:49 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-13b-hf', tokenizer='meta-llama/Llama-2-13b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-07 15:17:54 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 01-07 15:17:57 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-07 15:18:02 model_runner.py:437] Graph capturing finished in 5 secs.
overall result: 0.20943531093638312
INFO 01-07 15:29:01 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-7b-chat-hf', tokenizer='meta-llama/Llama-2-7b-chat-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-07 15:29:32 llm_engine.py:223] # GPU blocks: 2807, # CPU blocks: 512
INFO 01-07 15:29:34 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-07 15:29:38 model_runner.py:437] Graph capturing finished in 3 secs.
overall result: 0.3166547533952823
INFO 01-07 15:34:15 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-13b-chat-hf', tokenizer='meta-llama/Llama-2-13b-chat-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-07 15:35:11 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 01-07 15:35:14 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-07 15:35:19 model_runner.py:437] Graph capturing finished in 5 secs.
overall result: 0.29664045746962114
