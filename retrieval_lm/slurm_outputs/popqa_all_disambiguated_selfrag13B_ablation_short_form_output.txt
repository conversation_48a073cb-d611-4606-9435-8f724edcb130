==========================================
SLURM_JOB_ID = 57570
SLURM_NODELIST = gpunode03
==========================================
WARNING 01-27 15:10:49 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 01-27 15:10:49 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-27 15:12:06 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 01-27 15:12:09 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-27 15:12:14 model_runner.py:437] Graph capturing finished in 5 secs.
average: 1.0
average: 1.0
average: 1.0
average: 1.0
average: 0.975609756097561
average: 0.9803921568627451
average: 0.9672131147540983
average: 0.971830985915493
average: 0.9753086419753086
average: 0.978021978021978
average: 0.9801980198019802
average: 0.9819819819819819
average: 0.9834710743801653
average: 0.9770992366412213
average: 0.9716312056737588
average: 0.9668874172185431
average: 0.9565217391304348
average: 0.9532163742690059
average: 0.9447513812154696
average: 0.9319371727748691
average: 0.9303482587064676
average: 0.9289099526066351
average: 0.918552036199095
average: 0.9134199134199135
average: 0.9045643153526971
average: 0.900398406374502
average: 0.9003831417624522
average: 0.9040590405904059
average: 0.9039145907473309
average: 0.9037800687285223
average: 0.893687707641196
average: 0.8842443729903537
average: 0.8785046728971962
average: 0.8670694864048338
average: 0.8592375366568915
average: 0.8461538461538461
average: 0.8393351800554016
average: 0.8382749326145552
average: 0.8372703412073491
average: 0.8337595907928389
average: 0.830423940149626
average: 0.8272506082725061
average: 0.8242280285035629
average: 0.8213457076566125
average: 0.8231292517006803
average: 0.8248337028824834
average: 0.8264642082429501
average: 0.8301486199575372
average: 0.8316008316008316
average: 0.8329938900203666
average: 0.8323353293413174
average: 0.8356164383561644
average: 0.836852207293666
average: 0.8361581920903954
average: 0.8373382624768947
average: 0.8402903811252269
average: 0.8431372549019608
average: 0.8441330998248686
average: 0.8450946643717728
average: 0.8443316412859561
average: 0.8469217970049917
average: 0.8494271685761048
average: 0.8502415458937198
average: 0.8526148969889065
average: 0.8549141965678627
average: 0.8525345622119815
average: 0.8532526475037822
average: 0.8539493293591655
average: 0.856093979441997
average: 0.8581765557163531
average: 0.8601997146932953
average: 0.8607594936708861
average: 0.8585298196948682
average: 0.8508891928864569
average: 0.8502024291497976
average: 0.8468708388814914
average: 0.8449408672798949
average: 0.8456549935149157
average: 0.8437900128040973
average: 0.8419721871049305
average: 0.8389513108614233
average: 0.8384710234278668
average: 0.8355663824604141
average: 0.8315282791817088
average: 0.83115338882283
average: 0.8284371327849589
average: 0.8257839721254355
average: 0.8231917336394948
average: 0.8229284903518729
average: 0.819304152637486
average: 0.8168701442841287
average: 0.8155872667398463
average: 0.8121606948968513
average: 0.8055853920515574
average: 0.8012752391073327
average: 0.7928496319663512
average: 0.7845993756503642
average: 0.7775489186405767
average: 0.7737003058103975
average: 0.7749747729566094
average: 0.7772227772227772
average: 0.7784371909000989
average: 0.7796278158667973
average: 0.7807953443258971
average: 0.7829010566762729
average: 0.7840152235965747
average: 0.7841658812441094
average: 0.7843137254901961
average: 0.786308973172988
average: 0.7882676443629697
average: 0.7874659400544959
average: 0.7893789378937894
average: 0.7912578055307761
average: 0.7931034482758621
average: 0.7949167397020158
average: 0.7949609035621199
average: 0.7958656330749354
average: 0.7976088812980359
average: 0.7976291278577476
average: 0.7968094038623006
average: 0.7968359700249792
average: 0.7976878612716763
average: 0.7960687960687961
average: 0.7944760357432982
average: 0.7945205479452054
average: 0.7937649880095923
average: 0.7914353687549563
average: 0.7907159716758458
average: 0.7892271662763466
average: 0.7869868319132456
average: 0.7855495772482706
average: 0.7856598016781083
average: 0.785011355034065
average: 0.782870022539444
average: 0.7837434750186428
average: 0.7838638045891931
average: 0.783982365907421
average: 0.7819110138584975
average: 0.7834902244750181
average: 0.7843278217109992
Final result: 0.7834167262330236
Retrieval Frequencies: 155.44444444444446
WARNING 01-27 15:46:28 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 01-27 15:46:28 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-27 15:47:24 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 01-27 15:47:27 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-27 15:47:31 model_runner.py:437] Graph capturing finished in 5 secs.
average: 1.0
average: 1.0
average: 1.0
average: 1.0
average: 0.975609756097561
average: 0.9803921568627451
average: 0.9836065573770492
average: 0.9859154929577465
average: 0.9876543209876543
average: 0.989010989010989
average: 0.9900990099009901
average: 0.990990990990991
average: 0.9917355371900827
average: 0.9847328244274809
average: 0.9787234042553191
average: 0.9735099337748344
average: 0.968944099378882
average: 0.9649122807017544
average: 0.9558011049723757
average: 0.9424083769633508
average: 0.945273631840796
average: 0.943127962085308
average: 0.9321266968325792
average: 0.9264069264069265
average: 0.91701244813278
average: 0.9123505976095617
average: 0.9118773946360154
average: 0.915129151291513
average: 0.9145907473309609
average: 0.9140893470790378
average: 0.9036544850498339
average: 0.8938906752411575
average: 0.8878504672897196
average: 0.8761329305135952
average: 0.8709677419354839
average: 0.8575498575498576
average: 0.850415512465374
average: 0.8490566037735849
average: 0.847769028871391
average: 0.8439897698209718
average: 0.8403990024937655
average: 0.8369829683698297
average: 0.836104513064133
average: 0.8329466357308585
average: 0.8344671201814059
average: 0.835920177383592
average: 0.8373101952277657
average: 0.8407643312101911
average: 0.841995841995842
average: 0.8431771894093686
average: 0.8423153692614771
average: 0.8454011741682974
average: 0.8464491362763915
average: 0.8455743879472694
average: 0.8465804066543438
average: 0.8493647912885662
average: 0.8520499108734403
average: 0.8528896672504378
average: 0.8537005163511188
average: 0.8527918781725888
average: 0.8552412645590682
average: 0.8576104746317512
average: 0.8582930756843801
average: 0.8605388272583201
average: 0.8627145085803433
average: 0.8602150537634409
average: 0.8623298033282905
average: 0.8628912071535022
average: 0.8649045521292217
average: 0.8668596237337193
average: 0.8687589158345221
average: 0.869198312236287
average: 0.8668515950069348
average: 0.8590971272229823
average: 0.8582995951417004
average: 0.8548601864181092
average: 0.8528252299605782
average: 0.8534370946822308
average: 0.8514724711907811
average: 0.8495575221238938
average: 0.8476903870162297
average: 0.8471023427866831
average: 0.8453105968331304
average: 0.8423586040914561
average: 0.8418549346016647
average: 0.8390129259694477
average: 0.8362369337979094
average: 0.8323765786452354
average: 0.8320090805902384
average: 0.8294051627384961
average: 0.8268590455049944
average: 0.8254665203073546
average: 0.8230184581976113
average: 0.8163265306122449
average: 0.8119022316684378
average: 0.8033648790746583
average: 0.7950052029136316
average: 0.787847579814624
average: 0.7838939857288482
average: 0.7850655903128153
average: 0.7872127872127872
average: 0.7883283877349159
average: 0.7894221351616063
average: 0.7904946653734238
average: 0.792507204610951
average: 0.7935299714557564
average: 0.7935909519321395
average: 0.7936507936507936
average: 0.7955596669750231
average: 0.7974335472043996
average: 0.7965485921889192
average: 0.7983798379837984
average: 0.7992863514719001
average: 0.8010610079575596
average: 0.8028045574057844
average: 0.8027801911381407
average: 0.8036175710594315
average: 0.8052946199829206
average: 0.8044030482641829
average: 0.8035264483627204
average: 0.8034970857618651
average: 0.8042939719240297
average: 0.8026208026208026
average: 0.8009748172217709
average: 0.8009669621273167
average: 0.8001598721023181
average: 0.7985725614591594
average: 0.7970102281667978
average: 0.7946916471506635
average: 0.7924089852827265
average: 0.792467332820907
average: 0.7925247902364607
average: 0.7918243754731265
average: 0.7896318557475582
average: 0.790454884414616
average: 0.7905255366395263
average: 0.7905951506245408
average: 0.788475565280817
average: 0.7900072411296162
average: 0.7907979870596693
Final result: 0.7898498927805575
Retrieval Frequencies: 155.44444444444446
WARNING 01-27 16:21:41 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 01-27 16:21:41 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-27 16:21:54 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 01-27 16:21:57 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-27 16:22:01 model_runner.py:437] Graph capturing finished in 4 secs.
average: 1.0
average: 1.0
average: 1.0
average: 1.0
average: 0.975609756097561
average: 0.9803921568627451
average: 0.9836065573770492
average: 0.9859154929577465
average: 0.9876543209876543
average: 0.989010989010989
average: 0.9900990099009901
average: 0.9819819819819819
average: 0.9834710743801653
average: 0.9770992366412213
average: 0.9716312056737588
average: 0.9668874172185431
average: 0.9627329192546584
average: 0.9590643274853801
average: 0.9502762430939227
average: 0.93717277486911
average: 0.9353233830845771
average: 0.933649289099526
average: 0.9230769230769231
average: 0.9177489177489178
average: 0.9087136929460581
average: 0.9043824701195219
average: 0.9042145593869731
average: 0.9077490774907749
average: 0.9074733096085409
average: 0.9072164948453608
average: 0.8970099667774086
average: 0.887459807073955
average: 0.881619937694704
average: 0.8700906344410876
average: 0.8651026392961877
average: 0.8518518518518519
average: 0.8448753462603878
average: 0.8436657681940701
average: 0.84251968503937
average: 0.8388746803069054
average: 0.8354114713216958
average: 0.8321167883211679
average: 0.828978622327791
average: 0.8259860788863109
average: 0.8276643990929705
average: 0.8292682926829268
average: 0.8308026030368764
average: 0.8343949044585988
average: 0.8357588357588358
average: 0.8370672097759674
average: 0.8363273453093812
average: 0.8395303326810176
average: 0.8406909788867563
average: 0.839924670433145
average: 0.8410351201478743
average: 0.8439201451905626
average: 0.8467023172905526
average: 0.8476357267950964
average: 0.8485370051635112
average: 0.8477157360406091
average: 0.8502495840266223
average: 0.8527004909983633
average: 0.8534621578099839
average: 0.8557844690966719
average: 0.858034321372855
average: 0.8556067588325653
average: 0.8562783661119516
average: 0.856929955290611
average: 0.8590308370044053
average: 0.8610709117221418
average: 0.8630527817403709
average: 0.8621659634317862
average: 0.8599167822468793
average: 0.8522571819425444
average: 0.8502024291497976
average: 0.8468708388814914
average: 0.8449408672798949
average: 0.8456549935149157
average: 0.8437900128040973
average: 0.8419721871049305
average: 0.8401997503121099
average: 0.8397040690505548
average: 0.8367844092570037
average: 0.8339350180505415
average: 0.8335315101070154
average: 0.8307873090481787
average: 0.8281068524970964
average: 0.8254879448909299
average: 0.8251986379114642
average: 0.8215488215488216
average: 0.8190899001109878
average: 0.8177826564215148
average: 0.8143322475570033
average: 0.807733619763695
average: 0.8034006376195537
average: 0.7949526813880127
average: 0.7866805411030177
average: 0.7796086508753862
average: 0.7757390417940877
average: 0.7769929364278506
average: 0.7792207792207793
average: 0.7804154302670623
average: 0.781586679725759
average: 0.7827352085354026
average: 0.7848222862632085
average: 0.7859181731684111
average: 0.7869934024505184
average: 0.7871148459383753
average: 0.7890841813135985
average: 0.7910174152153987
average: 0.7901907356948229
average: 0.7920792079207921
average: 0.7939339875111507
average: 0.7957559681697612
average: 0.7975460122699386
average: 0.7975673327541268
average: 0.7984496124031008
average: 0.8001707941929974
average: 0.8001693480101609
average: 0.799328295549958
average: 0.7993338884263114
average: 0.8001651527663088
average: 0.7985257985257985
average: 0.7969130787977254
average: 0.7969379532634971
average: 0.7961630695443646
average: 0.7938144329896907
average: 0.7930763178599528
average: 0.7915690866510539
average: 0.7893106119287374
average: 0.7878554957724827
average: 0.7879481311975591
average: 0.7872823618470856
average: 0.7851239669421488
average: 0.7859806114839671
average: 0.7860843819393042
average: 0.7861866274797943
average: 0.7840991976659373
average: 0.7856625633598842
average: 0.7864845434938893
Final result: 0.7855611150822016
Retrieval Frequencies: 155.44444444444446
WARNING 01-27 16:56:09 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 01-27 16:56:09 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-27 16:56:19 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 01-27 16:56:22 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-27 16:56:26 model_runner.py:437] Graph capturing finished in 4 secs.
average: 0.0
average: 0.9090909090909091
average: 0.9523809523809523
average: 0.967741935483871
average: 0.9512195121951219
average: 0.9607843137254902
average: 0.9508196721311475
average: 0.9436619718309859
average: 0.9506172839506173
average: 0.9560439560439561
average: 0.9603960396039604
average: 0.963963963963964
average: 0.9669421487603306
average: 0.9541984732824428
average: 0.950354609929078
average: 0.9470198675496688
average: 0.9440993788819876
average: 0.935672514619883
average: 0.9281767955801105
average: 0.9162303664921466
average: 0.9154228855721394
average: 0.9146919431279621
average: 0.9049773755656109
average: 0.9004329004329005
average: 0.8921161825726142
average: 0.8884462151394422
average: 0.8888888888888888
average: 0.8929889298892989
average: 0.8932384341637011
average: 0.8934707903780069
average: 0.8837209302325582
average: 0.8745980707395499
average: 0.8691588785046729
average: 0.8580060422960725
average: 0.8533724340175953
average: 0.8433048433048433
average: 0.8365650969529086
average: 0.8355795148247979
average: 0.8346456692913385
average: 0.8312020460358056
average: 0.830423940149626
average: 0.8272506082725061
average: 0.8242280285035629
average: 0.8213457076566125
average: 0.8231292517006803
average: 0.8270509977827051
average: 0.8308026030368764
average: 0.8343949044585988
average: 0.8357588357588358
average: 0.8391038696537678
average: 0.8403193612774451
average: 0.8434442270058709
average: 0.8445297504798465
average: 0.8436911487758946
average: 0.844731977818854
average: 0.8475499092558983
average: 0.8502673796791443
average: 0.851138353765324
average: 0.8519793459552496
average: 0.8527918781725888
average: 0.8552412645590682
average: 0.8576104746317512
average: 0.8582930756843801
average: 0.8605388272583201
average: 0.8627145085803433
average: 0.8602150537634409
average: 0.8623298033282905
average: 0.8628912071535022
average: 0.8649045521292217
average: 0.8654124457308249
average: 0.8673323823109843
average: 0.8677918424753868
average: 0.8654646324549237
average: 0.8577291381668947
average: 0.8569500674763832
average: 0.8548601864181092
average: 0.8541392904073587
average: 0.85473411154345
average: 0.8553137003841229
average: 0.8533501896333755
average: 0.8514357053682896
average: 0.8495684340320592
average: 0.8477466504263094
average: 0.8447653429602888
average: 0.845422116527943
average: 0.8413631022326674
average: 0.8385598141695703
average: 0.835820895522388
average: 0.8331441543700341
average: 0.8305274971941639
average: 0.827968923418424
average: 0.827661909989023
average: 0.8251900108577633
average: 0.8184747583243824
average: 0.8140276301806588
average: 0.8054679284963197
average: 0.7970863683662851
average: 0.7899073120494335
average: 0.7849133537206932
average: 0.7850655903128153
average: 0.7872127872127872
average: 0.7873392680514342
average: 0.7884427032321254
average: 0.7895247332686712
average: 0.7896253602305475
average: 0.7887725975261656
average: 0.7879359095193214
average: 0.7880485527544351
average: 0.790009250693802
average: 0.7901008249312558
average: 0.7892824704813806
average: 0.7911791179117912
average: 0.792149866190901
average: 0.7939876215738285
average: 0.7957931638913234
average: 0.7958297132927888
average: 0.7967269595176572
average: 0.798462852263023
average: 0.7984758679085521
average: 0.7984886649874056
average: 0.79766860949209
average: 0.7985136251032204
average: 0.7977067977067978
average: 0.7977254264825345
average: 0.798549556809025
average: 0.7977617905675459
average: 0.796193497224425
average: 0.7938630999213218
average: 0.7923497267759563
average: 0.7924089852827265
average: 0.7932359723289777
average: 0.7932875667429443
average: 0.7925813777441333
average: 0.7926371149511645
average: 0.7941834451901566
average: 0.7934863064396743
average: 0.7935341660543718
average: 0.7913931436907367
average: 0.7929036929761043
average: 0.7936736161035226
Final result: 0.7927090779127949
Retrieval Frequencies: 155.44444444444446
WARNING 01-27 17:30:30 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 01-27 17:30:30 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-27 17:30:41 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 01-27 17:30:44 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-27 17:30:48 model_runner.py:437] Graph capturing finished in 4 secs.
average: 1.0
average: 1.0
average: 0.9523809523809523
average: 0.967741935483871
average: 0.9512195121951219
average: 0.9607843137254902
average: 0.9508196721311475
average: 0.9577464788732394
average: 0.9506172839506173
average: 0.9560439560439561
average: 0.9603960396039604
average: 0.963963963963964
average: 0.9669421487603306
average: 0.9465648854961832
average: 0.9432624113475178
average: 0.9337748344370861
average: 0.9254658385093167
average: 0.9181286549707602
average: 0.9116022099447514
average: 0.900523560209424
average: 0.9054726368159204
average: 0.9052132701421801
average: 0.8959276018099548
average: 0.8874458874458875
average: 0.8755186721991701
average: 0.8725099601593626
average: 0.8735632183908046
average: 0.8782287822878229
average: 0.8790035587188612
average: 0.8797250859106529
average: 0.8704318936877077
average: 0.8585209003215434
average: 0.8504672897196262
average: 0.8398791540785498
average: 0.8387096774193549
average: 0.8290598290598291
average: 0.8227146814404432
average: 0.8221024258760108
average: 0.821522309711286
average: 0.8184143222506394
average: 0.8179551122194514
average: 0.8126520681265207
average: 0.8052256532066508
average: 0.8004640371229699
average: 0.8027210884353742
average: 0.8070953436807096
average: 0.8091106290672451
average: 0.8131634819532909
average: 0.814968814968815
average: 0.8187372708757638
average: 0.8203592814371258
average: 0.821917808219178
average: 0.8234165067178503
average: 0.8229755178907722
average: 0.8243992606284658
average: 0.8275862068965517
average: 0.8306595365418895
average: 0.8318739054290718
average: 0.8330464716006885
average: 0.8341793570219966
average: 0.8369384359400999
average: 0.839607201309329
average: 0.8421900161030595
average: 0.8446909667194928
average: 0.8471138845553822
average: 0.8463901689708141
average: 0.8472012102874432
average: 0.8479880774962743
average: 0.8502202643171806
average: 0.8523878437047757
average: 0.854493580599144
average: 0.8551336146272855
average: 0.8515950069348127
average: 0.8454172366621067
average: 0.844804318488529
average: 0.8428761651131824
average: 0.8423127463863338
average: 0.8443579766536965
average: 0.8450704225352113
average: 0.8445006321112516
average: 0.8426966292134831
average: 0.842170160295931
average: 0.8404384896467723
average: 0.8375451263537906
average: 0.8370986920332937
average: 0.8343125734430082
average: 0.8315911730545877
average: 0.8289322617680827
average: 0.8286038592508513
average: 0.8260381593714927
average: 0.8224195338512763
average: 0.8210757409440176
average: 0.8175895765472313
average: 0.8120300751879699
average: 0.8065887353878852
average: 0.7981072555205048
average: 0.7898022892819979
average: 0.7826982492276005
average: 0.7798165137614679
average: 0.781029263370333
average: 0.7832167832167832
average: 0.7843719090009891
average: 0.7855044074436827
average: 0.7875848690591658
average: 0.7896253602305475
average: 0.7897240723120837
average: 0.7898209236569275
average: 0.7899159663865546
average: 0.7918593894542091
average: 0.7919340054995417
average: 0.7910990009082652
average: 0.7920792079207921
average: 0.7939339875111507
average: 0.7957559681697612
average: 0.7975460122699386
average: 0.7975673327541268
average: 0.797588285960379
average: 0.7993168232280102
average: 0.7993226079593565
average: 0.799328295549958
average: 0.7993338884263114
average: 0.8001651527663088
average: 0.7985257985257985
average: 0.7985377741673436
average: 0.8001611603545528
average: 0.7985611510791367
average: 0.7969865186360032
average: 0.7954366640440598
average: 0.7939110070257611
average: 0.7939581719597212
average: 0.7932359723289777
average: 0.7940503432494279
average: 0.7940953822861468
average: 0.7941397445529677
average: 0.7956748695003728
average: 0.7964470762398224
average: 0.7964731814842028
average: 0.7950401167031363
average: 0.7965242577842143
average: 0.7972681524083394
Final result: 0.7969978556111508
Retrieval Frequencies: 155.44444444444446
WARNING 01-27 18:04:56 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 01-27 18:04:56 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-27 18:05:07 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 01-27 18:05:10 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-27 18:05:14 model_runner.py:437] Graph capturing finished in 4 secs.
average: 1.0
average: 1.0
average: 0.9523809523809523
average: 0.967741935483871
average: 0.9512195121951219
average: 0.9607843137254902
average: 0.9672131147540983
average: 0.971830985915493
average: 0.9753086419753086
average: 0.978021978021978
average: 0.9801980198019802
average: 0.9819819819819819
average: 0.9834710743801653
average: 0.9770992366412213
average: 0.9716312056737588
average: 0.9602649006622517
average: 0.9440993788819876
average: 0.9415204678362573
average: 0.9337016574585635
average: 0.9214659685863874
average: 0.9154228855721394
average: 0.9146919431279621
average: 0.9049773755656109
average: 0.8961038961038961
average: 0.8879668049792531
average: 0.8844621513944223
average: 0.8850574712643678
average: 0.8892988929889298
average: 0.8896797153024911
average: 0.8900343642611683
average: 0.8803986710963455
average: 0.8681672025723473
average: 0.8598130841121495
average: 0.8489425981873112
average: 0.8475073313782991
average: 0.8376068376068376
average: 0.8310249307479224
average: 0.8301886792452831
average: 0.8293963254593176
average: 0.8260869565217391
average: 0.8254364089775561
average: 0.8175182481751825
average: 0.8147268408551069
average: 0.814385150812065
average: 0.8163265306122449
average: 0.8181818181818182
average: 0.8199566160520607
average: 0.8237791932059448
average: 0.8253638253638254
average: 0.8268839103869654
average: 0.8283433133732535
average: 0.8297455968688845
average: 0.8310940499040307
average: 0.8305084745762712
average: 0.8299445471349353
average: 0.8330308529945554
average: 0.8342245989304813
average: 0.8353765323992994
average: 0.8364888123924269
average: 0.8375634517766497
average: 0.8402662229617305
average: 0.8428805237315876
average: 0.8454106280193237
average: 0.8478605388272583
average: 0.8502340093603744
average: 0.8494623655913979
average: 0.8517397881996974
average: 0.8524590163934426
average: 0.8546255506607929
average: 0.8552821997105644
average: 0.8559201141226819
average: 0.8551336146272855
average: 0.8529819694868238
average: 0.8467852257181943
average: 0.8461538461538461
average: 0.844207723035952
average: 0.8436268068331143
average: 0.8456549935149157
average: 0.8463508322663252
average: 0.8457648546144121
average: 0.8439450686641697
average: 0.843403205918619
average: 0.8416565164433617
average: 0.8399518652226233
average: 0.8406658739595719
average: 0.836662749706228
average: 0.8339140534262486
average: 0.8312284730195177
average: 0.8320090805902384
average: 0.8282828282828283
average: 0.8246392896781354
average: 0.823271130625686
average: 0.8197611292073833
average: 0.8141783029001074
average: 0.8087141339001063
average: 0.8002103049421662
average: 0.7918834547346514
average: 0.7847579814624099
average: 0.781855249745158
average: 0.7830474268415741
average: 0.7852147852147852
average: 0.7863501483679525
average: 0.7874632713026445
average: 0.7895247332686712
average: 0.7915465898174832
average: 0.7925784966698383
average: 0.7935909519321395
average: 0.7936507936507936
average: 0.7955596669750231
average: 0.7974335472043996
average: 0.7965485921889192
average: 0.7974797479747975
average: 0.7992863514719001
average: 0.8010610079575596
average: 0.8028045574057844
average: 0.8027801911381407
average: 0.8027562446167097
average: 0.8044406490179334
average: 0.8044030482641829
average: 0.8043660789252729
average: 0.8043297252289758
average: 0.8051197357555739
average: 0.8034398034398035
average: 0.8034118602761983
average: 0.8049959709911362
average: 0.8033573141486811
average: 0.8017446471054719
average: 0.8001573564122738
average: 0.7993754879000781
average: 0.7993803253292022
average: 0.7986164488854727
average: 0.7993897787948131
average: 0.7993943981831946
average: 0.7993989481592787
average: 0.8008948545861297
average: 0.8016284233900814
average: 0.8008817046289493
average: 0.799416484318016
average: 0.8008689355539464
average: 0.8015815959741194
Final result: 0.8020014295925662
Retrieval Frequencies: 155.44444444444446
