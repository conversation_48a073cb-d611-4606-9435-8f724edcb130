==========================================
SLURM_JOB_ID = 54402
SLURM_NODELIST = gpunode03
==========================================
Token will not been saved to git credential helper. Pass `add_to_git_credential=True` if you want to set the git credential as well.
Token is valid (permission: fineGrained).
Your token has been saved to /home/<USER>/.cache/huggingface/token
Login successful
INFO 01-07 14:38:49 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-7b-chat-hf', tokenizer='meta-llama/Llama-2-7b-chat-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-07 14:44:33 llm_engine.py:223] # GPU blocks: 2807, # CPU blocks: 512
INFO 01-07 14:44:36 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-07 14:44:39 model_runner.py:437] Graph capturing finished in 3 secs.
overall result: 0.24303073624017155
INFO 01-07 14:49:06 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-13b-chat-hf', tokenizer='meta-llama/Llama-2-13b-chat-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-07 14:58:02 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 01-07 14:58:04 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-07 14:58:08 model_runner.py:437] Graph capturing finished in 3 secs.
overall result: 0.20943531093638312
