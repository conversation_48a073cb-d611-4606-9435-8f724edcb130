==========================================
SLURM_JOB_ID = 52346
SLURM_NODELIST = gpunode01
==========================================
WARNING 12-23 06:10:09 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-23 06:10:09 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-23 06:10:42 llm_engine.py:223] # GPU blocks: 3743, # CPU blocks: 512
INFO 12-23 06:10:45 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-23 06:10:49 model_runner.py:437] Graph capturing finished in 4 secs.
average: 0.0
average: 0.6363636363636364
average: 0.8095238095238095
average: 0.8387096774193549
average: 0.8292682926829268
average: 0.8235294117647058
average: 0.8524590163934426
average: 0.8591549295774648
average: 0.8518518518518519
average: 0.8681318681318682
average: 0.8811881188118812
average: 0.8648648648648649
average: 0.8677685950413223
average: 0.8778625954198473
average: 0.8794326241134752
average: 0.8741721854304636
average: 0.8757763975155279
average: 0.8713450292397661
average: 0.8674033149171271
average: 0.8534031413612565
average: 0.8507462686567164
average: 0.8530805687203792
average: 0.8461538461538461
average: 0.8441558441558441
average: 0.8298755186721992
average: 0.8286852589641435
average: 0.8275862068965517
average: 0.8339483394833949
average: 0.8327402135231317
average: 0.8350515463917526
average: 0.8372093023255814
average: 0.8263665594855305
average: 0.8130841121495327
average: 0.797583081570997
average: 0.7976539589442815
average: 0.7891737891737892
average: 0.7811634349030471
average: 0.7762803234501348
average: 0.7742782152230971
average: 0.7723785166240409
average: 0.7680798004987531
average: 0.7688564476885644
average: 0.7648456057007126
average: 0.765661252900232
average: 0.7687074829931972
average: 0.7716186252771619
average: 0.7765726681127982
average: 0.7813163481953291
average: 0.7837837837837838
average: 0.7881873727087576
average: 0.7904191616766467
average: 0.7925636007827789
average: 0.7965451055662188
average: 0.7984934086629002
average: 0.800369685767098
average: 0.8021778584392014
average: 0.8057040998217468
average: 0.8056042031523643
average: 0.8072289156626506
average: 0.8087986463620981
average: 0.8119800332778702
average: 0.8150572831423896
average: 0.8180354267310789
average: 0.820919175911252
average: 0.8221528861154446
average: 0.8218125960061444
average: 0.8245083207261724
average: 0.8256333830104322
average: 0.8281938325991189
average: 0.8277858176555717
average: 0.8302425106990015
average: 0.8312236286919831
average: 0.826629680998613
average: 0.8180574555403557
average: 0.8137651821862348
average: 0.810918774966711
average: 0.80946123521682
average: 0.8119325551232166
average: 0.8117797695262484
average: 0.8103666245259166
average: 0.8102372034956304
average: 0.8101109741060419
average: 0.8112058465286236
average: 0.8098676293622142
average: 0.8109393579072532
average: 0.8084606345475911
average: 0.8037166085946573
average: 0.801377726750861
average: 0.7956867196367764
average: 0.7912457912457912
average: 0.7902330743618202
average: 0.7892425905598244
average: 0.7871878393051032
average: 0.7819548872180451
average: 0.7789585547290117
average: 0.7718191377497371
average: 0.7648283038501561
average: 0.7579814624098867
average: 0.7543323139653415
average: 0.7547931382441978
average: 0.7552447552447552
average: 0.7566765578635015
average: 0.7571008814887366
average: 0.7584869059165859
average: 0.7598463016330451
average: 0.7592768791627021
average: 0.7587181903864278
average: 0.7591036414565826
average: 0.7613320999074931
average: 0.7616865261228231
average: 0.7611262488646685
average: 0.7632763276327633
average: 0.7636039250669046
average: 0.7656940760389036
average: 0.7668711656441718
average: 0.7671589921807124
average: 0.7674418604651163
average: 0.7685738684884714
average: 0.768839966130398
average: 0.7691015952980689
average: 0.7701915070774354
average: 0.7712634186622626
average: 0.7698607698607699
average: 0.7701056051990252
average: 0.7711522965350524
average: 0.7705835331734612
average: 0.7692307692307693
average: 0.7686860739575138
average: 0.7681498829039812
average: 0.7676219984508134
average: 0.7686395080707148
average: 0.7696414950419527
average: 0.7698713096139288
average: 0.7700976709241172
average: 0.7718120805369127
average: 0.772020725388601
average: 0.772226304188097
average: 0.7695113056163384
average: 0.7711803041274439
average: 0.7728253055355859
Final result: 0.7726947819871337
Retrieval Frequencies: 155.44444444444446
WARNING 12-23 06:49:49 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-23 06:49:49 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-23 06:50:46 llm_engine.py:223] # GPU blocks: 1427, # CPU blocks: 327
INFO 12-23 06:50:49 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-23 06:50:53 model_runner.py:437] Graph capturing finished in 5 secs.
average: 0.0
average: 0.9090909090909091
average: 0.9523809523809523
average: 0.967741935483871
average: 0.926829268292683
average: 0.9411764705882353
average: 0.9508196721311475
average: 0.9436619718309859
average: 0.9506172839506173
average: 0.9560439560439561
average: 0.9603960396039604
average: 0.954954954954955
average: 0.9586776859504132
average: 0.9465648854961832
average: 0.9361702127659575
average: 0.9337748344370861
average: 0.9316770186335404
average: 0.9298245614035088
average: 0.9226519337016574
average: 0.9109947643979057
average: 0.9104477611940298
average: 0.909952606635071
average: 0.9004524886877828
average: 0.8917748917748918
average: 0.8838174273858921
average: 0.8804780876494024
average: 0.8812260536398467
average: 0.8856088560885609
average: 0.8861209964412812
average: 0.8865979381443299
average: 0.8770764119601329
average: 0.8681672025723473
average: 0.8629283489096573
average: 0.8489425981873112
average: 0.844574780058651
average: 0.8376068376068376
average: 0.8310249307479224
average: 0.8301886792452831
average: 0.8293963254593176
average: 0.8260869565217391
average: 0.8229426433915212
average: 0.8199513381995134
average: 0.8171021377672208
average: 0.8167053364269141
average: 0.81859410430839
average: 0.8226164079822617
average: 0.8264642082429501
average: 0.8301486199575372
average: 0.8316008316008316
average: 0.8329938900203666
average: 0.8343313373253493
average: 0.837573385518591
average: 0.8387715930902111
average: 0.8380414312617702
average: 0.8391866913123844
average: 0.8421052631578947
average: 0.8431372549019608
average: 0.8441330998248686
average: 0.8450946643717728
average: 0.8460236886632826
average: 0.848585690515807
average: 0.851063829787234
average: 0.8518518518518519
average: 0.8541996830427893
average: 0.8564742589703588
average: 0.8540706605222734
average: 0.8562783661119516
average: 0.856929955290611
average: 0.8590308370044053
average: 0.8596237337192475
average: 0.8587731811697575
average: 0.8593530239099859
average: 0.8571428571428571
average: 0.8495212038303693
average: 0.8488529014844804
average: 0.8468708388814914
average: 0.8462549277266754
average: 0.8469520103761349
average: 0.8476312419974392
average: 0.8457648546144121
average: 0.8439450686641697
average: 0.842170160295931
average: 0.8416565164433617
average: 0.8399518652226233
average: 0.8406658739595719
average: 0.836662749706228
average: 0.8339140534262486
average: 0.8300803673938002
average: 0.8274687854710556
average: 0.8260381593714927
average: 0.8235294117647058
average: 0.823271130625686
average: 0.8208469055374593
average: 0.8141783029001074
average: 0.8097768331562167
average: 0.8012618296529969
average: 0.7929240374609782
average: 0.7857878475798146
average: 0.780835881753313
average: 0.781029263370333
average: 0.7832167832167832
average: 0.7833827893175074
average: 0.7845249755142018
average: 0.7856450048496605
average: 0.7857829010566763
average: 0.7859181731684111
average: 0.7841658812441094
average: 0.7843137254901961
average: 0.7853839037927844
average: 0.7873510540788268
average: 0.7856494096276113
average: 0.7875787578757876
average: 0.7885816235504014
average: 0.7904509283819628
average: 0.7922874671340929
average: 0.792354474370113
average: 0.7932816537467701
average: 0.7950469684030743
average: 0.7950889077053345
average: 0.7951301427371956
average: 0.794338051623647
average: 0.7952105697770437
average: 0.7944307944307945
average: 0.7944760357432982
average: 0.7953263497179693
average: 0.7945643485211831
average: 0.7930214115781126
average: 0.7915027537372148
average: 0.790007806401249
average: 0.7900852052672347
average: 0.7901614142966948
average: 0.7902364607170099
average: 0.789553368660106
average: 0.7896318557475582
average: 0.7912005965697241
average: 0.7905255366395263
average: 0.7905951506245408
average: 0.788475565280817
average: 0.7900072411296162
average: 0.7907979870596693
Final result: 0.7898498927805575
Retrieval Frequencies: 155.44444444444446
