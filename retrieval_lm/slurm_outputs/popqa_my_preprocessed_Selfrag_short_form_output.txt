WARNING 12-10 18:57:39 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-10 18:57:39 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-10 18:58:13 llm_engine.py:223] # GPU blocks: 3743, # CPU blocks: 512
INFO 12-10 18:58:15 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-10 18:58:19 model_runner.py:437] Graph capturing finished in 4 secs.
average: 0.0
average: 0.6363636363636364
average: 0.6666666666666666
average: 0.6774193548387096
average: 0.6585365853658537
average: 0.6862745098039216
average: 0.7213114754098361
average: 0.7464788732394366
average: 0.7407407407407407
average: 0.7582417582417582
average: 0.7722772277227723
average: 0.7657657657657657
average: 0.7851239669421488
average: 0.7938931297709924
average: 0.7872340425531915
average: 0.7814569536423841
average: 0.7763975155279503
average: 0.7660818713450293
average: 0.7513812154696132
average: 0.743455497382199
average: 0.7412935323383084
average: 0.7345971563981043
average: 0.7330316742081447
average: 0.7229437229437229
average: 0.7136929460580913
average: 0.7091633466135459
average: 0.7088122605363985
average: 0.7084870848708487
average: 0.7153024911032029
average: 0.7147766323024055
average: 0.7109634551495017
average: 0.6945337620578779
average: 0.6791277258566978
average: 0.6676737160120846
average: 0.6598240469208211
average: 0.6438746438746439
average: 0.6371191135734072
average: 0.6226415094339622
average: 0.6167979002624672
average: 0.6086956521739131
average: 0.600997506234414
average: 0.5936739659367397
average: 0.5890736342042755
average: 0.5916473317865429
average: 0.5941043083900227
average: 0.6008869179600886
average: 0.6008676789587852
average: 0.6072186836518046
average: 0.6112266112266113
average: 0.6191446028513238
average: 0.6207584830339321
average: 0.6262230919765166
average: 0.6333973128598849
average: 0.6384180790960452
average: 0.6395563770794824
average: 0.6442831215970962
average: 0.6488413547237076
average: 0.6532399299474606
average: 0.6557659208261618
average: 0.6598984771573604
average: 0.6655574043261231
average: 0.6693944353518821
average: 0.6731078904991948
average: 0.6767036450079239
average: 0.6801872074882995
average: 0.6820276497695853
average: 0.686838124054463
average: 0.6900149031296572
average: 0.6916299559471366
average: 0.6917510853835022
average: 0.6932952924393724
average: 0.6933895921237694
average: 0.6851595006934813
average: 0.679890560875513
average: 0.6720647773279352
average: 0.6671105193075899
average: 0.6636005256241787
average: 0.6575875486381323
average: 0.6568501920614597
average: 0.6510745891276865
average: 0.6441947565543071
average: 0.6374845869297164
average: 0.6358099878197321
average: 0.6329723225030084
average: 0.6325802615933412
average: 0.6286721504112809
average: 0.6225319396051103
average: 0.6176808266360505
average: 0.6106696935300795
average: 0.6071829405162739
average: 0.6059933407325194
average: 0.6026344676180022
average: 0.6015200868621065
average: 0.5972073039742213
average: 0.5929861849096706
average: 0.5867507886435331
average: 0.5806451612903226
average: 0.5756951596292482
average: 0.5739041794087666
average: 0.5782038345105953
average: 0.5804195804195804
average: 0.5835806132542037
average: 0.5866797257590598
average: 0.588748787584869
average: 0.5917387127761767
average: 0.5956232159847764
average: 0.5975494816211122
average: 0.5994397759103641
average: 0.6022201665124884
average: 0.6021998166819432
average: 0.6030881017257039
average: 0.6057605760576058
average: 0.6092774308652988
average: 0.6118479221927497
average: 0.6152497808939527
average: 0.6168549087749783
average: 0.6175710594315246
average: 0.6199829205807003
average: 0.619813717188823
average: 0.617968094038623
average: 0.6161532056619484
average: 0.6160198183319571
average: 0.6158886158886159
average: 0.6157595450852965
average: 0.6164383561643836
average: 0.6163069544364509
average: 0.6137985725614592
average: 0.6105428796223447
average: 0.6088992974238876
average: 0.6080557707203718
average: 0.6079938508839354
average: 0.6056445461479787
average: 0.6056018168054504
average: 0.6070623591284748
average: 0.6085011185682326
average: 0.6062176165803109
average: 0.6047024246877296
average: 0.6010211524434719
average: 0.6031860970311369
average: 0.606038820992092
Final result: 0.6061472480343102
Retrieval Frequencies: 155.44444444444446
WARNING 12-10 19:30:46 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-10 19:30:46 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-10 19:31:55 llm_engine.py:223] # GPU blocks: 1427, # CPU blocks: 327
INFO 12-10 19:31:58 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-10 19:32:03 model_runner.py:437] Graph capturing finished in 5 secs.
average: 1.0
average: 0.8181818181818182
average: 0.8571428571428571
average: 0.8064516129032258
average: 0.7804878048780488
average: 0.8235294117647058
average: 0.8360655737704918
average: 0.8450704225352113
average: 0.8395061728395061
average: 0.8351648351648352
average: 0.8514851485148515
average: 0.8468468468468469
average: 0.859504132231405
average: 0.8625954198473282
average: 0.851063829787234
average: 0.847682119205298
average: 0.8385093167701864
average: 0.8245614035087719
average: 0.8066298342541437
average: 0.7958115183246073
average: 0.7860696517412935
average: 0.7819905213270142
average: 0.7737556561085973
average: 0.7662337662337663
average: 0.7593360995850622
average: 0.7569721115537849
average: 0.7586206896551724
average: 0.7601476014760148
average: 0.7651245551601423
average: 0.7628865979381443
average: 0.7541528239202658
average: 0.7395498392282959
average: 0.7227414330218068
average: 0.7069486404833837
average: 0.6920821114369502
average: 0.6752136752136753
average: 0.667590027700831
average: 0.660377358490566
average: 0.6509186351706037
average: 0.6470588235294118
average: 0.6359102244389028
average: 0.6326034063260341
average: 0.6294536817102138
average: 0.62877030162413
average: 0.6303854875283447
average: 0.6363636363636364
average: 0.6377440347071583
average: 0.643312101910828
average: 0.6444906444906445
average: 0.6456211812627292
average: 0.6447105788423154
average: 0.649706457925636
average: 0.6506717850287908
average: 0.655367231638418
average: 0.6580406654343808
average: 0.6606170598911071
average: 0.6631016042780749
average: 0.6654991243432574
average: 0.6695352839931153
average: 0.6717428087986463
average: 0.6772046589018302
average: 0.6808510638297872
average: 0.6827697262479872
average: 0.6846275752773375
average: 0.6879875195007801
average: 0.6897081413210445
average: 0.6928895612708018
average: 0.6959761549925484
average: 0.7004405286343612
average: 0.7018813314037626
average: 0.703281027104137
average: 0.7018284106891702
average: 0.694868238557559
average: 0.6880984952120383
average: 0.6815114709851552
average: 0.6764314247669774
average: 0.6727989487516426
average: 0.6679636835278858
average: 0.6670934699103713
average: 0.6586599241466498
average: 0.651685393258427
average: 0.6448828606658447
average: 0.6406820950060901
average: 0.6365824308062575
average: 0.6349583828775267
average: 0.6310223266745005
average: 0.6283391405342624
average: 0.6222732491389208
average: 0.6174801362088536
average: 0.6127946127946128
average: 0.611542730299667
average: 0.6092206366630076
average: 0.6080347448425625
average: 0.602577873254565
average: 0.5982996811902231
average: 0.5920084121976866
average: 0.5858480749219563
average: 0.5808444902162719
average: 0.5790010193679919
average: 0.5812310797174571
average: 0.5854145854145855
average: 0.5885262116716122
average: 0.5915768854064642
average: 0.5945683802133851
average: 0.5984630163304515
average: 0.5984776403425309
average: 0.5994344957587182
average: 0.6003734827264239
average: 0.6040703052728955
average: 0.6058661778185152
average: 0.6058128973660308
average: 0.6075607560756076
average: 0.6110615521855486
average: 0.6118479221927497
average: 0.6143733567046451
average: 0.6159860990443093
average: 0.6175710594315246
average: 0.6191289496157131
average: 0.6189669771380186
average: 0.617968094038623
average: 0.6178184845961698
average: 0.6168455821635013
average: 0.6158886158886159
average: 0.6141348497156783
average: 0.6148267526188558
average: 0.6139088729016786
average: 0.6130055511498811
average: 0.6105428796223447
average: 0.6081186572989852
average: 0.6072811773818745
average: 0.6056879323597233
average: 0.6056445461479787
average: 0.607115821347464
average: 0.6055597295266717
average: 0.6055182699478001
average: 0.6039970392301999
average: 0.6024981631153563
average: 0.5995623632385121
average: 0.6017378711078928
average: 0.603882099209202
Final result: 0.6040028591851322
Retrieval Frequencies: 155.44444444444446
WARNING 12-10 20:17:19 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-10 20:17:19 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-10 20:17:47 llm_engine.py:223] # GPU blocks: 3743, # CPU blocks: 512
INFO 12-10 20:17:50 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-10 20:17:54 model_runner.py:437] Graph capturing finished in 4 secs.
average: 0.0
average: 0.6363636363636364
average: 0.6666666666666666
average: 0.6451612903225806
average: 0.6341463414634146
average: 0.6666666666666666
average: 0.7049180327868853
average: 0.7323943661971831
average: 0.7283950617283951
average: 0.7362637362637363
average: 0.7524752475247525
average: 0.7477477477477478
average: 0.768595041322314
average: 0.7786259541984732
average: 0.7730496453900709
average: 0.7682119205298014
average: 0.7639751552795031
average: 0.7543859649122807
average: 0.7403314917127072
average: 0.7382198952879581
average: 0.736318407960199
average: 0.7345971563981043
average: 0.7285067873303167
average: 0.7186147186147186
average: 0.7095435684647303
average: 0.7051792828685259
average: 0.7049808429118773
average: 0.7047970479704797
average: 0.7117437722419929
average: 0.711340206185567
average: 0.707641196013289
average: 0.6881028938906752
average: 0.6728971962616822
average: 0.6616314199395771
average: 0.656891495601173
average: 0.6410256410256411
average: 0.6343490304709142
average: 0.6172506738544474
average: 0.6115485564304461
average: 0.6061381074168798
average: 0.600997506234414
average: 0.5936739659367397
average: 0.5890736342042755
average: 0.5916473317865429
average: 0.5941043083900227
average: 0.6008869179600886
average: 0.6030368763557483
average: 0.6093418259023354
average: 0.6133056133056133
average: 0.6211812627291242
average: 0.6227544910179641
average: 0.6281800391389433
average: 0.6353166986564299
average: 0.64030131826742
average: 0.6414048059149723
average: 0.6460980036297641
average: 0.6506238859180036
average: 0.6549912434325744
average: 0.657487091222031
average: 0.6615905245346869
average: 0.6672212978369384
average: 0.6710310965630114
average: 0.6747181964573269
average: 0.6782884310618067
average: 0.6801872074882995
average: 0.6820276497695853
average: 0.6853252647503782
average: 0.6885245901639344
average: 0.6916299559471366
average: 0.6917510853835022
average: 0.6947218259629101
average: 0.6947960618846695
average: 0.6865464632454924
average: 0.6812585499316005
average: 0.6734143049932524
average: 0.6684420772303595
average: 0.6649145860709592
average: 0.6575875486381323
average: 0.6568501920614597
average: 0.6510745891276865
average: 0.6441947565543071
average: 0.6374845869297164
average: 0.6358099878197321
average: 0.6329723225030084
average: 0.6313912009512486
average: 0.627497062279671
average: 0.6213704994192799
average: 0.616532721010333
average: 0.6095346197502838
average: 0.6049382716049383
average: 0.6037735849056604
average: 0.6004390779363337
average: 0.5993485342019544
average: 0.5950590762620838
average: 0.5908607863974495
average: 0.5846477392218717
average: 0.578563995837669
average: 0.572605561277034
average: 0.5708460754332314
average: 0.5751765893037336
average: 0.5764235764235764
average: 0.5786350148367952
average: 0.5808031341821743
average: 0.5829291949563531
average: 0.5859750240153698
average: 0.5889628924833492
average: 0.590951932139491
average: 0.5929038281979458
average: 0.5957446808510638
average: 0.5957836846929423
average: 0.5967302452316077
average: 0.5994599459945995
average: 0.6030330062444246
average: 0.6056587091069849
average: 0.6091148115687993
average: 0.6099044309296264
average: 0.611541774332472
average: 0.61400512382579
average: 0.6138865368331922
average: 0.6129303106633082
average: 0.6119900083263947
average: 0.6118909991742362
average: 0.6117936117936118
average: 0.6108854589764419
average: 0.6116035455278002
average: 0.6107114308553158
average: 0.6082474226804123
average: 0.6058221872541306
average: 0.6042154566744731
average: 0.6018590240123934
average: 0.6018447348193697
average: 0.5995423340961098
average: 0.5995457986373959
average: 0.6003005259203607
average: 0.6010439970171514
average: 0.5995558845299778
average: 0.5966201322556943
average: 0.5944566010211525
average: 0.5966690803765388
average: 0.599568655643422
Final result: 0.5997140814867763
Retrieval Frequencies: 155.44444444444446
WARNING 12-10 20:57:50 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-10 20:57:50 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-10 20:58:47 llm_engine.py:223] # GPU blocks: 1427, # CPU blocks: 327
INFO 12-10 20:58:50 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-10 20:58:54 model_runner.py:437] Graph capturing finished in 5 secs.
average: 1.0
average: 0.8181818181818182
average: 0.8571428571428571
average: 0.7741935483870968
average: 0.7804878048780488
average: 0.8235294117647058
average: 0.819672131147541
average: 0.8169014084507042
average: 0.8148148148148148
average: 0.8131868131868132
average: 0.8316831683168316
average: 0.8288288288288288
average: 0.8429752066115702
average: 0.8473282442748091
average: 0.8368794326241135
average: 0.8344370860927153
average: 0.8198757763975155
average: 0.7953216374269005
average: 0.7734806629834254
average: 0.7696335078534031
average: 0.7611940298507462
average: 0.7535545023696683
average: 0.7420814479638009
average: 0.7316017316017316
average: 0.7261410788381742
average: 0.7250996015936255
average: 0.7279693486590039
average: 0.7306273062730627
average: 0.7366548042704626
average: 0.7353951890034365
average: 0.7308970099667774
average: 0.7138263665594855
average: 0.6947040498442367
average: 0.6797583081570997
average: 0.6656891495601173
average: 0.6524216524216524
average: 0.6454293628808865
average: 0.6388140161725068
average: 0.6351706036745407
average: 0.6265984654731458
average: 0.6159600997506235
average: 0.6131386861313869
average: 0.6128266033254157
average: 0.6125290023201856
average: 0.6145124716553289
average: 0.6208425720620843
average: 0.6225596529284165
average: 0.6284501061571125
average: 0.632016632016632
average: 0.6313645621181263
average: 0.6307385229540918
average: 0.6340508806262231
average: 0.6372360844529751
average: 0.6421845574387948
average: 0.6432532347504621
average: 0.6460980036297641
average: 0.6470588235294118
average: 0.649737302977233
average: 0.6540447504302926
average: 0.6565143824027073
average: 0.6622296173044925
average: 0.6661211129296236
average: 0.6682769726247987
average: 0.6703645007923931
average: 0.6739469578783152
average: 0.6758832565284179
average: 0.6792738275340393
average: 0.6825633383010432
average: 0.6872246696035242
average: 0.6903039073806078
average: 0.6904422253922967
average: 0.6891701828410689
average: 0.6823855755894591
average: 0.6757865937072504
average: 0.6707152496626181
average: 0.6657789613848203
average: 0.6609724047306176
average: 0.6562905317769131
average: 0.6542893725992317
average: 0.6460176991150443
average: 0.6392009987515606
average: 0.6325524044389642
average: 0.6285018270401949
average: 0.6245487364620939
average: 0.6230677764565993
average: 0.6216216216216216
average: 0.6178861788617886
average: 0.6119402985074627
average: 0.6072644721906924
average: 0.6026936026936027
average: 0.6015538290788013
average: 0.5993413830954994
average: 0.5982627578718784
average: 0.5939849624060151
average: 0.589798087141339
average: 0.583596214511041
average: 0.5775234131113424
average: 0.572605561277034
average: 0.5718654434250765
average: 0.5731584258324924
average: 0.5774225774225774
average: 0.5806132542037586
average: 0.5837414299706171
average: 0.5868089233753637
average: 0.590778097982709
average: 0.5908658420551856
average: 0.5918944392082941
average: 0.5919701213818861
average: 0.5957446808510638
average: 0.5976168652612283
average: 0.59763851044505
average: 0.5994599459945995
average: 0.6030330062444246
average: 0.6038903625110522
average: 0.6064855390008764
average: 0.6081668114682884
average: 0.6098191214470284
average: 0.6114432109308283
average: 0.6104995766299746
average: 0.6095717884130982
average: 0.6103247293921732
average: 0.6102394715111478
average: 0.6093366093366094
average: 0.6076360682372055
average: 0.6083803384367445
average: 0.6075139888089528
average: 0.605868358445678
average: 0.6034618410700237
average: 0.6010928961748634
average: 0.5979860573199071
average: 0.5964642582628747
average: 0.5964912280701754
average: 0.5972747918243755
average: 0.5957926371149511
average: 0.5950782997762863
average: 0.5943745373797187
average: 0.5922116091109478
average: 0.5893508388037928
average: 0.5916002896451846
average: 0.5938173975557153
Final result: 0.5939957112223017
Retrieval Frequencies: 155.44444444444446
WARNING 12-10 21:54:41 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-10 21:54:41 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-10 21:54:48 llm_engine.py:223] # GPU blocks: 3743, # CPU blocks: 512
INFO 12-10 21:54:51 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-10 21:54:55 model_runner.py:437] Graph capturing finished in 4 secs.
average: 0.0
average: 0.5454545454545454
average: 0.6666666666666666
average: 0.6451612903225806
average: 0.6341463414634146
average: 0.6666666666666666
average: 0.7049180327868853
average: 0.7323943661971831
average: 0.7283950617283951
average: 0.7362637362637363
average: 0.7524752475247525
average: 0.7477477477477478
average: 0.768595041322314
average: 0.7786259541984732
average: 0.7730496453900709
average: 0.7682119205298014
average: 0.7639751552795031
average: 0.7543859649122807
average: 0.7458563535911602
average: 0.743455497382199
average: 0.7412935323383084
average: 0.7393364928909952
average: 0.7285067873303167
average: 0.7186147186147186
average: 0.7095435684647303
average: 0.7051792828685259
average: 0.7049808429118773
average: 0.7084870848708487
average: 0.7153024911032029
average: 0.7147766323024055
average: 0.7109634551495017
average: 0.6913183279742765
average: 0.67601246105919
average: 0.6646525679758308
average: 0.6598240469208211
average: 0.6438746438746439
average: 0.6343490304709142
average: 0.6172506738544474
average: 0.6115485564304461
average: 0.6035805626598465
average: 0.5985037406483791
average: 0.5936739659367397
average: 0.5890736342042755
average: 0.5916473317865429
average: 0.5941043083900227
average: 0.6008869179600886
average: 0.6030368763557483
average: 0.6093418259023354
average: 0.6133056133056133
average: 0.6211812627291242
average: 0.624750499001996
average: 0.6301369863013698
average: 0.6372360844529751
average: 0.6421845574387948
average: 0.6432532347504621
average: 0.647912885662432
average: 0.6524064171122995
average: 0.6567425569176882
average: 0.6592082616179001
average: 0.6632825719120136
average: 0.6688851913477537
average: 0.6710310965630114
average: 0.6747181964573269
average: 0.6767036450079239
average: 0.6786271450858035
average: 0.6804915514592934
average: 0.6838124054462935
average: 0.6870342771982116
average: 0.6901615271659325
average: 0.6903039073806078
average: 0.6918687589158345
average: 0.6919831223628692
average: 0.6851595006934813
average: 0.6785225718194254
average: 0.6707152496626181
average: 0.6657789613848203
average: 0.6622864651773982
average: 0.6549935149156939
average: 0.6530089628681178
average: 0.6472819216182049
average: 0.6404494382022472
average: 0.6337854500616523
average: 0.6321559074299634
average: 0.6293622141997594
average: 0.629013079667063
average: 0.6251468860164512
average: 0.6178861788617886
average: 0.6130884041331802
average: 0.6072644721906924
average: 0.6026936026936027
average: 0.5993340732519423
average: 0.5960482985729967
average: 0.5960912052117264
average: 0.5907626208378088
average: 0.5866099893730075
average: 0.580441640378549
average: 0.5744016649323621
average: 0.5695159629248198
average: 0.5677879714576962
average: 0.5721493440968718
average: 0.5734265734265734
average: 0.5756676557863502
average: 0.5778648383937316
average: 0.5790494665373423
average: 0.5821325648414986
average: 0.5851569933396765
average: 0.586239396795476
average: 0.5882352941176471
average: 0.5911193339500462
average: 0.5912007332722273
average: 0.592188919164396
average: 0.594959495949595
average: 0.5985727029438002
average: 0.6003536693191865
average: 0.6029798422436459
average: 0.6038227628149435
average: 0.6055124892334195
average: 0.6071733561058924
average: 0.607112616426757
average: 0.6062132661628883
average: 0.6053288925895087
average: 0.6052848885218828
average: 0.6044226044226044
average: 0.602761982128351
average: 0.6019339242546333
average: 0.6003197442046363
average: 0.5971451229183188
average: 0.5948072383949646
average: 0.5932864949258392
average: 0.5910147172734315
average: 0.5903151421983089
average: 0.5888634630053394
average: 0.5889477668433005
average: 0.5897821187077386
average: 0.5898583146905294
average: 0.5884529977794226
average: 0.585598824393828
average: 0.5827862873814734
average: 0.5850832729905865
average: 0.5880661394680087
Final result: 0.588277340957827
Retrieval Frequencies: 155.44444444444446
WARNING 12-10 22:47:49 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-10 22:47:49 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-10 22:48:02 llm_engine.py:223] # GPU blocks: 1427, # CPU blocks: 327
INFO 12-10 22:48:04 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-10 22:48:09 model_runner.py:437] Graph capturing finished in 5 secs.
average: 1.0
average: 0.8181818181818182
average: 0.9047619047619048
average: 0.8064516129032258
average: 0.8292682926829268
average: 0.8627450980392157
average: 0.8524590163934426
average: 0.8309859154929577
average: 0.8271604938271605
average: 0.8241758241758241
average: 0.8415841584158416
average: 0.8378378378378378
average: 0.8512396694214877
average: 0.8549618320610687
average: 0.8439716312056738
average: 0.8410596026490066
average: 0.8260869565217391
average: 0.8011695906432749
average: 0.7845303867403315
average: 0.7801047120418848
average: 0.7661691542288557
average: 0.7630331753554502
average: 0.751131221719457
average: 0.7359307359307359
average: 0.7302904564315352
average: 0.7290836653386454
average: 0.7318007662835249
average: 0.7343173431734318
average: 0.7402135231316725
average: 0.738831615120275
average: 0.7342192691029901
average: 0.7202572347266881
average: 0.7009345794392523
average: 0.6858006042296072
average: 0.6715542521994134
average: 0.6581196581196581
average: 0.6509695290858726
average: 0.6442048517520216
average: 0.6404199475065617
average: 0.629156010230179
average: 0.6209476309226932
average: 0.6155717761557178
average: 0.6152019002375297
average: 0.6148491879350348
average: 0.6167800453514739
average: 0.623059866962306
average: 0.6247288503253796
average: 0.6305732484076433
average: 0.6340956340956341
average: 0.6334012219959266
average: 0.6347305389221557
average: 0.639921722113503
average: 0.6429942418426103
average: 0.647834274952919
average: 0.6487985212569316
average: 0.6515426497277677
average: 0.6524064171122995
average: 0.6532399299474606
average: 0.657487091222031
average: 0.6598984771573604
average: 0.6655574043261231
average: 0.6677577741407529
average: 0.6682769726247987
average: 0.6687797147385103
average: 0.6708268330733229
average: 0.6728110599078341
average: 0.6762481089258698
average: 0.6795827123695977
average: 0.684287812041116
average: 0.6874095513748191
average: 0.6875891583452212
average: 0.6849507735583685
average: 0.6782246879334258
average: 0.6716826265389877
average: 0.6653171390013495
average: 0.6604527296937417
average: 0.6557161629434954
average: 0.6511024643320363
average: 0.645326504481434
average: 0.6371681415929203
average: 0.630461922596754
average: 0.623921085080148
average: 0.6199756394640682
average: 0.618531889290012
average: 0.6171224732461356
average: 0.6157461809635723
average: 0.6120789779326364
average: 0.6061997703788748
average: 0.601589103291714
average: 0.5970819304152637
average: 0.5948945615982242
average: 0.5927552140504939
average: 0.5917480998914224
average: 0.5864661654135338
average: 0.5823591923485654
average: 0.576235541535226
average: 0.5702393340270552
average: 0.5653964984552008
average: 0.564729867482161
average: 0.5660948536831484
average: 0.5704295704295704
average: 0.5736894164193868
average: 0.5768854064642507
average: 0.5800193986420951
average: 0.5840537944284342
average: 0.5842055185537584
average: 0.5852968897266729
average: 0.5854341736694678
average: 0.5892691951896393
average: 0.5902841429880843
average: 0.5894641235240691
average: 0.5913591359135913
average: 0.5950044603033007
average: 0.596816976127321
average: 0.5994741454864154
average: 0.6012163336229366
average: 0.6020671834625323
average: 0.6037574722459437
average: 0.6028789161727349
average: 0.6020151133501259
average: 0.601165695253955
average: 0.6011560693641619
average: 0.5995085995085995
average: 0.5962632006498781
average: 0.59709911361805
average: 0.5963229416466826
average: 0.5931800158604282
average: 0.5908733280881195
average: 0.5886026541764247
average: 0.5855925639039504
average: 0.5833973866256725
average: 0.5827612509534706
average: 0.5836487509462528
average: 0.5822689706987227
average: 0.580909768829232
average: 0.5795706883789785
average: 0.5775165319617928
average: 0.574033552151714
average: 0.5763939174511223
average: 0.5787203450754853
Final result: 0.5789849892780558
Retrieval Frequencies: 155.44444444444446
