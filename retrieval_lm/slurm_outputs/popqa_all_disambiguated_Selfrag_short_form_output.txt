==========================================
SLURM_JOB_ID = 52344
SLURM_NODELIST = gpunode02
==========================================
WARNING 12-23 03:26:33 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-23 03:26:33 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-23 03:27:06 llm_engine.py:223] # GPU blocks: 2806, # CPU blocks: 512
INFO 12-23 03:27:09 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-23 03:27:15 model_runner.py:437] Graph capturing finished in 5 secs.
average: 1.0
average: 0.7272727272727273
average: 0.8571428571428571
average: 0.8709677419354839
average: 0.8536585365853658
average: 0.8431372549019608
average: 0.8688524590163934
average: 0.8732394366197183
average: 0.8641975308641975
average: 0.8791208791208791
average: 0.8910891089108911
average: 0.8828828828828829
average: 0.8842975206611571
average: 0.8854961832061069
average: 0.8865248226950354
average: 0.8807947019867549
average: 0.8881987577639752
average: 0.8830409356725146
average: 0.8784530386740331
average: 0.8691099476439791
average: 0.8706467661691543
average: 0.8720379146919431
average: 0.8642533936651584
average: 0.8614718614718615
average: 0.8464730290456431
average: 0.8446215139442231
average: 0.842911877394636
average: 0.8487084870848709
average: 0.8505338078291815
average: 0.852233676975945
average: 0.8538205980066446
average: 0.8456591639871383
average: 0.8348909657320872
average: 0.8217522658610272
average: 0.8240469208211144
average: 0.8176638176638177
average: 0.8088642659279779
average: 0.8032345013477089
average: 0.800524934383202
average: 0.7979539641943734
average: 0.7930174563591023
average: 0.7931873479318735
average: 0.7885985748218527
average: 0.7888631090487239
average: 0.7913832199546486
average: 0.7937915742793792
average: 0.7982646420824295
average: 0.802547770700637
average: 0.8045738045738046
average: 0.8085539714867617
average: 0.810379241516966
average: 0.8140900195694716
average: 0.817658349328215
average: 0.8192090395480226
average: 0.8207024029574861
average: 0.822141560798548
average: 0.8253119429590018
average: 0.8248686514886164
average: 0.8261617900172117
average: 0.8274111675126904
average: 0.8302828618968386
average: 0.8330605564648118
average: 0.8357487922705314
average: 0.8367670364500792
average: 0.8361934477379095
average: 0.8356374807987711
average: 0.8381240544629349
average: 0.8390461997019374
average: 0.8414096916299559
average: 0.8408104196816208
average: 0.8430813124108416
average: 0.8438818565400844
average: 0.840499306518724
average: 0.8331053351573188
average: 0.8286099865047234
average: 0.8255659121171771
average: 0.823915900131406
average: 0.8261997405966277
average: 0.8258642765685019
average: 0.8242730720606827
average: 0.8239700374531835
average: 0.8236744759556104
average: 0.8246041412911084
average: 0.8231046931407943
average: 0.8240190249702735
average: 0.8213866039952996
average: 0.8164924506387921
average: 0.8140068886337543
average: 0.8093076049943246
average: 0.8069584736251403
average: 0.8057713651498335
average: 0.8035126234906695
average: 0.8002171552660152
average: 0.7948442534908701
average: 0.7906482465462275
average: 0.7833859095688749
average: 0.7762747138397502
average: 0.7693099897013388
average: 0.7655453618756372
average: 0.7658930373360242
average: 0.7662337662337663
average: 0.7675568743818002
average: 0.7678746327130265
average: 0.7691561590688651
average: 0.7704130643611912
average: 0.7716460513796385
average: 0.7709707822808671
average: 0.7703081232492998
average: 0.7724329324699353
average: 0.773602199816682
average: 0.7729336966394187
average: 0.774977497749775
average: 0.775200713648528
average: 0.7771883289124668
average: 0.7791411042944786
average: 0.7793223284100782
average: 0.7795004306632214
average: 0.7805294619982921
average: 0.7806943268416596
average: 0.7808564231738035
average: 0.7810158201498751
average: 0.781998348472337
average: 0.7805077805077805
average: 0.7806661251015434
average: 0.7816277195809831
average: 0.7809752198241406
average: 0.7795400475812847
average: 0.7789142407553108
average: 0.7782982045277127
average: 0.7784663051897753
average: 0.7786318216756342
average: 0.7795575896262396
average: 0.7797123391370174
average: 0.7798647633358378
average: 0.7815063385533184
average: 0.7809030347890451
average: 0.78104335047759
average: 0.7789934354485777
average: 0.78059377262853
average: 0.7821710999281093
Final result: 0.781987133666905
Retrieval Frequencies: 155.44444444444446
WARNING 12-23 03:53:56 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-23 03:53:56 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-23 03:54:06 llm_engine.py:223] # GPU blocks: 2806, # CPU blocks: 512
INFO 12-23 03:54:10 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-23 03:54:15 model_runner.py:437] Graph capturing finished in 5 secs.
average: 1.0
average: 0.7272727272727273
average: 0.8571428571428571
average: 0.8709677419354839
average: 0.8536585365853658
average: 0.8431372549019608
average: 0.8688524590163934
average: 0.8732394366197183
average: 0.8641975308641975
average: 0.8791208791208791
average: 0.8910891089108911
average: 0.8828828828828829
average: 0.8842975206611571
average: 0.8854961832061069
average: 0.8865248226950354
average: 0.8807947019867549
average: 0.8881987577639752
average: 0.8830409356725146
average: 0.8784530386740331
average: 0.8691099476439791
average: 0.8606965174129353
average: 0.8625592417061612
average: 0.8597285067873304
average: 0.8571428571428571
average: 0.8423236514522822
average: 0.8406374501992032
average: 0.842911877394636
average: 0.8487084870848709
average: 0.8505338078291815
average: 0.852233676975945
average: 0.8538205980066446
average: 0.8456591639871383
average: 0.8348909657320872
average: 0.8217522658610272
average: 0.8240469208211144
average: 0.8176638176638177
average: 0.8088642659279779
average: 0.8032345013477089
average: 0.800524934383202
average: 0.7979539641943734
average: 0.7930174563591023
average: 0.7907542579075426
average: 0.7838479809976246
average: 0.7842227378190255
average: 0.7868480725623582
average: 0.7893569844789357
average: 0.7939262472885033
average: 0.7983014861995754
average: 0.7983367983367984
average: 0.8024439918533605
average: 0.8043912175648703
average: 0.8082191780821918
average: 0.8119001919385797
average: 0.8135593220338984
average: 0.8151571164510166
average: 0.8185117967332124
average: 0.82174688057041
average: 0.8213660245183888
average: 0.8227194492254734
average: 0.8223350253807107
average: 0.8252911813643927
average: 0.8281505728314239
average: 0.8309178743961353
average: 0.8320126782884311
average: 0.8330733229329174
average: 0.8325652841781874
average: 0.8350983358547656
average: 0.834575260804769
average: 0.8370044052863436
average: 0.8364688856729378
average: 0.8388017118402282
average: 0.8410689170182841
average: 0.8363384188626907
average: 0.8290013679890561
average: 0.8245614035087719
average: 0.8215712383488681
average: 0.8199737187910644
average: 0.8223086900129701
average: 0.8233034571062741
average: 0.8230088495575221
average: 0.8214731585518102
average: 0.8212083847102343
average: 0.8221680876979294
average: 0.8219013237063778
average: 0.8228299643281808
average: 0.8202115158636898
average: 0.8153310104529616
average: 0.8117106773823192
average: 0.8070374574347332
average: 0.8024691358024691
average: 0.8013318534961155
average: 0.7991218441273326
average: 0.7969598262757872
average: 0.790547798066595
average: 0.7863974495217854
average: 0.7802313354363828
average: 0.7731529656607701
average: 0.7662203913491246
average: 0.764525993883792
average: 0.7648839556004037
average: 0.7652347652347652
average: 0.7665677546983185
average: 0.7678746327130265
average: 0.7691561590688651
average: 0.7713736791546589
average: 0.7725975261655567
average: 0.7737983034872762
average: 0.773109243697479
average: 0.7752081406105458
average: 0.7763519706691109
average: 0.7756584922797457
average: 0.7776777677767777
average: 0.7787689562890276
average: 0.7807250221043325
average: 0.782646801051709
average: 0.7827975673327541
average: 0.7838070628768303
average: 0.784799316823228
average: 0.7849280270956817
average: 0.7850545759865659
average: 0.7860116569525396
average: 0.786952931461602
average: 0.7854217854217854
average: 0.7855402112103981
average: 0.7864625302175665
average: 0.7857713828936851
average: 0.7850911974623315
average: 0.7844217151848938
average: 0.7837626854020296
average: 0.7838884585592564
average: 0.7847809377401999
average: 0.7856598016781083
average: 0.7865253595760787
average: 0.786626596543952
average: 0.7882177479492916
average: 0.7875647668393783
average: 0.7876561351947098
average: 0.7855579868708972
average: 0.7871107892831282
average: 0.7886412652767792
Final result: 0.7884203002144389
Retrieval Frequencies: 155.44444444444446
WARNING 12-23 04:21:05 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-23 04:21:05 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-23 04:21:14 llm_engine.py:223] # GPU blocks: 2806, # CPU blocks: 512
INFO 12-23 04:21:18 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-23 04:21:23 model_runner.py:437] Graph capturing finished in 5 secs.
average: 1.0
average: 0.7272727272727273
average: 0.8571428571428571
average: 0.8387096774193549
average: 0.8292682926829268
average: 0.8235294117647058
average: 0.8524590163934426
average: 0.8591549295774648
average: 0.8518518518518519
average: 0.8681318681318682
average: 0.8712871287128713
average: 0.8648648648648649
average: 0.8677685950413223
average: 0.8778625954198473
average: 0.8794326241134752
average: 0.8741721854304636
average: 0.8819875776397516
average: 0.8830409356725146
average: 0.8784530386740331
average: 0.8691099476439791
average: 0.8706467661691543
average: 0.8720379146919431
average: 0.8642533936651584
average: 0.8614718614718615
average: 0.8506224066390041
average: 0.848605577689243
average: 0.8467432950191571
average: 0.8523985239852399
average: 0.8540925266903915
average: 0.8556701030927835
average: 0.8571428571428571
average: 0.8488745980707395
average: 0.838006230529595
average: 0.824773413897281
average: 0.8269794721407625
average: 0.8205128205128205
average: 0.8116343490304709
average: 0.8059299191374663
average: 0.8031496062992126
average: 0.8005115089514067
average: 0.7955112219451371
average: 0.7956204379562044
average: 0.7909738717339667
average: 0.7911832946635731
average: 0.7936507936507936
average: 0.7960088691796009
average: 0.8004338394793926
average: 0.8046709129511678
average: 0.8066528066528067
average: 0.8105906313645621
average: 0.812375249500998
average: 0.8160469667318982
average: 0.8195777351247601
average: 0.8210922787193974
average: 0.822550831792976
average: 0.8239564428312159
average: 0.8270944741532977
average: 0.8266199649737302
average: 0.8278829604130808
average: 0.8291032148900169
average: 0.831946755407654
average: 0.8346972176759411
average: 0.8373590982286635
average: 0.838351822503962
average: 0.8377535101404057
average: 0.837173579109063
average: 0.8396369137670197
average: 0.8405365126676602
average: 0.8428781204111601
average: 0.8422575976845152
average: 0.8445078459343794
average: 0.8452883263009845
average: 0.841886269070735
average: 0.8344733242134063
average: 0.8299595141700404
average: 0.8268974700399467
average: 0.8252299605781866
average: 0.8274967574578469
average: 0.8271446862996159
average: 0.8255372945638433
average: 0.8252184769038702
average: 0.8249075215782984
average: 0.8258221680876979
average: 0.8243080625752106
average: 0.8252080856123662
average: 0.8225616921269095
average: 0.8176538908246226
average: 0.8151549942594719
average: 0.8104426787741204
average: 0.8080808080808081
average: 0.806881243063263
average: 0.8057080131723381
average: 0.8023887079261672
average: 0.7969924812030075
average: 0.793836344314559
average: 0.786540483701367
average: 0.7793964620187305
average: 0.772399588053553
average: 0.7686034658511722
average: 0.768920282542886
average: 0.7692307692307693
average: 0.7705242334322453
average: 0.7708129285014691
average: 0.7720659553831232
average: 0.7732949087415946
average: 0.774500475737393
average: 0.7737983034872762
average: 0.773109243697479
average: 0.7752081406105458
average: 0.7763519706691109
average: 0.7756584922797457
average: 0.7776777677767777
average: 0.7778768956289027
average: 0.7798408488063661
average: 0.7817703768624014
average: 0.7819287576020851
average: 0.7820844099913867
average: 0.7830913748932536
average: 0.7832345469940728
average: 0.783375314861461
average: 0.7835137385512073
average: 0.7844756399669695
average: 0.782964782964783
average: 0.7831031681559708
average: 0.7840451248992748
average: 0.7833733013589129
average: 0.781919111816019
average: 0.7812745869394178
average: 0.78064012490242
average: 0.7807900852052673
average: 0.7809377401998463
average: 0.7818459191456903
average: 0.7819833459500378
average: 0.7821187077385424
average: 0.7837434750186428
average: 0.7831236121391562
average: 0.7832476120499633
average: 0.7811816192560175
average: 0.782766111513396
average: 0.7843278217109992
Final result: 0.7841315225160829
Retrieval Frequencies: 155.44444444444446
WARNING 12-23 04:48:10 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-23 04:48:10 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-23 04:48:19 llm_engine.py:223] # GPU blocks: 2806, # CPU blocks: 512
INFO 12-23 04:48:22 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-23 04:48:27 model_runner.py:437] Graph capturing finished in 5 secs.
average: 1.0
average: 0.7272727272727273
average: 0.8571428571428571
average: 0.8387096774193549
average: 0.8292682926829268
average: 0.8235294117647058
average: 0.8524590163934426
average: 0.8591549295774648
average: 0.8518518518518519
average: 0.8681318681318682
average: 0.8712871287128713
average: 0.8648648648648649
average: 0.8677685950413223
average: 0.8702290076335878
average: 0.8723404255319149
average: 0.8675496688741722
average: 0.8757763975155279
average: 0.8771929824561403
average: 0.8729281767955801
average: 0.8638743455497382
average: 0.8606965174129353
average: 0.8625592417061612
average: 0.8597285067873304
average: 0.8571428571428571
average: 0.8506224066390041
average: 0.848605577689243
average: 0.8505747126436781
average: 0.8560885608856088
average: 0.8576512455516014
average: 0.8591065292096219
average: 0.8604651162790697
average: 0.8520900321543409
average: 0.8411214953271028
average: 0.8277945619335347
average: 0.8299120234604106
average: 0.8233618233618234
average: 0.814404432132964
average: 0.8086253369272237
average: 0.8057742782152231
average: 0.80306905370844
average: 0.7980049875311721
average: 0.7956204379562044
average: 0.7885985748218527
average: 0.7865429234338747
average: 0.7891156462585034
average: 0.7915742793791575
average: 0.7960954446854663
average: 0.8004246284501062
average: 0.8004158004158004
average: 0.8044806517311609
average: 0.8063872255489022
average: 0.8101761252446184
average: 0.8138195777351248
average: 0.815442561205273
average: 0.8170055452865065
average: 0.8203266787658802
average: 0.8235294117647058
average: 0.8231173380035026
average: 0.8244406196213425
average: 0.8240270727580372
average: 0.826955074875208
average: 0.8297872340425532
average: 0.8325281803542673
average: 0.8335974643423137
average: 0.8346333853354134
average: 0.8341013824884793
average: 0.8366111951588502
average: 0.8360655737704918
average: 0.8384728340675477
average: 0.8379160636758322
average: 0.8402282453637661
average: 0.8424753867791842
average: 0.8391123439667129
average: 0.8317373461012312
average: 0.8272604588394062
average: 0.8242343541944075
average: 0.8226018396846255
average: 0.8249027237354085
average: 0.8258642765685019
average: 0.8255372945638433
average: 0.8252184769038702
average: 0.8249075215782984
average: 0.8258221680876979
average: 0.825511432009627
average: 0.8263971462544589
average: 0.8237367802585194
average: 0.818815331010453
average: 0.8151549942594719
average: 0.8104426787741204
average: 0.8058361391694725
average: 0.804661487236404
average: 0.8035126234906695
average: 0.8013029315960912
average: 0.7948442534908701
average: 0.7917109458023379
average: 0.7854889589905363
average: 0.7783558792924038
average: 0.7713697219361483
average: 0.7696228338430173
average: 0.7699293642785066
average: 0.7702297702297702
average: 0.771513353115727
average: 0.7727717923604309
average: 0.7730358874878759
average: 0.7752161383285303
average: 0.7764034253092293
average: 0.7775683317624882
average: 0.776844070961718
average: 0.7789084181313598
average: 0.7800183318056828
average: 0.779291553133515
average: 0.7812781278127813
average: 0.7823371989295272
average: 0.784261715296198
average: 0.7861524978089395
average: 0.7862728062554301
average: 0.7872523686477175
average: 0.7882152006831767
average: 0.7883149872988993
average: 0.7884130982367759
average: 0.7893422148209825
average: 0.7902559867877786
average: 0.7886977886977887
average: 0.7887896019496344
average: 0.7896857373086221
average: 0.7889688249400479
average: 0.7882632831086439
average: 0.7875688434303698
average: 0.7868852459016393
average: 0.7869868319132456
average: 0.7878554957724827
average: 0.7887109077040427
average: 0.789553368660106
average: 0.7896318557475582
average: 0.7912005965697241
average: 0.7905255366395263
average: 0.7905951506245408
average: 0.788475565280817
average: 0.7900072411296162
average: 0.7915168943206327
Final result: 0.7912794853466762
Retrieval Frequencies: 155.44444444444446
WARNING 12-23 05:15:16 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-23 05:15:16 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-23 05:15:25 llm_engine.py:223] # GPU blocks: 2806, # CPU blocks: 512
INFO 12-23 05:15:28 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-23 05:15:33 model_runner.py:437] Graph capturing finished in 5 secs.
average: 1.0
average: 0.7272727272727273
average: 0.8571428571428571
average: 0.8709677419354839
average: 0.8536585365853658
average: 0.8431372549019608
average: 0.8688524590163934
average: 0.8732394366197183
average: 0.8641975308641975
average: 0.8791208791208791
average: 0.8811881188118812
average: 0.8648648648648649
average: 0.8677685950413223
average: 0.8702290076335878
average: 0.8723404255319149
average: 0.8543046357615894
average: 0.8509316770186336
average: 0.847953216374269
average: 0.8453038674033149
average: 0.8324607329842932
average: 0.8308457711442786
average: 0.8341232227488151
average: 0.8235294117647058
average: 0.8268398268398268
average: 0.8132780082987552
average: 0.8127490039840638
average: 0.8122605363984674
average: 0.8191881918819188
average: 0.8220640569395018
average: 0.8247422680412371
average: 0.8272425249169435
average: 0.8167202572347267
average: 0.8068535825545171
average: 0.7915407854984894
average: 0.7917888563049853
average: 0.7834757834757835
average: 0.775623268698061
average: 0.77088948787062
average: 0.7690288713910761
average: 0.7672634271099744
average: 0.7630922693266833
average: 0.7615571776155717
average: 0.7577197149643705
average: 0.7540603248259861
average: 0.7573696145124716
average: 0.7605321507760532
average: 0.7657266811279827
average: 0.7707006369426752
average: 0.7733887733887734
average: 0.7780040733197556
average: 0.780439121756487
average: 0.7827788649706457
average: 0.7869481765834933
average: 0.7890772128060264
average: 0.7911275415896488
average: 0.7931034482758621
average: 0.7967914438502673
average: 0.7968476357267951
average: 0.7986230636833046
average: 0.8003384094754653
average: 0.8036605657237936
average: 0.806873977086743
average: 0.8099838969404187
average: 0.8129952456418383
average: 0.8143525741029641
average: 0.8141321044546851
average: 0.8169440242057489
average: 0.8181818181818182
average: 0.8208516886930984
average: 0.8205499276410999
average: 0.8231098430813124
average: 0.8241912798874824
average: 0.8183079056865464
average: 0.8098495212038304
average: 0.805668016194332
average: 0.8029294274300932
average: 0.8015768725361366
average: 0.8041504539559015
average: 0.8040973111395646
average: 0.8027812895069533
average: 0.8027465667915106
average: 0.8027127003699137
average: 0.8038976857490865
average: 0.802647412755716
average: 0.8038049940546967
average: 0.8014101057579318
average: 0.7967479674796748
average: 0.7944890929965557
average: 0.7888762769580022
average: 0.7845117845117845
average: 0.7835738068812431
average: 0.7815587266739846
average: 0.7795874049945711
average: 0.7744360902255639
average: 0.7704569606801275
average: 0.7634069400630915
average: 0.7565036420395421
average: 0.7497425334706488
average: 0.746177370030581
average: 0.7467204843592331
average: 0.7472527472527473
average: 0.7487636003956478
average: 0.7492654260528894
average: 0.7507274490785645
average: 0.7521613832853026
average: 0.7516650808753568
average: 0.7511781338360037
average: 0.7516339869281046
average: 0.7539315448658649
average: 0.7543538038496792
average: 0.7538601271571299
average: 0.7560756075607561
average: 0.7564674397859055
average: 0.7586206896551724
average: 0.7598597721297108
average: 0.7602085143353605
average: 0.7605512489233419
average: 0.7617421007685738
average: 0.7620660457239627
average: 0.762384550797649
average: 0.761865112406328
average: 0.7630057803468208
average: 0.7616707616707616
average: 0.7619821283509342
average: 0.7630942788074134
average: 0.762589928057554
average: 0.7613005551149881
average: 0.7608182533438238
average: 0.760343481654957
average: 0.7598760650658405
average: 0.7609531129900077
average: 0.7620137299771167
average: 0.7623012869038607
average: 0.7625845229151015
average: 0.7643549589858315
average: 0.764618800888231
average: 0.7648787656135194
average: 0.762217359591539
average: 0.7639391745112237
average: 0.7656362329259525
Final result: 0.7655468191565404
Retrieval Frequencies: 155.44444444444446
WARNING 12-23 05:42:22 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-23 05:42:22 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-23 05:42:31 llm_engine.py:223] # GPU blocks: 2806, # CPU blocks: 512
INFO 12-23 05:42:34 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-23 05:42:39 model_runner.py:437] Graph capturing finished in 5 secs.
average: 1.0
average: 0.7272727272727273
average: 0.8571428571428571
average: 0.8709677419354839
average: 0.8536585365853658
average: 0.8431372549019608
average: 0.8688524590163934
average: 0.8591549295774648
average: 0.8518518518518519
average: 0.8681318681318682
average: 0.8514851485148515
average: 0.8378378378378378
average: 0.8429752066115702
average: 0.8473282442748091
average: 0.8439716312056738
average: 0.8211920529801324
average: 0.8136645962732919
average: 0.8011695906432749
average: 0.7955801104972375
average: 0.7905759162303665
average: 0.7860696517412935
average: 0.7867298578199052
average: 0.7782805429864253
average: 0.7748917748917749
average: 0.7634854771784232
average: 0.7649402390438247
average: 0.7701149425287356
average: 0.7712177121771218
average: 0.7722419928825622
average: 0.7731958762886598
average: 0.7740863787375415
average: 0.7684887459807074
average: 0.7601246105919003
average: 0.743202416918429
average: 0.7448680351906158
average: 0.7407407407407407
average: 0.7340720221606648
average: 0.7331536388140162
average: 0.7296587926509186
average: 0.7289002557544757
average: 0.7256857855361596
average: 0.7226277372262774
average: 0.7149643705463183
average: 0.7076566125290024
average: 0.7120181405895691
average: 0.7161862527716186
average: 0.7223427331887202
average: 0.7282377919320594
average: 0.7318087318087318
average: 0.7372708757637475
average: 0.7405189620758483
average: 0.7436399217221135
average: 0.7485604606525912
average: 0.751412429378531
average: 0.7523105360443623
average: 0.7549909255898367
average: 0.7593582887700535
average: 0.7600700525394045
average: 0.7624784853700516
average: 0.7648054145516074
average: 0.7687188019966722
average: 0.7725040916530278
average: 0.7761674718196457
average: 0.7781299524564184
average: 0.781591263650546
average: 0.7818740399385561
average: 0.7821482602118003
average: 0.7824143070044709
average: 0.7856093979441997
average: 0.7858176555716353
average: 0.7888730385164051
average: 0.790436005625879
average: 0.7864077669902912
average: 0.7783857729138167
average: 0.7746288798920378
average: 0.7723035952063915
average: 0.771353482260184
average: 0.7730220492866408
average: 0.7746478873239436
average: 0.7749683944374209
average: 0.7740324594257179
average: 0.7743526510480888
average: 0.7758830694275274
average: 0.7749699157641395
average: 0.7764565992865636
average: 0.7732079905992949
average: 0.7688734030197445
average: 0.7669345579793341
average: 0.7627695800227015
average: 0.7586980920314254
average: 0.758046614872364
average: 0.756311745334797
average: 0.754614549402823
average: 0.7497314715359829
average: 0.7460148777895855
average: 0.7392218717139852
average: 0.7325702393340271
average: 0.7260556127703398
average: 0.7227319062181448
average: 0.7235116044399597
average: 0.7242757242757243
average: 0.7250247279920871
average: 0.7238001958863859
average: 0.7245392822502424
average: 0.7262247838616714
average: 0.7269267364414843
average: 0.7285579641847314
average: 0.7282913165266106
average: 0.730804810360777
average: 0.7323556370302475
average: 0.7311534968210718
average: 0.7317731773177317
average: 0.7323818019625334
average: 0.7347480106100795
average: 0.7361963190184049
average: 0.735881841876629
average: 0.73557278208441
average: 0.7361229718189581
average: 0.7366638441998307
average: 0.7371956339210747
average: 0.7377185678601166
average: 0.7390586292320397
average: 0.7387387387387387
average: 0.7392363931762794
average: 0.7405318291700241
average: 0.7402078337330136
average: 0.739095955590801
average: 0.7387883556254917
average: 0.7377049180327869
average: 0.7381874515879163
average: 0.7394312067640276
average: 0.7414187643020596
average: 0.7426192278576835
average: 0.7430503380916604
average: 0.7449664429530202
average: 0.7446336047372317
average: 0.7450404114621602
average: 0.7432530999270606
average: 0.7443881245474294
average: 0.7455068296189792
Final result: 0.7455325232308792
Retrieval Frequencies: 155.44444444444446
WARNING 12-23 06:09:27 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-23 06:09:27 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-23 06:09:37 llm_engine.py:223] # GPU blocks: 2806, # CPU blocks: 512
INFO 12-23 06:09:40 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-23 06:09:45 model_runner.py:437] Graph capturing finished in 5 secs.
average: 1.0
average: 0.7272727272727273
average: 0.8571428571428571
average: 0.8387096774193549
average: 0.8292682926829268
average: 0.8235294117647058
average: 0.8524590163934426
average: 0.8591549295774648
average: 0.8518518518518519
average: 0.8681318681318682
average: 0.8712871287128713
average: 0.8558558558558559
average: 0.859504132231405
average: 0.8625954198473282
average: 0.8652482269503546
average: 0.8609271523178808
average: 0.8633540372670807
average: 0.8654970760233918
average: 0.861878453038674
average: 0.8534031413612565
average: 0.8507462686567164
average: 0.8530805687203792
average: 0.8416289592760181
average: 0.8398268398268398
average: 0.8298755186721992
average: 0.8286852589641435
average: 0.8314176245210728
average: 0.8376383763837638
average: 0.8398576512455516
average: 0.8419243986254296
average: 0.8438538205980066
average: 0.8360128617363344
average: 0.8255451713395638
average: 0.8066465256797583
average: 0.8035190615835777
average: 0.7977207977207977
average: 0.7894736842105263
average: 0.784366576819407
average: 0.7821522309711286
average: 0.7800511508951407
average: 0.7755610972568578
average: 0.7761557177615572
average: 0.7672209026128266
average: 0.7633410672853829
average: 0.7664399092970522
average: 0.7694013303769401
average: 0.7744034707158352
average: 0.7791932059447984
average: 0.7796257796257796
average: 0.7841140529531568
average: 0.7864271457085829
average: 0.7886497064579256
average: 0.7927063339731286
average: 0.7947269303201506
average: 0.7948243992606284
average: 0.7985480943738656
average: 0.8021390374331551
average: 0.8021015761821366
average: 0.8037865748709122
average: 0.805414551607445
average: 0.8086522462562395
average: 0.8117839607201309
average: 0.8148148148148148
average: 0.8161648177496038
average: 0.8190327613104524
average: 0.8202764976958525
average: 0.8199697428139183
average: 0.819672131147541
average: 0.8223201174743024
average: 0.8219971056439942
average: 0.8245363766048502
average: 0.8255977496483825
average: 0.8210818307905686
average: 0.8125854993160054
average: 0.8083670715249662
average: 0.8055925432756325
average: 0.8042049934296978
average: 0.8054474708171206
average: 0.8066581306017926
average: 0.8065739570164349
average: 0.8052434456928839
average: 0.8051787916152897
average: 0.8063337393422655
average: 0.8062575210589651
average: 0.807372175980975
average: 0.8037602820211516
average: 0.7990708478513356
average: 0.7967853042479908
average: 0.7922814982973894
average: 0.7878787878787878
average: 0.7869034406215316
average: 0.7848518111964874
average: 0.7828447339847991
average: 0.7776584317937701
average: 0.7736450584484591
average: 0.7676130389064143
average: 0.7606659729448492
average: 0.7538619979402678
average: 0.7522935779816514
average: 0.7537840565085772
average: 0.7542457542457542
average: 0.7556874381800198
average: 0.7571008814887366
average: 0.7584869059165859
average: 0.760806916426513
average: 0.7621313035204567
average: 0.763430725730443
average: 0.7637721755368814
average: 0.7659574468085106
average: 0.7671860678276811
average: 0.7665758401453224
average: 0.7677767776777678
average: 0.7689562890276539
average: 0.770999115826702
average: 0.7730061349693251
average: 0.7732406602953953
average: 0.7743324720068906
average: 0.7754056362083689
average: 0.7756138865368332
average: 0.7758186397984886
average: 0.7768526228143214
average: 0.7778695293146161
average: 0.7772317772317773
average: 0.7774167343623071
average: 0.7775987107171636
average: 0.7769784172661871
average: 0.7755749405233942
average: 0.7749803304484658
average: 0.7743950039032006
average: 0.774593338497289
average: 0.7755572636433513
average: 0.7765064836003052
average: 0.7774413323239969
average: 0.777610818933133
average: 0.7785234899328859
average: 0.7779422649888971
average: 0.778104335047759
average: 0.7760758570386579
average: 0.777697320782042
average: 0.7785765636232926
Final result: 0.7784131522516083
Retrieval Frequencies: 155.44444444444446
