==========================================
SLURM_JOB_ID = 54425
SLURM_NODELIST = gpunode03
==========================================
Token will not been saved to git credential helper. Pass `add_to_git_credential=True` if you want to set the git credential as well.
Token is valid (permission: fineGrained).
Your token has been saved to /home/<USER>/.cache/huggingface/token
Login successful
INFO 01-07 15:36:07 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-7b-hf', tokenizer='meta-llama/Llama-2-7b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-07 15:36:12 llm_engine.py:223] # GPU blocks: 2807, # CPU blocks: 512
INFO 01-07 15:36:14 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-07 15:36:18 model_runner.py:437] Graph capturing finished in 4 secs.
overall result: 0.4910650464617584
INFO 01-07 15:44:32 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-13b-hf', tokenizer='meta-llama/Llama-2-13b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-07 15:44:39 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 01-07 15:44:42 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-07 15:44:47 model_runner.py:437] Graph capturing finished in 5 secs.
overall result: 0.4874910650464618
INFO 01-07 15:59:29 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-7b-chat-hf', tokenizer='meta-llama/Llama-2-7b-chat-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-07 15:59:35 llm_engine.py:223] # GPU blocks: 2807, # CPU blocks: 512
INFO 01-07 15:59:37 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-07 15:59:41 model_runner.py:437] Graph capturing finished in 4 secs.
overall result: 0.8148677626876341
INFO 01-07 16:03:43 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-13b-chat-hf', tokenizer='meta-llama/Llama-2-13b-chat-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-07 16:03:50 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 01-07 16:03:53 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-07 16:03:57 model_runner.py:437] Graph capturing finished in 4 secs.
overall result: 0.7791279485346676
INFO 01-07 16:13:59 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-7b-hf', tokenizer='meta-llama/Llama-2-7b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-07 16:14:04 llm_engine.py:223] # GPU blocks: 2807, # CPU blocks: 512
INFO 01-07 16:14:06 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-07 16:14:11 model_runner.py:437] Graph capturing finished in 5 secs.
overall result: 0.3888491779842745
INFO 01-07 16:22:18 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-13b-hf', tokenizer='meta-llama/Llama-2-13b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-07 16:22:24 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 01-07 16:22:27 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-07 16:22:32 model_runner.py:437] Graph capturing finished in 5 secs.
overall result: 0.42887776983559683
INFO 01-07 16:36:55 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-7b-chat-hf', tokenizer='meta-llama/Llama-2-7b-chat-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-07 16:37:27 llm_engine.py:223] # GPU blocks: 2807, # CPU blocks: 512
INFO 01-07 16:37:30 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-07 16:37:34 model_runner.py:437] Graph capturing finished in 4 secs.
overall result: 0.6311651179413867
INFO 01-07 16:41:15 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-13b-chat-hf', tokenizer='meta-llama/Llama-2-13b-chat-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 01-07 16:42:08 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 01-07 16:42:11 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 01-07 16:42:16 model_runner.py:437] Graph capturing finished in 5 secs.
overall result: 0.6090064331665476
