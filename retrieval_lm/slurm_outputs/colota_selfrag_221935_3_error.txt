[rank0]: Traceback (most recent call last):
[rank0]:   File "run_short_form.py", line 371, in <module>
[rank0]:     main()
[rank0]:   File "run_short_form.py", line 302, in main
[rank0]:     model = LLM(model=gpt, download_dir=args.download_dir,
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/entrypoints/llm.py", line 177, in __init__
[rank0]:     self.llm_engine = LLMEngine.from_engine_args(
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/engine/llm_engine.py", line 573, in from_engine_args
[rank0]:     engine = cls(
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/engine/llm_engine.py", line 334, in __init__
[rank0]:     self.model_executor = executor_class(
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/executor/executor_base.py", line 47, in __init__
[rank0]:     self._init_executor()
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/executor/gpu_executor.py", line 40, in _init_executor
[rank0]:     self.driver_worker.load_model()
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/worker/worker.py", line 183, in load_model
[rank0]:     self.model_runner.load_model()
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/worker/model_runner.py", line 1058, in load_model
[rank0]:     self.model = get_model(model_config=self.model_config,
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/model_executor/model_loader/__init__.py", line 19, in get_model
[rank0]:     return loader.load_model(model_config=model_config,
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/model_executor/model_loader/loader.py", line 402, in load_model
[rank0]:     model.load_weights(self._get_all_weights(model_config, model))
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/model_executor/models/llama.py", line 582, in load_weights
[rank0]:     loader.load_weights(
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/model_executor/models/utils.py", line 203, in load_weights
[rank0]:     autoloaded_weights = list(self._load_module("", self.module, weights))
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/model_executor/models/utils.py", line 175, in _load_module
[rank0]:     for child_prefix, child_weights in self._groupby_prefix(weights):
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/model_executor/models/utils.py", line 104, in _groupby_prefix
[rank0]:     for prefix, group in itertools.groupby(weights_by_parts,
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/model_executor/models/utils.py", line 101, in <genexpr>
[rank0]:     weights_by_parts = ((weight_name.split(".", 1), weight_data)
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/model_executor/models/llama.py", line 582, in <genexpr>
[rank0]:     loader.load_weights(
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/model_executor/model_loader/loader.py", line 377, in _get_all_weights
[rank0]:     yield from self._get_weights_iterator(primary_weights)
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/model_executor/model_loader/loader.py", line 336, in _get_weights_iterator
[rank0]:     hf_folder, hf_weights_files, use_safetensors = self._prepare_weights(
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/model_executor/model_loader/loader.py", line 292, in _prepare_weights
[rank0]:     hf_folder = download_weights_from_hf(
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/model_executor/model_loader/weight_utils.py", line 246, in download_weights_from_hf
[rank0]:     with get_lock(model_name_or_path, cache_dir):
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/model_executor/model_loader/weight_utils.py", line 62, in get_lock
[rank0]:     os.makedirs(os.path.dirname(lock_dir), exist_ok=True)
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/os.py", line 223, in makedirs
[rank0]:     mkdir(name, mode)
[rank0]: FileNotFoundError: [Errno 2] No such file or directory: ''
