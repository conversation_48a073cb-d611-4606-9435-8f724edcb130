==========================================
SLURM_JOB_ID = 52201
SLURM_NODELIST = gpunode01
==========================================
WARNING 12-21 11:13:50 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-21 11:13:50 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-21 11:14:58 llm_engine.py:223] # GPU blocks: 1427, # CPU blocks: 327
INFO 12-21 11:15:01 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-21 11:15:06 model_runner.py:437] Graph capturing finished in 5 secs.
average: 1.0
average: 1.0
average: 0.9047619047619048
average: 0.8387096774193549
average: 0.7317073170731707
average: 0.7254901960784313
average: 0.7213114754098361
average: 0.7323943661971831
average: 0.7160493827160493
average: 0.7142857142857143
average: 0.7326732673267327
average: 0.7207207207207207
average: 0.7355371900826446
average: 0.7480916030534351
average: 0.7304964539007093
average: 0.7284768211920529
average: 0.7204968944099379
average: 0.7076023391812866
average: 0.6795580110497238
average: 0.6649214659685864
average: 0.6517412935323383
average: 0.6492890995260664
average: 0.6470588235294118
average: 0.645021645021645
average: 0.6307053941908713
average: 0.6334661354581673
average: 0.632183908045977
average: 0.6309963099630996
average: 0.6227758007117438
average: 0.6219931271477663
average: 0.6146179401993356
average: 0.6012861736334405
average: 0.5887850467289719
average: 0.5709969788519638
average: 0.5601173020527859
average: 0.5470085470085471
average: 0.5373961218836565
average: 0.5283018867924528
average: 0.5249343832020997
average: 0.5217391304347826
average: 0.5112219451371571
average: 0.5060827250608273
average: 0.501187648456057
average: 0.5034802784222738
average: 0.5079365079365079
average: 0.5144124168514412
average: 0.5162689804772235
average: 0.524416135881104
average: 0.5322245322245323
average: 0.5356415478615071
average: 0.5369261477045908
average: 0.538160469667319
average: 0.5393474088291746
average: 0.5461393596986818
average: 0.5452865064695009
average: 0.5499092558983666
average: 0.5543672014260249
average: 0.5586690017513135
average: 0.5628227194492255
average: 0.5651438240270727
average: 0.5723793677204659
average: 0.5777414075286416
average: 0.5829307568438004
average: 0.5863708399366085
average: 0.5912636505460218
average: 0.5944700460829493
average: 0.6006051437216339
average: 0.6035767511177347
average: 0.6079295154185022
average: 0.6121562952243126
average: 0.6134094151212554
average: 0.6118143459915611
average: 0.6061026352288488
average: 0.600547195622435
average: 0.5951417004048583
average: 0.5912117177097204
average: 0.5873850197109067
average: 0.5836575875486382
average: 0.5787451984635084
average: 0.5714285714285714
average: 0.5655430711610487
average: 0.55980271270037
average: 0.5554202192448234
average: 0.5523465703971119
average: 0.5517241379310345
average: 0.5487661574618097
average: 0.5458768873403019
average: 0.539609644087256
average: 0.5357548240635641
average: 0.531986531986532
average: 0.5305216426193119
average: 0.5290889132821076
average: 0.5276872964169381
average: 0.5241675617615468
average: 0.5207226354941552
average: 0.5152471083070452
average: 0.5098855359001041
average: 0.505664263645726
average: 0.5056065239551478
average: 0.508577194752775
average: 0.5134865134865135
average: 0.5153313550939663
average: 0.5181194906953966
average: 0.5208535402521823
average: 0.5235350624399616
average: 0.5261655566127498
average: 0.527803958529689
average: 0.5294117647058824
average: 0.5337650323774283
average: 0.534372135655362
average: 0.5340599455040872
average: 0.5364536453645364
average: 0.5388046387154326
average: 0.5393457117595049
average: 0.5425065731814198
average: 0.5447437011294527
average: 0.5460809646856158
average: 0.5465414175918019
average: 0.5444538526672311
average: 0.5424013434089001
average: 0.5412156536219817
average: 0.5392237819983484
average: 0.5372645372645373
average: 0.5353371242891958
average: 0.5342465753424658
average: 0.5323741007194245
average: 0.5313243457573354
average: 0.5295043273013376
average: 0.5269320843091335
average: 0.5251742835011619
average: 0.5234435049961568
average: 0.5240274599542334
average: 0.5246025738077215
average: 0.5236664162283997
average: 0.522744220730798
average: 0.5218356772760918
average: 0.5194709772226305
average: 0.5164113785557987
average: 0.5191889934829833
average: 0.5219266714593818
Final result: 0.5225160829163689
Retrieval Frequencies: 155.44444444444446
WARNING 12-21 12:00:40 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-21 12:00:40 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-21 12:00:53 llm_engine.py:223] # GPU blocks: 1427, # CPU blocks: 327
INFO 12-21 12:00:56 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-21 12:01:01 model_runner.py:437] Graph capturing finished in 5 secs.
average: 1.0
average: 0.9090909090909091
average: 0.8571428571428571
average: 0.7741935483870968
average: 0.7073170731707317
average: 0.7058823529411765
average: 0.6885245901639344
average: 0.676056338028169
average: 0.6666666666666666
average: 0.6703296703296703
average: 0.693069306930693
average: 0.6846846846846847
average: 0.7024793388429752
average: 0.7175572519083969
average: 0.7092198581560284
average: 0.7086092715231788
average: 0.7018633540372671
average: 0.6900584795321637
average: 0.6629834254143646
average: 0.6544502617801047
average: 0.6417910447761194
average: 0.6398104265402843
average: 0.6380090497737556
average: 0.6363636363636364
average: 0.6182572614107884
average: 0.6215139442231076
average: 0.6206896551724138
average: 0.6199261992619927
average: 0.6156583629893239
average: 0.6151202749140894
average: 0.6112956810631229
average: 0.5980707395498392
average: 0.5825545171339563
average: 0.5649546827794562
average: 0.5542521994134897
average: 0.5441595441595442
average: 0.5346260387811634
average: 0.522911051212938
average: 0.5196850393700787
average: 0.5115089514066496
average: 0.5012468827930174
average: 0.49878345498783455
average: 0.49643705463182897
average: 0.4965197215777262
average: 0.5011337868480725
average: 0.5099778270509978
average: 0.5119305856832972
average: 0.5201698513800425
average: 0.5280665280665281
average: 0.5315682281059063
average: 0.5329341317365269
average: 0.5342465753424658
average: 0.5355086372360844
average: 0.5423728813559322
average: 0.5415896487985212
average: 0.5462794918330308
average: 0.5490196078431373
average: 0.5516637478108581
average: 0.5559380378657487
average: 0.5583756345177665
average: 0.5657237936772047
average: 0.5695581014729951
average: 0.5748792270531401
average: 0.5784469096671949
average: 0.5834633385335414
average: 0.5867895545314901
average: 0.5930408472012103
average: 0.5976154992548435
average: 0.6020558002936858
average: 0.6063675832127352
average: 0.6077032810271041
average: 0.6047819971870605
average: 0.5991678224687933
average: 0.5937072503419972
average: 0.5883940620782726
average: 0.5845539280958721
average: 0.580814717477004
average: 0.5771725032425421
average: 0.5736235595390525
average: 0.5663716814159292
average: 0.5605493133583022
average: 0.5548705302096177
average: 0.5505481120584653
average: 0.5487364620938628
average: 0.5481569560047562
average: 0.5464159811985899
average: 0.5435540069686411
average: 0.5373134328358209
average: 0.5334846765039728
average: 0.5297418630751964
average: 0.5283018867924528
average: 0.5268935236004391
average: 0.5255157437567861
average: 0.5209452201933404
average: 0.5175345377258236
average: 0.5120925341745531
average: 0.5067637877211238
average: 0.5025746652935118
average: 0.5025484199796126
average: 0.5045408678102926
average: 0.5094905094905094
average: 0.5113748763600395
average: 0.5142017629774731
average: 0.5169738118331717
average: 0.5196926032660903
average: 0.5214081826831589
average: 0.5221489161168709
average: 0.5228758169934641
average: 0.5272895467160037
average: 0.5279560036663611
average: 0.5258855585831063
average: 0.5283528352835284
average: 0.5316681534344335
average: 0.5322723253757736
average: 0.5363716038562665
average: 0.5386620330147698
average: 0.5400516795865633
average: 0.5405636208368916
average: 0.5385266723116003
average: 0.5365239294710328
average: 0.5362198168193172
average: 0.5350949628406276
average: 0.5331695331695332
average: 0.5312753858651503
average: 0.5302175664786463
average: 0.5291766586730615
average: 0.528152260111023
average: 0.5263571990558615
average: 0.5238095238095238
average: 0.5213013168086754
average: 0.5196003074558032
average: 0.5194508009153318
average: 0.5200605601816806
average: 0.5184072126220887
average: 0.517524235645041
average: 0.5159141376757957
average: 0.5135929463629684
average: 0.5105762217359592
average: 0.5133960897900073
average: 0.516175413371675
Final result: 0.5167977126518942
Retrieval Frequencies: 155.44444444444446
WARNING 12-21 12:57:19 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-21 12:57:19 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-21 12:58:10 llm_engine.py:223] # GPU blocks: 1427, # CPU blocks: 327
INFO 12-21 12:58:12 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-21 12:58:17 model_runner.py:437] Graph capturing finished in 5 secs.
average: 1.0
average: 0.9090909090909091
average: 0.8571428571428571
average: 0.7741935483870968
average: 0.6829268292682927
average: 0.6862745098039216
average: 0.6721311475409836
average: 0.6619718309859155
average: 0.654320987654321
average: 0.6593406593406593
average: 0.693069306930693
average: 0.6936936936936937
average: 0.7107438016528925
average: 0.7251908396946565
average: 0.7163120567375887
average: 0.7152317880794702
average: 0.7080745341614907
average: 0.6900584795321637
average: 0.6685082872928176
average: 0.6596858638743456
average: 0.6467661691542289
average: 0.6445497630331753
average: 0.6380090497737556
average: 0.6277056277056277
average: 0.6099585062240664
average: 0.6135458167330677
average: 0.6130268199233716
average: 0.6125461254612546
average: 0.6120996441281139
average: 0.6151202749140894
average: 0.6112956810631229
average: 0.5980707395498392
average: 0.5825545171339563
average: 0.5649546827794562
average: 0.5542521994134897
average: 0.5441595441595442
average: 0.5346260387811634
average: 0.5202156334231806
average: 0.5144356955380578
average: 0.5063938618925832
average: 0.49625935162094764
average: 0.49391727493917276
average: 0.4916864608076009
average: 0.4918793503480278
average: 0.4988662131519274
average: 0.5077605321507761
average: 0.5097613882863341
average: 0.5180467091295117
average: 0.525987525987526
average: 0.5295315682281059
average: 0.530938123752495
average: 0.5342465753424658
average: 0.5393474088291746
average: 0.5480225988700564
average: 0.5452865064695009
average: 0.5499092558983666
average: 0.5543672014260249
average: 0.5569176882661997
average: 0.5611015490533563
average: 0.5651438240270727
average: 0.5723793677204659
average: 0.5761047463175123
average: 0.5813204508856683
average: 0.5847860538827259
average: 0.5881435257410297
average: 0.5913978494623656
average: 0.594553706505295
average: 0.5991058122205664
average: 0.604992657856094
average: 0.6107091172214182
average: 0.6119828815977175
average: 0.6104078762306611
average: 0.6047156726768377
average: 0.5991792065663475
average: 0.5924426450742241
average: 0.5885486018641811
average: 0.5847568988173456
average: 0.5810635538261998
average: 0.5761843790012804
average: 0.5689001264222503
average: 0.5630461922596754
average: 0.5573366214549939
average: 0.5529841656516443
average: 0.5523465703971119
average: 0.5517241379310345
average: 0.5499412455934195
average: 0.5447154471544715
average: 0.5384615384615384
average: 0.5346197502837684
average: 0.5297418630751964
average: 0.5283018867924528
average: 0.5268935236004391
average: 0.5244299674267101
average: 0.5198711063372717
average: 0.5164718384697131
average: 0.5110410094637224
average: 0.5057232049947971
average: 0.5015447991761071
average: 0.5025484199796126
average: 0.5045408678102926
average: 0.5074925074925075
average: 0.5093966369930761
average: 0.5122428991185113
average: 0.5150339476236664
average: 0.5177713736791547
average: 0.5185537583254044
average: 0.5202639019792649
average: 0.5210084033613446
average: 0.5254394079555966
average: 0.5261228230980751
average: 0.5231607629427792
average: 0.5256525652565257
average: 0.528099910793934
average: 0.5296198054818745
average: 0.5328659070990359
average: 0.5351867940920938
average: 0.5366063738156761
average: 0.53800170794193
average: 0.5351397121083827
average: 0.5331654072208228
average: 0.5320566194837635
average: 0.5309661436829067
average: 0.5290745290745291
average: 0.5264012997562957
average: 0.5261885576148267
average: 0.5243804956035172
average: 0.5226011102299762
average: 0.5208497246262785
average: 0.5183450429352069
average: 0.5158791634391944
average: 0.5142198308993082
average: 0.5141113653699466
average: 0.5147615442846328
average: 0.5131480090157776
average: 0.511558538404176
average: 0.5092524056254626
average: 0.5069801616458487
average: 0.5040116703136397
average: 0.5068790731354091
average: 0.509705248023005
Final result: 0.5103645461043602
Retrieval Frequencies: 155.44444444444446
