#!/bin/bash
#SBATCH --job-name=test_ret_base
#SBATCH --output=test_ret_base_%j.out
#SBATCH --error=test_ret_base_%j.err
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --partition=ampere
#SBATCH --nodes=1
#SBATCH --gpus-per-node=1

eval "$(conda shell.bash hook)"
conda activate selfrag

python passage_retrieval.py \
--model_name_or_path facebook/contriever-msmarco \
--passages /home/<USER>/atlas/dataset/corpora/wiki/enwiki-dec2020/text-list-100-sec.jsonl \
--passages_embeddings "wikipedia_embeddings_2020/*" \
--data /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambiguated_question_version_ready_for_contriever.json \
--n_docs 20 \
--output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/popqa_disambiguated_contrievermsm2020_results.jsonl


