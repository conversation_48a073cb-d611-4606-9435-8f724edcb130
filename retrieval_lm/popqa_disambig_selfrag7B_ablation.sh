#!/bin/bash
#SBATCH --job-name=popqa_all_disambiguated_selfrag7B_selfrag_13B_short_form
#SBATCH --output=slurm_outputs/popqa_all_disambiguated_Selfrag_short_form_output.txt
#SBATCH --error=slurm_outputs/popqa_all_disambiguated_selfrag_short_form_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=24:00:00
#SBATCH --partition=ampere  

# Standard configuration
python run_short_form.py \
--model_name selfrag/selfrag_llama2_7b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_all_disambiguated_SR7B_top5_results.jsonl \
--metric match --ndocs 5 --use_groundness --use_utility --use_seqscore \
--dtype half 

# Ablation: Minus seqscore
python run_short_form.py \
--model_name selfrag/selfrag_llama2_7b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_all_disambiguated_SR7B_minus_seqscore.jsonl \
--metric match --ndocs 5 --use_groundness --use_utility \
--dtype half 

# Ablation: Minus utility token
python run_short_form.py \
--model_name selfrag/selfrag_llama2_7b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_all_disambiguated_SR7B_minus_utility.jsonl \
--metric match --ndocs 5 --use_groundness --use_seqscore \
--dtype half 

# Ablation: Minus utility token + seqscore
python run_short_form.py \
--model_name selfrag/selfrag_llama2_7b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_all_disambiguated_SR7B_minus_utility_seqscore.jsonl \
--metric match --ndocs 5 --use_groundness \
--dtype half 

# Ablation: Minus grounding token
python run_short_form.py \
--model_name selfrag/selfrag_llama2_7b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_all_disambiguated_SR7B_minus_grounding.jsonl \
--metric match --ndocs 5 --use_utility --use_seqscore \
--dtype half 

# Ablation: Minus grounding token + seqscore
python run_short_form.py \
--model_name selfrag/selfrag_llama2_7b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_all_disambiguated_SR7B_minus_grounding_seqscore.jsonl \
--metric match --ndocs 5 --use_utility \
--dtype half 

# Ablation: Minus all (only relevance score)
python run_short_form.py \
--model_name selfrag/selfrag_llama2_7b \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_disambig_atlas_2020_wiki_preprocessed_contriever2020_retrieved_documents.json \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_all_disambiguated_SR7B_minus_all.jsonl \
--metric match --ndocs 5 \
--dtype half 
