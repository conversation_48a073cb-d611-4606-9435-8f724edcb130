#!/bin/bash
#SBATCH --job-name=popqa_baseline_author_provided_retrieve5_selfrag7_13B_short_form
#SBATCH --output=slurm_outputs/popqa_ablation_atlas2020_selfrag7_13B_short_form_output.txt
#SBATCH --error=slurm_outputs/popqa_ablation_atlas2020_selfrag7_13B_short_form_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=48:00:00
#SBATCH --partition=ampere  


# Run baseline: SR7B w gs retrieve 10 
python run_short_form.py \
--model_name selfrag/selfrag_llama2_7b \
--input_file /home/<USER>/self-rag-llama3/self-rag/eval_data_dup/popqa_longtail_w_gs.jsonl \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_original_author_provided_w_gs_top_10_selfrag7b_standard_settings.jsonl \
--metric match --ndocs 10 --use_groundness --use_utility --use_seqscore \
--dtype half


# Run baseline: SR13B w gs retrieve 10 
python run_short_form.py \
--model_name selfrag/selfrag_llama2_13b \
--input_file /home/<USER>/self-rag-llama3/self-rag/eval_data_dup/popqa_longtail_w_gs.jsonl \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/popqa/popqa_original_author_provided_w_gs_top_10_selfrag13b_standard_settings.jsonl \
--metric match --ndocs 10 --use_groundness --use_utility --use_seqscore \
--dtype half


