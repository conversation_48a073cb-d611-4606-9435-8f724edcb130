#!/bin/bash
#SBATCH --job-name=retriever     # Job name
#SBATCH --output=retriever_output.txt  # Output log file
#SBATCH --error=retriever_error.txt    # Error log file
#SBATCH --cpus-per-task=4              # Number of CPU cores per task
#SBATCH --mem=16G                      # Memory per node
#SBATCH --partition=ampere           # Partition name
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=8
#SBATCH --gpus-per-node=8
#SBATCH --cpus-per-task=8

export PATH="/home/<USER>/.conda/bin:$PATH"
eval "$(conda shell.bash hook)"
conda activate selfrag

# Run the retriever
python passage_retrieval.py \
    --model_name_or_path facebook/contriever-msmarco \
    --passages psgs_w100.tsv \
    --passages_embeddings "wikipedia_embeddings/*" \
    --query "What was <PERSON>'s job?" \
    --output_dir test_output.json \
    --n_docs 20