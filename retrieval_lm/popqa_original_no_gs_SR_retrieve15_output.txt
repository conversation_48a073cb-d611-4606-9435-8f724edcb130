==========================================
SLURM_JOB_ID = 52328
SLURM_NODELIST = gpunode03
==========================================
WARNING 12-23 00:18:09 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-23 00:18:09 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-23 00:18:53 llm_engine.py:223] # GPU blocks: 2806, # CPU blocks: 512
INFO 12-23 00:19:04 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-23 00:19:13 model_runner.py:437] Graph capturing finished in 9 secs.
average: 0.0
average: 0.5454545454545454
average: 0.6190476190476191
average: 0.6129032258064516
average: 0.5609756097560976
average: 0.5686274509803921
average: 0.5901639344262295
average: 0.6197183098591549
average: 0.6049382716049383
average: 0.6263736263736264
average: 0.6435643564356436
average: 0.6306306306306306
average: 0.6446280991735537
average: 0.6641221374045801
average: 0.6595744680851063
average: 0.6556291390728477
average: 0.6521739130434783
average: 0.6432748538011696
average: 0.6243093922651933
average: 0.6178010471204188
average: 0.6069651741293532
average: 0.6066350710900474
average: 0.6063348416289592
average: 0.6017316017316018
average: 0.5892116182572614
average: 0.5896414342629482
average: 0.5900383141762452
average: 0.5904059040590406
average: 0.594306049822064
average: 0.5945017182130584
average: 0.5946843853820598
average: 0.5755627009646302
average: 0.5638629283489096
average: 0.5498489425981873
average: 0.5454545454545454
average: 0.5327635327635327
average: 0.5235457063711911
average: 0.5121293800539084
average: 0.5091863517060368
average: 0.5038363171355499
average: 0.49625935162094764
average: 0.48905109489051096
average: 0.4845605700712589
average: 0.48491879350348027
average: 0.49206349206349204
average: 0.49889135254988914
average: 0.5032537960954447
average: 0.5116772823779193
average: 0.5197505197505198
average: 0.5295315682281059
average: 0.5349301397205589
average: 0.538160469667319
average: 0.54510556621881
average: 0.551789077212806
average: 0.5489833641404805
average: 0.5553539019963702
average: 0.5614973262032086
average: 0.5674255691768827
average: 0.5697074010327022
average: 0.5736040609137056
average: 0.5806988352745425
average: 0.5842880523731587
average: 0.5877616747181964
average: 0.589540412044374
average: 0.592823712948518
average: 0.5960061443932412
average: 0.6006051437216339
average: 0.6050670640834576
average: 0.6079295154185022
average: 0.6092619392185239
average: 0.6119828815977175
average: 0.6090014064697609
average: 0.6033287101248266
average: 0.5978112175102599
average: 0.5910931174089069
average: 0.5872170439414115
average: 0.5847568988173456
average: 0.5784695201037614
average: 0.5774647887323944
average: 0.5714285714285714
average: 0.5655430711610487
average: 0.55980271270037
average: 0.5566382460414129
average: 0.5571600481347774
average: 0.5564803804994055
average: 0.5534665099882491
average: 0.5470383275261324
average: 0.5407577497129736
average: 0.5357548240635641
average: 0.5308641975308642
average: 0.5283018867924528
average: 0.5257958287596048
average: 0.5255157437567861
average: 0.523093447905478
average: 0.5185972369819342
average: 0.5131440588853838
average: 0.5078043704474505
average: 0.5036045314109165
average: 0.5025484199796126
average: 0.5065590312815338
average: 0.5074925074925075
average: 0.5084075173095944
average: 0.5112634671890304
average: 0.513094083414161
average: 0.5148895292987512
average: 0.5176022835394862
average: 0.5193213949104618
average: 0.5210084033613446
average: 0.5245143385753932
average: 0.5252062328139322
average: 0.5258855585831063
average: 0.5292529252925292
average: 0.5325602140945584
average: 0.5340406719717065
average: 0.5363716038562665
average: 0.5386620330147698
average: 0.5426356589147286
average: 0.5431255337318531
average: 0.5410668924640135
average: 0.5398824517212426
average: 0.5403830141548709
average: 0.5392237819983484
average: 0.5389025389025389
average: 0.5361494719740049
average: 0.5358581788879936
average: 0.533972821742606
average: 0.5305313243457573
average: 0.5295043273013376
average: 0.5269320843091335
average: 0.5251742835011619
average: 0.5249807840122982
average: 0.5225019069412662
average: 0.522331566994701
average: 0.5221637866265966
average: 0.5205070842654735
average: 0.5181347150259067
average: 0.5150624540778839
average: 0.512764405543399
average: 0.5155684286748733
average: 0.5183321351545651
Final result: 0.5189421015010722
Retrieval Frequencies: 155.44444444444446
WARNING 12-23 00:50:46 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 12-23 00:50:46 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 12-23 00:51:49 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 12-23 00:51:52 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 12-23 00:52:10 model_runner.py:437] Graph capturing finished in 17 secs.
average: 1.0
average: 0.9090909090909091
average: 0.8571428571428571
average: 0.7741935483870968
average: 0.6829268292682927
average: 0.6862745098039216
average: 0.6721311475409836
average: 0.6619718309859155
average: 0.654320987654321
average: 0.6593406593406593
average: 0.693069306930693
average: 0.6846846846846847
average: 0.7024793388429752
average: 0.7175572519083969
average: 0.7092198581560284
average: 0.7086092715231788
average: 0.7018633540372671
average: 0.6900584795321637
average: 0.6685082872928176
average: 0.6596858638743456
average: 0.6467661691542289
average: 0.6445497630331753
average: 0.6425339366515838
average: 0.6363636363636364
average: 0.6182572614107884
average: 0.6215139442231076
average: 0.6206896551724138
average: 0.6199261992619927
average: 0.6192170818505338
average: 0.6185567010309279
average: 0.6146179401993356
average: 0.6012861736334405
average: 0.5887850467289719
average: 0.5709969788519638
average: 0.5601173020527859
average: 0.5498575498575499
average: 0.5401662049861495
average: 0.5283018867924528
average: 0.5223097112860893
average: 0.5140664961636828
average: 0.5037406483790524
average: 0.5012165450121655
average: 0.498812351543943
average: 0.4988399071925754
average: 0.5056689342403629
average: 0.5144124168514412
average: 0.5162689804772235
average: 0.524416135881104
average: 0.5322245322245323
average: 0.5356415478615071
average: 0.5409181636726547
average: 0.5420743639921722
average: 0.54510556621881
average: 0.5536723163841808
average: 0.5526802218114603
average: 0.5571687840290381
average: 0.5597147950089126
average: 0.5621716287215411
average: 0.5662650602409639
average: 0.5702199661590525
average: 0.5773710482529119
average: 0.5810147299509002
average: 0.5861513687600645
average: 0.589540412044374
average: 0.592823712948518
average: 0.5960061443932412
average: 0.5990922844175491
average: 0.6035767511177347
average: 0.6079295154185022
average: 0.6121562952243126
average: 0.6134094151212554
average: 0.6118143459915611
average: 0.6047156726768377
average: 0.5991792065663475
average: 0.5924426450742241
average: 0.5885486018641811
average: 0.5847568988173456
average: 0.5810635538261998
average: 0.5774647887323944
average: 0.5701643489254109
average: 0.5642946317103621
average: 0.5585696670776819
average: 0.5542021924482339
average: 0.5535499398315282
average: 0.5529131985731273
average: 0.5511163337250293
average: 0.5458768873403019
average: 0.539609644087256
average: 0.5357548240635641
average: 0.5308641975308642
average: 0.5294117647058824
average: 0.5279912184412733
average: 0.5255157437567861
average: 0.5209452201933404
average: 0.5175345377258236
average: 0.5120925341745531
average: 0.5067637877211238
average: 0.5025746652935118
average: 0.5025484199796126
average: 0.5045408678102926
average: 0.5074925074925075
average: 0.5093966369930761
average: 0.5122428991185113
average: 0.5150339476236664
average: 0.5177713736791547
average: 0.5185537583254044
average: 0.5202639019792649
average: 0.5210084033613446
average: 0.5254394079555966
average: 0.5261228230980751
average: 0.5240690281562216
average: 0.5265526552655265
average: 0.5289919714540589
average: 0.5305039787798409
average: 0.5337423312883436
average: 0.5360556038227628
average: 0.537467700258398
average: 0.53800170794193
average: 0.5359864521591872
average: 0.5340050377833753
average: 0.533721898417985
average: 0.532617671345995
average: 0.5307125307125307
average: 0.5280259951259139
average: 0.5269943593875906
average: 0.5251798561151079
average: 0.5241871530531325
average: 0.5224232887490166
average: 0.5199063231850117
average: 0.517428350116189
average: 0.5157571099154497
average: 0.5156369183829138
average: 0.5162755488266465
average: 0.5146506386175808
average: 0.5130499627143923
average: 0.5114729829755736
average: 0.5091844232182219
average: 0.5061998541210795
average: 0.5090514120202752
average: 0.511861969805895
Final result: 0.5125089349535382
Retrieval Frequencies: 155.44444444444446
