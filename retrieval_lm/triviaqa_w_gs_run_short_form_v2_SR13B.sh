#!/bin/bash
#SBATCH --job-name=triviaqa_w_gs_selfrag13B_short_form
#SBATCH --output=triviaqa_w_gs_selfrag13B_short_form_output.txt
#SBATCH --error=triviaqa_w_gs_selfrag13B_short_form_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=12:00:00
#SBATCH --partition=ampere

python run_short_form.py \
--model_name selfrag/selfrag_llama2_13b \
--input_file experiments/triviaqa/testing123.jsonl \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file experiments/triviaqa/triviaqa_selfrag13B_retrieval_w_gs.jsonl \
--metric match --ndocs 10 --use_groundness --use_utility --use_seqscore \
--dtype half \