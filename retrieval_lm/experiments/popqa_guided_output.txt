WARNING 07-19 10:46:15 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 07-19 10:46:15 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 07-19 10:47:34 llm_engine.py:223] # GPU blocks: 265, # CPU blocks: 327
INFO 07-19 10:47:36 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 07-19 10:47:43 model_runner.py:437] Graph capturing finished in 7 secs.
Model prediction: <PERSON>
Model prediction: ​
Model prediction: <PERSON><PERSON> (1922–2005) was a Tamil film producer, director, and actress.
Model prediction: Who produced
Model prediction: <PERSON> Hyams
Model prediction: ...are a type of magazine that focuses on the automobile industry.These magazines typically cover a wide range of topics, including automobile design, technology, and performance, as well as news and reviews of the latest cars
Model prediction: She is a poet.
Model prediction: - What athletic activity is b-ball?- What sport is hoops?- What sport is basketball?
Model prediction: Beth <PERSON>
Model prediction: with the Big Bang Theory.
