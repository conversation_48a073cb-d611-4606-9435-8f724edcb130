from vllm import LLM, SamplingParams
import pandas as pd

# Define paths and model parameters
model_name = "selfrag/selfrag_llama2_13b"
download_dir = "/home/<USER>/model_cache"
input_file = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/arc-challenge-test.parquet"  # Update with your path
output_file = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/arc-challenge-test_selfrag13B_noretrieval.csv"

# Load the model
model = LLM(model_name, download_dir=download_dir)

# Define sampling parameters
sampling_params = SamplingParams(temperature=0.0, top_p=1.0, max_tokens=100, skip_special_tokens=True)

# Placeholder for retrieval (replace this with actual retrieval logic)
def retrieve_passages(question):
    # Retrieve relevant content from a database or other source
    retrieved_paragraph = "This is the retrieved content."
    return retrieved_paragraph

# Function to format the input for the ARC-style prompt
def format_prompt(question, choices):
    formatted_choices = "\n".join([f"{label}. {choice}" for label, choice in zip(choices['label'], choices['text'])])
    prompt = (f"Given four answer candidates, A, B, C and D, choose the best answer choice. "
              f"Please answer with the capitalized alphabet only, without adding any extra phrase or period. "
              f"{question}\n"
              f"{formatted_choices}")
    return prompt

# Load the dataset (parquet file)
df = pd.read_parquet(input_file)

# Prepare to store results
results = []

# Iterate over the dataset and make predictions
for index, row in df.iterrows():
    question = row['question']
    choices = row['choices']  # Assuming this is a list or sequence of choices
    answer_key = row['answerKey']  # Assuming correct answer is in 'answerKey' column

    # Retrieve relevant passages for the question (optional)
    retrieved_content = retrieve_passages(question)
    
    # Format the prompt including the choices
    formatted_prompt = format_prompt(question, choices)
    
    # Generate predictions
    preds = model.generate([formatted_prompt], sampling_params)
    
    # Extract the prediction
    prediction = preds[0].outputs[0].text.strip()  # Stripping to ensure no leading/trailing whitespace
    
    # Store the result along with the original data
    results.append({
        "id": row['id'],
        "question": question,
        "choices": choices,
        "ground_truth": answer_key,
        "prediction": prediction,
        "retrieved_content": retrieved_content  # Store the retrieved content for debugging
    })

# Convert results to a DataFrame for easier analysis
results_df = pd.DataFrame(results)

# Save the results to a CSV file
results_df.to_csv(output_file, index=False)
