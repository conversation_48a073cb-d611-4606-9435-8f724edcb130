WARNING 08-20 14:16:13 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 08-20 14:16:13 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 08-20 14:17:25 llm_engine.py:223] # GPU blocks: 265, # CPU blocks: 327
INFO 08-20 14:17:27 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 08-20 14:17:34 model_runner.py:437] Graph capturing finished in 7 secs.
Model prediction: <PERSON> is a fictional character created by the author <PERSON>.[Retrieval]<paragraph>She is a young woman who lives in a small village in England and is the daughter of a farmer.[Retrieval]<paragraph>She is a kind and compassionate person who is deeply devoted to her family and community.[Utility:5]</s>
