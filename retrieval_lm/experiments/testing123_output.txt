WARNING 11-06 10:20:58 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 11-06 10:20:58 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 11-06 10:22:18 llm_engine.py:223] # GPU blocks: 1427, # CPU blocks: 327
INFO 11-06 10:22:21 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 11-06 10:22:25 model_runner.py:437] Graph capturing finished in 4 secs.
Model prediction: <PERSON> is a professor of English at the University of Georgia.[Utility:5]</s>
Model prediction: <PERSON> is a fictional character created by the author <PERSON>.[Retrieval]<paragraph>[Utility:5]</s>
Model prediction: <PERSON> <PERSON> is a psychologist and author.[Utility:5]</s>
Model prediction: <PERSON>k Bellizzi is a retired American professional basketball player.[Retrieval]<paragraph>He played in the NBA from 1987 to 1997.[Utility:5]</s>
Model prediction: Ole Krarup is a Danish politician and a member of the Folketing, the national parliament of Denmark.[Retrieval]<paragraph>He is a member of the Conservative Party and has been a member of the Folketing since 2001.[Utility:5]</s>
