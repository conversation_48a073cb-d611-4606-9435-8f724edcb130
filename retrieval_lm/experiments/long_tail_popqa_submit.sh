#!/bin/bash
#SBATCH --job-name=popqa_long_tail
#SBATCH --output=popqa_long_tail_output.txt
#SBATCH --error=popqa_long_tail_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=02:00:00
#SBATCH --partition=ampere 

# Activate conda environment
source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

# Run the Python script
python long_tail_popqa_script.py
