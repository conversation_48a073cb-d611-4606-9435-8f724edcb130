import json
import os

# --- Configuration ---
# This script assumes it is being run from:
# /home/<USER>/xion/selfrag_project/self-rag/retrieval_lm/experiments/colota/

# --- QA File Paths ---
QA_RETRIEVAL_RESULTS_FILE = 'colota_qa_retrieval_results.json'
# Path to the original dataset to get the answers
ORIGINAL_QA_DATA_FILE = '/home/<USER>/CoLoTa/CoLoTa_qa.json' 
# Desired output file name
QA_OUTPUT_FILE = 'colota_qa_selfrag_format.json'


# --- CV File Paths ---
CV_RETRIEVAL_RESULTS_FILE = 'colota_cv_retrieval_results.json'
# Path to the original dataset to get the answers
ORIGINAL_CV_DATA_FILE = '/home/<USER>/CoLoTa/CoLoTa_cv.json' 
# Desired output file name
CV_OUTPUT_FILE = 'colota_cv_selfrag_format.json'


def process_colota_file(retrieval_file, original_data_file, output_file):
    """
    Processes a retrieved results file and the original CoLoTa dataset
    to create a new JSON file in the Self-RAG format.
    """
    print(f"--- Processing: {retrieval_file} ---")

    # Check for required files
    if not os.path.exists(retrieval_file):
        print(f"!!! ERROR: Retrieval results file not found: '{retrieval_file}'")
        return
    if not os.path.exists(original_data_file):
        print(f"!!! ERROR: Original CoLoTa data file not found: '{original_data_file}'")
        return

    # 1. Load original CoLoTa data to create a question-to-answer mapping
    print(f"Loading original data from {original_data_file} to map answers...")
    question_to_answers = {}
    try:
        with open(original_data_file, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        for entry in original_data:
            query = entry.get('query')
            answer = entry.get('answer')
            if query is not None and answer is not None:
                # IMPORTANT: Convert boolean answer to a list of strings as required
                # e.g., True -> ["true"], False -> ["false"]
                question_to_answers[query] = [str(answer).lower()]
    except (json.JSONDecodeError, IOError) as e:
        print(f"Failed to load or parse original data file: {e}")
        return
    print(f"Created a mapping for {len(question_to_answers)} questions.")

    # 2. Load the Contriever retrieval results
    print(f"Loading retrieval results from {retrieval_file}...")
    try:
        with open(retrieval_file, 'r', encoding='utf-8') as f:
            retrieval_data = json.load(f)
    except (json.JSONDecodeError, IOError) as e:
        print(f"Failed to load or parse retrieval results file: {e}")
        return

    # 3. Transform the data into the Self-RAG format
    selfrag_data = []
    for item in retrieval_data:
        query = item.get('query')
        if not query:
            continue

        # Find the corresponding answer from our map
        if query in question_to_answers:
            selfrag_item = {
                'question': query,
                'answers': question_to_answers[query],
                'ctxs': item.get('ctxs', []) # Ensure 'ctxs' key exists
            }
            selfrag_data.append(selfrag_item)
        else:
            print(f"Warning: No matching answer found for query: '{query}'")

    # 4. Save the final formatted data to the output file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(selfrag_data, f, indent=2, ensure_ascii=False)
        print(f"✅ Successfully converted {len(selfrag_data)} items.")
        print(f"Self-RAG formatted file saved to: {output_file}\n")
    except IOError as e:
        print(f"Failed to write output file: {e}")

# --- Main Execution Block ---
if __name__ == "__main__":
    # Process the Question Answering files
    process_colota_file(
        retrieval_file=QA_RETRIEVAL_RESULTS_FILE,
        original_data_file=ORIGINAL_QA_DATA_FILE,
        output_file=QA_OUTPUT_FILE
    )

    # Process the Claim Verification files
    process_colota_file(
        retrieval_file=CV_RETRIEVAL_RESULTS_FILE,
        original_data_file=ORIGINAL_CV_DATA_FILE,
        output_file=CV_OUTPUT_FILE
    )

    print("All processing complete!")