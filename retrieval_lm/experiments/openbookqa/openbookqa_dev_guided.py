from vllm import LLM, SamplingParams

model = LLM("selfrag/selfrag_llama2_13b", download_dir="/home/<USER>/model_cache", dtype="half")
sampling_params = SamplingParams(temperature=0.0, top_p=1.0, max_tokens=100, skip_special_tokens=False)

def format_prompt(input, paragraph=None):
    prompt = "### Instruction:\n{0}\n\n### Response:\n".format(input)
    if paragraph is not None:
        prompt += "[Retrieval]<paragraph>{0}</paragraph>".format(paragraph)
    return prompt

query_1 = """
Instruction: You are provided with a question from the OpenBookQA Dev dataset. Provide a choice for D that exactly match the dataset.

Question: If the north is cold and the midnorth is warm, there will be

Choices:
A) snow
B) stars
C) air motion
D)
"""

query_2 = """
Instruction: You are provided with a question from the OpenBookQA Dev dataset. Provide a choice for D that exactly match the dataset.

Question: One step in creating an alternative fuel is

Choices:
A) coal miners looking for coal
B) gold being sifted from dirt
C) growing crops of corn
D)
"""

query_3 = """
Instruction: You are provided with a question from the OpenBookQA Dev dataset. Provide a choice for D that exactly match the dataset.

Question: What is the earth's surface made of?

Choices:
A) Grass
B) Asphalt
C) Water
D)
"""

query_4 = """
Instruction: You are provided with a question from the OpenBookQA Dev dataset. Provide a choice for D that exactly match the dataset.

Question: If you wanted to be outside in the daylight for the longest period when would be worst?

Choices:
A) June
B) January
C) April
D)
"""

query_5 = """
Instruction: You are provided with a question from the OpenBookQA Dev dataset. Provide a choice for D that exactly match the dataset.

Question: What does tearing do to a whole?

Choices:
A) it neutralizes the whole
B) it takes it apart
C) it enlarges the whole
D)
"""

query_6 = """
Instruction: You are provided with a question from the OpenBookQA Dev dataset. Provide a choice for D that exactly match the dataset.

Question: A man wants to create a room that he can light up with the flick of a switch. To do this, he sends some electricity into a completed electrical circuit, through a conductor, which allows

Choices:
A) light from the sun to shine
B) lights to be burned out
C) sunlight to shine through bulbs
D)
"""

query_7 = """
Instruction: You are provided with a question from the OpenBookQA Dev dataset. Provide a choice for D that exactly match the dataset.

Question: In which situation would an animal shed fur?

Choices:
A) It is angry
B) It is hot
C) It is cold
D)
"""

query_8 = """
Instruction: You are provided with a question from the OpenBookQA Dev dataset. Provide a choice for D that exactly match the dataset.

Question: When hurricanes move over land, the hurricane does what?

Choices:
A) speeds up a considerate amount
B) stops almost at once
C) gains a lot of strength
D)
"""

query_9 = """
Instruction: You are provided with a question from the OpenBookQA Dev dataset. Provide a choice for D that exactly match the dataset.

Question: Refraction can easily be witnessed in

Choices:
A) concrete walls
B) fake diamonds
C) Christmas ornaments
D)
"""

query_10 = """
Instruction: You are provided with a question from the OpenBookQA Dev dataset. Provide a choice for D that exactly match the dataset.

Question: Evaporation is the reason that

Choices:
A) crime is on the rise in the cities
B) a rain puddle is suddenly gone
C) the polar ice caps are spreading
D)
"""

queries = [query_1, query_2, query_3, query_4, query_5, query_6, query_7, query_8, query_9, query_10]

# for a query that doesn't require retrieval
preds = model.generate([format_prompt(query) for query in queries], sampling_params)
for pred in preds:
    print("Model prediction: {0}".format(pred.outputs[0].text))
