import pandas as pd
import json 
# This is prediction accuracy not retrieval success!!!!


def add_top5_pred_correct(csv_file_path, json_file_path, output_csv_path):
    """
    Processes a CSV file and JSON file to add a column "top5_pred_correct" to the CSV.
    
    Args:
        csv_file_path (str): Path to the input CSV file.
        json_file_path (str): Path to the input JSON file.
        output_csv_path (str): Path to save the output CSV with the new column.

    Returns:
        None
    """
    # Load the CSV file
    df = pd.read_csv(csv_file_path)

    #Load Json file
    with open(json_file_path, "r") as f:
        predictions  = json.load(f)["preds"]

    #Ensure Prediction list length equals number of row in CSV
    if len(predictions) != len(df):
        raise ValueError("The number of rows is not equal to the number of predictions")
    
    #Initialize a list to store correctness status
    pred_correctness = []

    # Iterate and check correctness
    for i, row in df.iterrows():
        possible_answers = eval(row["possible_answers"])
        current_predictions = predictions[i].lower()

        is_correct = any(answer.lower() in current_predictions for answer in possible_answers)
        pred_correctness.append(is_correct)
        

    # Append new column to dataframe 
    df["selfrag13b_original_retrieve15_author_provided_normal_setting"] = pred_correctness

    # Save the updated DataFrame with the correctness column
    df.to_csv(output_csv_path, index=False)
    print(f"Updated CSV with 'Correctness_Check' column saved to {output_csv_path}.")

csv_file_path = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_baseline_with_correctness.csv"
json_file_path = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_original_retrieve15_no_gs_SR13B_results.jsonl"
output_csv_path = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_baseline_with_correctness.csv"


# Call the function
add_top5_pred_correct(csv_file_path, json_file_path, output_csv_path)