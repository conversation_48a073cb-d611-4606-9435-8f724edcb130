from vllm import LLM, SamplingParams

model = LLM("selfrag/selfrag_llama2_13b", download_dir="/home/<USER>/model_cache", dtype="half")
sampling_params = SamplingParams(temperature=0.0, top_p=0.95, max_tokens=100, skip_special_tokens=False)

def format_prompt(input, paragraph=None):
    prompt = "### Instruction:\n{0}\n\n### Response:\n".format(input)
    if paragraph is not None:
        prompt += "[Retrieval]<paragraph>{0}</paragraph>".format(paragraph)
    return prompt

query_1 = "What is <PERSON><PERSON><PERSON>'s occupation?"
query_2 = "In what country is Tupper-Barnett House?"
query_3 = "What is <PERSON><PERSON>'s occupation?"
query_4 = "Who was the composer of 'Re<PERSON>ßt euch los, bedrä<PERSON>te <PERSON>, BWV 224'?"
query_5 = "What sport does Sebastián Morquio play?"
query_6 = "What sport does <PERSON><PERSON> play?"
query_7 = "Who was the producer of the silent German drama film 'Mother and Child'?"
query_8 = "Who is the author of 'Bones'?"
query_9 = "What is the religion of <PERSON><PERSON><PERSON>?"
query_10 = "In what country is Toronto Northwest?"

queries = [query_1, query_2, query_3, query_4, query_5, query_6, query_7, query_8, query_9, query_10]

# for a query that doesn't require retrieval
preds = model.generate([format_prompt(query) for query in queries], sampling_params)
for pred in preds:
    print("Model prediction: {0}".format(pred.outputs[0].text))
