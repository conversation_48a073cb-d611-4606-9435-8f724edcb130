import pandas as pd
import re

def clean_possible_answers(possible_answers_str):
    """
    Fully clean and normalize the `possible_answers` string.
    - Remove excessive quotes and escape characters.
    - Ensure the final format matches the desired output and is valid for ast.literal_eval.
    """
    if pd.isna(possible_answers_str):  # Handle NaN values
        return "[]"
    
    # Step 1: Remove extra quotes around elements
    cleaned_str = re.sub(r'"+', '"', possible_answers_str)  # Replace multiple quotes with a single quote

    # Step 2: Remove leading and trailing quotes
    cleaned_str = cleaned_str.strip('"')

    # Step 3: Replace any lingering issues with a valid JSON-like structure
    # Example: `[""""text"""", """"text""""]` -> `["text", "text"]`
    cleaned_str = re.sub(r'"\s*,\s*"', '","', cleaned_str)  # Normalize commas between elements
    cleaned_str = re.sub(r'(?<!\\)"{2,}', '"', cleaned_str)  # Reduce double quotes to single quotes around elements

    # Step 4: Escape inner quotes to make it compatible with literal_eval
    cleaned_str = re.sub(r'(?<!\\)"(.*?)"', r'"\1"', cleaned_str.replace('"', '\\"'))  # Escape double quotes inside

    # Ensure the string starts and ends with a proper bracket
    if not cleaned_str.startswith("["):
        cleaned_str = "[" + cleaned_str
    if not cleaned_str.endswith("]"):
        cleaned_str = cleaned_str + "]"
    
    return cleaned_str


# File paths
csv_file = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/full_version_popqa_monthly_page_views_cleaned2.csv"
cleaned_csv_file = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/full_version_popqa_monthly_page_views_cleaned3.csv"

# Load the CSV into a DataFrame
df = pd.read_csv(csv_file)

# Apply cleaning to the `possible_answers` column
df['possible_answers'] = df['possible_answers'].apply(clean_possible_answers)

# Save the cleaned DataFrame back to a CSV file
df.to_csv(cleaned_csv_file, index=False, encoding='utf-8')

print(f"Cleaned CSV file saved to {cleaned_csv_file}")
