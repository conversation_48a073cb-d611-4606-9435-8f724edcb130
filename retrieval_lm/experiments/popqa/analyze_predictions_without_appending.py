import pandas as pd
import json

def load_json_lines(file_path):
    data = []
    with open(file_path, "r") as f:
        for line in f:
            if line.strip():  # Skip empty lines
                data.append(json.loads(line))
    return data

def calculate_metrics(csv_file_path, json_file_path, correctness_column_name="correctness"):
    """
    Calculates the five metrics directly without appending correctness to the CSV.
    
    Args:
        csv_file_path (str): Path to the CSV file with entity data.
        json_file_path (str): Path to the JSON file with predictions.
        correctness_column_name (str): Temporary name for correctness column in memory.

    Returns:
        dict: A dictionary containing the five calculated metrics.
    """
    # Load the CSV file
    df = pd.read_csv(csv_file_path)

    # Load the JSON file
    predictions_data = load_json_lines(json_file_path)
    predictions = [item["output"] for item in predictions_data]  # Extract "preds" from each JSON object

    # Ensure prediction list length equals number of rows in CSV
    if len(predictions) != len(df):
        raise ValueError("The number of rows is not equal to the number of predictions")

    # Add correctness in memory without modifying the CSV
    df[correctness_column_name] = [
        any(answer.lower() in predictions[i].lower() for answer in eval(row["possible_answers"]))
        for i, row in df.iterrows()
    ]

    # Calculate metrics
    metrics = {}

    # Overall correctness
    overall_correct = df[correctness_column_name].mean()
    metrics["Overall Correctness"] = overall_correct

    # Completely unambiguous questions
    df_completely_unambiguous = df[df["entity_name_occurrences"] <= 1]
    unambiguous_correct = df_completely_unambiguous[correctness_column_name].mean()
    metrics["Unambiguous Correctness"] = unambiguous_correct

    # Name ambiguous questions
    df_name_ambiguous = df[df["entity_name_occurrences"] > 1]
    name_ambiguous_correct = df_name_ambiguous[correctness_column_name].mean()
    metrics["Name Ambiguous Correctness"] = name_ambiguous_correct

    # Relevant entity unambiguous questions
    df_relevant_entity_unambiguous = df[df["relevant_entity_count"] <= 1]
    relevant_unambiguous_correct = df_relevant_entity_unambiguous[correctness_column_name].mean()
    metrics["Relevant Entity Unambiguous Correctness"] = relevant_unambiguous_correct

    # Relevant entity ambiguous questions
    df_relevant_entity_ambiguous = df[df["relevant_entity_count"] > 1]
    relevant_ambiguous_correct = df_relevant_entity_ambiguous[correctness_column_name].mean()
    metrics["Relevant Entity Ambiguous Correctness"] = relevant_ambiguous_correct

    return metrics


def batch_process_metrics(csv_file_path, prediction_files):
    """
    Processes multiple prediction files against a single CSV file and computes metrics for each.

    Args:
        csv_file_path (str): Path to the CSV file with entity data.
        prediction_files (list): List of paths to JSON prediction files.

    Returns:
        dict: A dictionary where each key is a JSON file path, and the value is a dictionary of metrics.
    """
    results = {}

    for json_file in prediction_files:
        # Call the calculate_metrics function for each prediction file
        metrics = calculate_metrics(csv_file_path, json_file)

        # Store the metrics in the results dictionary with the file name as the key
        results[json_file] = metrics

    return results

csv_file_path = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_baseline_with_correctness.csv"
prediction_files = [
    # Vanilla Llama2 with retrieval (5 docs)
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_llama7b_retrieve5_author_provided.jsonl",
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_llama13b_retrieve5_author_provided.jsonl"
]


# Call batch_process_metrics to compute metrics for all prediction files
results = batch_process_metrics(csv_file_path, prediction_files)

# Print results
for json_file, metrics in results.items():
    print(f"Metrics for {json_file}:")
    for metric_name, value in metrics.items():
        print(f"  {metric_name}: {value:.2%}")
