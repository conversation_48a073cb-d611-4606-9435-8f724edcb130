import pandas as pd

def analyze_correctness(input_file, correctness_column, value):
    """
    Analyze the correctness of predictions for different categories of questions.
    
    Args:
        input_file (str): Path to the input CSV file.
        correctness_column (str): Name of the column indicating correctness (e.g., "Correctness_Check").
        
    Returns:
        None: Prints the analysis results.
    """
    # Load the CSV file
    df = pd.read_csv(input_file)

    # Total count of rows
    print(f"Total count: {len(df)}")

    # Overall correctness
    df_correct = df[df[correctness_column] == value]
    print(f"Overall correctness: {len(df_correct) / len(df):.2%}")

    # Completely unambiguous questions
    df_completely_unambiguous = df[df["entity_name_occurrences"] <= 1]
    print(f"Total number of completely unambiguous questions: {len(df_completely_unambiguous)}")
    df_correct_completely_unambiguous = df_completely_unambiguous[df_completely_unambiguous[correctness_column] == value]
    print(f"Correctness for completely unambiguous questions: {len(df_correct_completely_unambiguous) / len(df_completely_unambiguous):.2%}")

    # Name ambiguous questions
    df_name_ambiguous = df[df["entity_name_occurrences"] > 1]
    print(f"Total number of name ambiguous questions: {len(df_name_ambiguous)}")
    df_correct_name_ambiguous = df_name_ambiguous[df_name_ambiguous[correctness_column] == value]
    print(f"Correctness for name ambiguous questions: {len(df_correct_name_ambiguous) / len(df_name_ambiguous):.2%}")

    #Relevant Entity unabiguous questions
    df_relevant_entity_unambiguous= df[(df["relevant_entity_count"] <= 1)]
    print(f"Total number of relevant entitiy unambiguous questions are {len(df_relevant_entity_unambiguous)}")
    df_relevant_entity_unambiguous_success = df_relevant_entity_unambiguous[df_relevant_entity_unambiguous[correctness_column] == value]
    print(f"total number of relevant entity unambiguous sucessful is : {len(df_relevant_entity_unambiguous_success)/len(df_relevant_entity_unambiguous):.2%}")


    # Relevant entity ambiguous questions
    df_relevant_entity_ambiguous = df[df["relevant_entity_count"] > 1]
    print(f"Total number of relevant entity ambiguous questions: {len(df_relevant_entity_ambiguous)}")
    df_correct_relevant_entity_ambiguous = df_relevant_entity_ambiguous[df_relevant_entity_ambiguous[correctness_column] == value]
    print(f"Correctness for relevant entity ambiguous questions: {len(df_correct_relevant_entity_ambiguous) / len(df_relevant_entity_ambiguous):.2%}")

    # Add any further analysis as needed here



column = "selfrag7b_original_retrieve15_author_provided_normal_setting"
analyze_correctness("/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa/popqa_baseline_with_correctness.csv", column, True)
