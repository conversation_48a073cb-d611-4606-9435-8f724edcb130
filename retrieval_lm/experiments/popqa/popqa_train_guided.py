from vllm import LLM, SamplingParams

model = LLM("selfrag/selfrag_llama2_13b", download_dir="/home/<USER>/model_cache", dtype="half")
sampling_params = SamplingParams(temperature=0.7, top_p=0.95, max_tokens=50, skip_special_tokens=True)

def format_prompt(input, paragraph=None):
    prompt = "### Instruction:\n{0}\n\n### Response:\n".format(input)
    if paragraph is not None:
        prompt += "[Retrieval]<paragraph>{0}</paragraph>".format(paragraph)
    return prompt

query_1 = """
Instruction: You are provided with the first piece of an instance from the train split of the popQA dataset. Finish the second piece of the instance as exactly appeared in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: Who wrote the script

Label: Luciano Emmer

Second Piece:
"""

query_2 = """
Instruction: You are provided with the first piece of an instance from the train split of the popQA dataset. Finish the second piece of the instance as exactly appeared in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: What country is Central

Label: Philippines, Republic of the Philippines, PH, ph, the Philippines, Philippine Islands, PHL, RP, PHI, Pinas

Second Piece:
"""

query_3 = """
Instruction: You are provided with the first piece of an instance from the train split of the popQA dataset. Finish the second piece of the instance as exactly appeared in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: Who directed the work

Label: Suri Krishnamma

Second Piece:
"""

query_4 = """
Instruction: You are provided with the first piece of an instance from the train split of the popQA dataset. Finish the second piece of the instance as exactly appeared in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: Who produced

Label: Yann Tiersen, Yann Pierre Tiersen

Second Piece:
"""

query_5 = """
Instruction: You are provided with the first piece of an instance from the train split of the popQA dataset. Finish the second piece of the instance as exactly appeared in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: Who was in charge of writing

Label: Peter Hyams

Second Piece:
"""

query_6 = """
Instruction: You are provided with the first piece of an instance from the train split of the popQA dataset. Finish the second piece of the instance as exactly appeared in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: What genre is

Label: automobile magazine, car magazine

Second Piece:
"""

query_7 = """
Instruction: You are provided with the first piece of an instance from the train split of the popQA dataset. Finish the second piece of the instance as exactly appeared in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: What job does

Label: poet, poetess, bard

Second Piece:
"""

query_8 = """
Instruction: You are provided with the first piece of an instance from the train split of the popQA dataset. Finish the second piece of the instance as exactly appeared in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: What athletic activity

Label: basketball, hoops, b-ball, basket ball, BB, Basketball

Second Piece:
"""

query_9 = """
Instruction: You are provided with the first piece of an instance from the train split of the popQA dataset. Finish the second piece of the instance as exactly appeared in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: Who produced

Label: Beth Murphy

Second Piece:
"""

query_10 = """
Instruction: You are provided with the first piece of an instance from the train split of the popQA dataset. Finish the second piece of the instance as exactly appeared in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: Bob is associated

Label: sitcom, situational comedy, situation comedy

Second Piece:
"""

queries = [query_1, query_2, query_3, query_4, query_5, query_6, query_7, query_8, query_9, query_10]

# Generate predictions for the queries
preds = model.generate([format_prompt(query) for query in queries], sampling_params)
for pred in preds:
    print("Model prediction: {0}".format(pred.outputs[0].text))
