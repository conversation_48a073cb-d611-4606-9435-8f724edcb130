[{"text": "What is the occupation of the British individual, <PERSON> (1818-1875)?"}, {"text": "What was the occupation of the Honduran individual, <PERSON><PERSON> (1982-2014)?"}, {"text": "What is the occupation of <PERSON> (1852-1928), the Australian individual?"}, {"text": "What is the occupation of <PERSON> (1837-1910), the Canadian individual?"}, {"text": "What is the occupation of <PERSON> (born 1962), the American individual?"}, {"text": "What was the occupation of <PERSON>, the American individual (1877-1951)?"}, {"text": "What is the occupation of <PERSON> (1858-1937), the Canadian individual?"}, {"text": "What is the occupation of <PERSON><PERSON> (1912-1934), the American?"}, {"text": "What was the occupation of <PERSON> (1935-2017), the Danish individual?"}, {"text": "What was the occupation of <PERSON> (1867-1903)?"}, {"text": "What is the occupation of <PERSON><PERSON><PERSON><PERSON>, the Tunisian individual?"}, {"text": "What is the occupation of <PERSON> (1901-2018), the English individual?"}, {"text": "What is the occupation of <PERSON> (1926-1973), the American individual?"}, {"text": "What is the occupation of <PERSON><PERSON><PERSON> (born 1950), the individual from Senegal?"}, {"text": "What is the occupation of the Norwegian, <PERSON> (born 1939)?"}, {"text": "What is the occupation of <PERSON> (born 1961), the Panamanian?"}, {"text": "What is the occupation of <PERSON><PERSON><PERSON><PERSON><PERSON> (born 1950) from Iceland?"}, {"text": "What is the occupation of <PERSON> (1903-1974), the Australian?"}, {"text": "What is the occupation of <PERSON>, the Canadian individual?"}, {"text": "What was the occupation of <PERSON> (1924-1989), an American?"}, {"text": "What was the occupation of <PERSON> (1898-1966)?"}, {"text": "What is the occupation of <PERSON><PERSON> (1902-1994)?"}, {"text": "What is the occupation of <PERSON><PERSON><PERSON> (1554-1598), the Franco-Flemish individual?"}, {"text": "What is the occupation of <PERSON> (born 1945), from New Caledonia?"}, {"text": "What is the occupation of <PERSON><PERSON><PERSON> (born 1954), the Japanese individual?"}, {"text": "What was the occupation of <PERSON>, who lived from 1775-1854?"}, {"text": "What was the occupation of <PERSON> (1830-1888)?"}, {"text": "What is the occupation of <PERSON> (born 1968), the British individual?"}, {"text": "What is the occupation of <PERSON>, the Northern Irish individual?"}, {"text": "What was the occupation of <PERSON> (1870-1940)?"}, {"text": "What is the occupation of <PERSON> (born 1949), the American professional?"}, {"text": "What is the occupation of <PERSON> (born 1959), the Tanzanian individual?"}, {"text": "What is the occupation of <PERSON><PERSON><PERSON> (born 1957), the Japanese individual?"}, {"text": "What was the occupation of <PERSON> (1843-1923)?"}, {"text": "What is the occupation of <PERSON>, the Hungarian individual?"}, {"text": "What is the occupation of <PERSON><PERSON><PERSON><PERSON> (1907-1991)?"}, {"text": "What was the occupation of <PERSON> (1918-1965)?"}, {"text": "What was the occupation of <PERSON>, a 17th-century Englishman who died in 1661?"}, {"text": "What is the occupation of <PERSON><PERSON> (born 1941), the ethicist?"}, {"text": "What was the occupation of <PERSON> (1871-1951), the Irishman?"}, {"text": "What was the occupation of <PERSON><PERSON> (1855-1938), the Italian individual?"}, {"text": "What is the occupation of <PERSON><PERSON><PERSON> (1921-1977)?"}, {"text": "What was the occupation of <PERSON> (1794-1874)?"}, {"text": "What is the occupation of <PERSON> (1817-1892), the Irish individual?"}, {"text": "What is the occupation of <PERSON> (born 1990), the American individual?"}, {"text": "What was the occupation of <PERSON> (1887-1952)?"}, {"text": "What was the occupation of <PERSON> (1802-1872)?"}, {"text": "What is the occupation of <PERSON><PERSON> (1934-2024) from Norway?"}, {"text": "What was the occupation of <PERSON> (1935-2016), the Canadian?"}, {"text": "What is the occupation of <PERSON> (1743-1817), the British individual?"}, {"text": "What is the occupation of <PERSON> (born 1901), the American individual?"}, {"text": "What was the occupation of <PERSON> (1939-2013)?"}, {"text": "What was the occupation of <PERSON> (1805-1844)?"}, {"text": "What is the occupation of <PERSON><PERSON>, the American professional?"}, {"text": "What was the occupation of <PERSON><PERSON><PERSON>, the Czech individual (1884-1965)?"}, {"text": "What is the occupation of <PERSON> from Sri Lanka?"}, {"text": "What is the occupation of <PERSON> (1769-1841), the American?"}, {"text": "What is the occupation of <PERSON><PERSON>, the West Indian professional?"}, {"text": "What is the occupation of <PERSON><PERSON> (1892-1961), the Norwegian individual?"}, {"text": "What was the occupation of <PERSON>, the German individual (1883-1968)?"}, {"text": "What is the occupation of the Finnish individual, <PERSON> (1868-1939)?"}, {"text": "What is the occupation of <PERSON> (1828-1888), the American individual?"}, {"text": "What is the occupation of <PERSON> (1732-1799), the British individual?"}, {"text": "What was the occupation of <PERSON> (1371-1434), the Englishman?"}, {"text": "What is the occupation of <PERSON><PERSON><PERSON> (1924-1982), the Burmese individual?"}, {"text": "What was the occupation of <PERSON> (1917-2008)?"}, {"text": "What was the occupation of <PERSON><PERSON><PERSON> (1917-2006)?"}, {"text": "What is the occupation of <PERSON> (born 1955), the Ukrainian figure?"}, {"text": "What is the occupation of <PERSON>, the Italian individual (1732-1791)?"}, {"text": "What is the occupation of <PERSON><PERSON> (born 1947), the American individual?"}, {"text": "What is the occupation of <PERSON><PERSON><PERSON><PERSON> (born 1955), the individual from Cyprus?"}, {"text": "What was the occupation of <PERSON>, Jr. (1762-1826)?"}, {"text": "What is the occupation of <PERSON><PERSON>?"}, {"text": "What is the occupation of Marie-France <PERSON> (born 1940)?"}, {"text": "What was the occupation of <PERSON> (1673-1731), the German individual?"}, {"text": "What is the occupation of <PERSON> (1401-1561), the Englishman?"}, {"text": "What was the occupation of <PERSON><PERSON><PERSON> (1260-1285), a figure from the late thirteenth century?"}, {"text": "What is the occupation of <PERSON> (born 1941), the American?"}, {"text": "What is the occupation of <PERSON> (1822-1907), the Canadian individual?"}, {"text": "What is the occupation of <PERSON><PERSON><PERSON> (born 1936), the German professional?"}, {"text": "What is the occupation of <PERSON><PERSON> (born 1970)?"}, {"text": "What was the occupation of <PERSON> (1892-1974), the French individual?"}, {"text": "What is the occupation of <PERSON><PERSON> (born 1970), the Belizean?"}, {"text": "What is the occupation of <PERSON> (born 1947), the Uruguayan individual?"}, {"text": "What is the occupation of <PERSON><PERSON> (born 1972), the Mexican individual?"}, {"text": "What is the occupation of <PERSON> (1854-1935), the Canadian?"}, {"text": "What is the occupation of <PERSON> (born 1946), the Italian individual?"}, {"text": "What was the occupation of <PERSON> (1841-1916)?"}, {"text": "What was the occupation of <PERSON> (1901-1977), the American?"}, {"text": "What was the occupation of Frits Castricum (1947-2011) from the Netherlands?"}, {"text": "What is the occupation of <PERSON> (1937-2023), the Norwegian individual?"}, {"text": "What is the occupation of <PERSON> (born 1973)?"}, {"text": "What was the occupation of <PERSON> (1862-1947)?"}, {"text": "What is the occupation of <PERSON> (1907-1970)?"}, {"text": "What was the occupation of the Scottish individual, <PERSON> (1860-1917)?"}, {"text": "What was the occupation of <PERSON><PERSON> (1879-1925)?"}, {"text": "What is the occupation of <PERSON><PERSON> (1926-2003), the Hungarian-American?"}, {"text": "What is the occupation of <PERSON> (1882-1964), the French individual?"}, {"text": "What is the occupation of <PERSON><PERSON> <PERSON><PERSON>?"}, {"text": "What is the occupation of <PERSON> (1912-1980), the Canadian individual?"}, {"text": "What is the occupation of <PERSON> (1888-1974), the French scientist?"}, {"text": "What is the occupation of <PERSON><PERSON> (1948-2008), from Guinea?"}, {"text": "What is the occupation of <PERSON> (1820-1887), the American individual?"}, {"text": "What is the occupation of <PERSON>, the Irishman?"}, {"text": "What was the occupation of <PERSON> (1912-2003)?"}, {"text": "What is the occupation of <PERSON><PERSON><PERSON>, who is from Nepal?"}, {"text": "What was the occupation of <PERSON> (1894-1973)?"}, {"text": "What is the occupation of <PERSON> (1849-1921), the Spanish individual?"}, {"text": "What was the occupation of <PERSON><PERSON><PERSON><PERSON> (1936–2020)?"}, {"text": "What was the occupation of <PERSON> from England (1640-1660)?"}, {"text": "What is the occupation of <PERSON><PERSON><PERSON><PERSON><PERSON> (1796-1863)?"}, {"text": "What is the occupation of <PERSON> (1911-1988)?"}, {"text": "What was the occupation of <PERSON> (1900-1985), a British individual?"}, {"text": "What is the occupation of <PERSON> (1827-1909), the American individual?"}, {"text": "What is the occupation of Uruguayan, <PERSON> (born 1954)?"}, {"text": "What is the occupation of <PERSON> (born 1956)?"}, {"text": "What is the occupation of <PERSON><PERSON> (born 1978), the Czech lawyer?"}, {"text": "What is the occupation of <PERSON><PERSON> (born 1972), a Mexican national?"}, {"text": "What is the occupation of Sir <PERSON>, 3rd Baronet (1751-1782)?"}, {"text": "What is the occupation of <PERSON> (1824-1889), the British individual?"}, {"text": "What is the occupation of <PERSON> (1470-1516), the Englishman?"}, {"text": "In what city was the Angolan basketball player, <PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the American politician, <PERSON>, born?"}, {"text": "In what city was the Dutch musician, <PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the American baseball player, <PERSON> (1932-1996), born?"}, {"text": "In what city was the Italian artist <PERSON> (1536-1593) born?"}, {"text": "In what city was the German field hockey player and journalist, <PERSON>, born?"}, {"text": "In what city was the Indian politician, <PERSON><PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the Hungarian artist, <PERSON><PERSON> (1914–1986), born?"}, {"text": "In what city was the Russian mathematician, <PERSON>, born?"}, {"text": "In what city was the Indonesian footballer, <PERSON><PERSON>, born?"}, {"text": "In what city was the American baseball player <PERSON> born?"}, {"text": "In what city was the British orienteer <PERSON> born?"}, {"text": "In what city was the Italian footballer, <PERSON> (1919-1971), born?"}, {"text": "In what city was the American agronomist, <PERSON>, born?"}, {"text": "In what city was the road bicycle racer, <PERSON><PERSON><PERSON> (1907-1997), born?"}, {"text": "In what city was the Ukrainian diplomat, <PERSON><PERSON><PERSON><PERSON> (1930-2011), born?"}, {"text": "In what city was the Nigerian weightlifter <PERSON><PERSON> born?"}, {"text": "In what city was the English footballer, <PERSON> (born 1958), born?"}, {"text": "In what city was <PERSON>, the lawyer and former Lieutenant Governor of Wisconsin (1828-1885), born?"}, {"text": "In what city was the Argentine footballer, <PERSON>, born?"}, {"text": "In what city was the Argentine philosopher, <PERSON> (1930-2015), born?"}, {"text": "In what city was the Swedish orienteer, <PERSON><PERSON> born?"}, {"text": "In what city was the Belgian politician, <PERSON> (1943-2016), born?"}, {"text": "In what city was the Brazilian model, <PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the Major League Baseball player <PERSON> born?"}, {"text": "In what city was the Olympic ski jumper, <PERSON>, born?"}, {"text": "In what city was the English footballer, <PERSON> (1878-1962), born?"}, {"text": "In what city was the American composer, <PERSON> (1929-1998), born?"}, {"text": "In what city was the Russian association football player, <PERSON>, born?"}, {"text": "In what city was the American basketball player, <PERSON> (1941–2005), born?"}, {"text": "In what city was the American football player, <PERSON> (1894-1962), born?"}, {"text": "In what city was the Belgian painter of portraits, still lifes and interiors, <PERSON><PERSON><PERSON><PERSON> (1851-1938), born?"}, {"text": "In what city was the Northern Irish footballer, <PERSON>, born?"}, {"text": "In what city was the Dutch noble and writer, <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was the English footballer, <PERSON> (1891-1966), born?"}, {"text": "In what city was the former NASCAR driver <PERSON> born?"}, {"text": "In what city was the Polish sprinter <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was the Argentine field hockey player <PERSON> born?"}, {"text": "In what city was the American football player, <PERSON> (1879-1938), born?"}, {"text": "In what city was <PERSON>, the Apache Indian scout, born?"}, {"text": "In what city was the Scottish footballer <PERSON> (born 1907) born?"}, {"text": "In what city was the Italian sculptor, <PERSON>, born?"}, {"text": "In what city was the Argentine journalist <PERSON><PERSON> (1922-1991) born?"}, {"text": "In what city was the Norwegian alpine skier, <PERSON>, born?"}, {"text": "In what city was the Scottish footballer, <PERSON> (1948-2002), born?"}, {"text": "In what city was the Serbian footballer, <PERSON><PERSON>, born?"}, {"text": "In what city was the Italian footballer <PERSON> born?"}, {"text": "In what city was the Papua New Guinean sprinter <PERSON> born?"}, {"text": "In what city was the Scottish footballer, <PERSON> (born 1977), born?"}, {"text": "In what city was the Danish swimmer, <PERSON><PERSON>, born?"}, {"text": "In what city was the Dutch mayor, <PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was the German footballer <PERSON> born?"}, {"text": "In what city was the German historian, <PERSON> (1841-1903), born?"}, {"text": "In what city was the Namibian footballer, <PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the Flemish painter, <PERSON> (1663-1720/1), born?"}, {"text": "In what city was the Mexican footballer <PERSON> born?"}, {"text": "In what city was the Italian alpine skier <PERSON> born?"}, {"text": "In what city was the Mexican politician, <PERSON>, born?"}, {"text": "In what city was the German naval officer and Knight's Cross recipient, <PERSON><PERSON><PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the American politician, <PERSON> (1861-1927), born?"}, {"text": "In what city was the Venezuelan baseball player <PERSON><PERSON> born?"}, {"text": "In what city was the Australian sportsman, <PERSON> (1927-2013), born?"}, {"text": "In what city was the Polish handball player <PERSON> born?"}, {"text": "In what city was the United States general, <PERSON>, born?"}, {"text": "In what city was the Italian water polo player and coach, <PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the Russian footballer <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was the Canadian football player <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON><PERSON>, the former mayor of Montreal (1912-1914), born?"}, {"text": "In what city was the Catholic bishop <PERSON><PERSON> born?"}, {"text": "In what city was the American baseball player <PERSON> born?"}, {"text": "In what city was the American artist, <PERSON> (1839-1904), born?"}, {"text": "In what city was the amateur international cricketer, <PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the Montenegrin footballer <PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was the East German athlete, <PERSON> born?"}, {"text": "In what city was the American baseball player <PERSON> born?"}, {"text": "In what city was the Italian painter, <PERSON> (1586-1646), born?"}, {"text": "In what city was the British racing driver <PERSON> born?"}, {"text": "In what city was the American zoologist, <PERSON> (1872-1947), born?"}, {"text": "In what city was the athlete and judge from Ohio, United States, <PERSON> (born 1880) born?"}, {"text": "In what city was the Norwegian geologist, <PERSON>, born?"}, {"text": "In what city was the temperance worker, nurse, community leader, and writer <PERSON> born?"}, {"text": "In what city was the Mexican beach volleyball player, <PERSON>, born?"}, {"text": "In what city was the West Indian cricketer, <PERSON>, born?"}, {"text": "In what city was the English cricketer, <PERSON> (born 1974), born?"}, {"text": "In what city was the Spanish ski mountaineer, <PERSON><PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the canoe racer, <PERSON> born?"}, {"text": "In what city was the Russian language activist <PERSON><PERSON> born?"}, {"text": "In what city was the Serbian footballer <PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was the Japanese voice actress, <PERSON><PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the Austrian actress, <PERSON><PERSON>, born?"}, {"text": "In what city was the volleyball player <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was the writer <PERSON><PERSON> born?"}, {"text": "In what city was the Finnish historian, <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was the cricketer <PERSON> (1837-1914) born?"}, {"text": "In what city was the South Korean association football player, <PERSON>, born?"}, {"text": "In what city was the Mexican politician, <PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the Japanese skeleton racer, <PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the French scholar, physician and poet, <PERSON> (1617–1687), born?"}, {"text": "In what city was the American racing driver, <PERSON>, born?"}, {"text": "In what city was the British engraver, <PERSON>, born?"}, {"text": "In what city was the British musician, <PERSON><PERSON> (1904-1988), born?"}, {"text": "In what city was the Austrian swimmer, <PERSON>, born?"}, {"text": "In what city was the English cricketer, <PERSON> (born 1982), born?"}, {"text": "In what city was the Polish chess player <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was the English female hurdler, <PERSON><PERSON>, born?"}, {"text": "In what city was the footballer <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON>, the New Zealand mathematician, public servant, and university administrator, born?"}, {"text": "In what city was the Australian politician, <PERSON> born?"}, {"text": "In what city was the Lithuanian musician, <PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the footballer <PERSON><PERSON> born?"}, {"text": "In what city was the Gaelic football player <PERSON> born?"}, {"text": "In what city was the Swedish soldier, <PERSON> (1774-1809), born?"}, {"text": "In what city was the Irish hurler <PERSON> born?"}, {"text": "In what city was the Canadian Paralympic athlete, skier and swimmer <PERSON> born?"}, {"text": "In what city was the French sprinter <PERSON> born?"}, {"text": "In what city was the Japanese sailor <PERSON><PERSON> born?"}, {"text": "In what city was the athletics competitor <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was the Finnish playwright and scriptwriter <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON>, the US Navy officer (1913-1942), born?"}, {"text": "In what city was the Swiss artist, <PERSON> (1862-1936), born?"}, {"text": "In what city was General de División Médico and surgeon <PERSON> born?"}, {"text": "In what city was the Welsh footballer, <PERSON> (1899-?), born?"}, {"text": "In what city was the Argentine rugby union footballer, <PERSON>, born?"}, {"text": "In what city was the English footballer, <PERSON> (born 1956), born?"}, {"text": "In what city was the Chilean musician <PERSON> born?"}, {"text": "In what city was the American publisher and historian, <PERSON> (1819-1906), born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON>, the Hungarian film director (1933–2023), born?"}, {"text": "In what city was the Polish composer and pianist, <PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the bobsledder <PERSON> born?"}, {"text": "In what city was the Australian solicitor and politician, <PERSON> born?"}, {"text": "In what city was the Italian boxer, <PERSON>, born?"}, {"text": "In what city was the footballer <PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was the Canadian politician, <PERSON> (1841-1889), born?"}, {"text": "In what city was the Hungarian artist, <PERSON><PERSON><PERSON> (1901-1966), born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was the Czechoslovak triple jumper, <PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the Mexican boxer, <PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the German bookseller, <PERSON>, born?"}, {"text": "In what city was the Belgian herpetologist and botanical collector, <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was the Iranian musician <PERSON><PERSON> born?"}, {"text": "In what city was the Costa Rican politician, <PERSON> (1801-1856), born?"}, {"text": "In what city was the Swedish writer, <PERSON>, born?"}, {"text": "In what city was the Dutch actor <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was the ice sledge hockey player, <PERSON>, born?"}, {"text": "In what city was the baseball player <PERSON><PERSON> born?"}, {"text": "In what city was the English cricketer, <PERSON> (1843-1913), born?"}, {"text": "In what city was the botanist <PERSON> (1824-1880) born?"}, {"text": "In what city was the Spanish economist, <PERSON>, born?"}, {"text": "In what city was the Russian engineer and aviation pioneer, <PERSON><PERSON><PERSON> (1880-1936), born?"}, {"text": "In what city was the Italian painter <PERSON> (1845-1885) born?"}, {"text": "In what city was the Greek poet and diplomat, <PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the Japanese association football player, <PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the Swedish coxswain, <PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the American baseball player, <PERSON>, born?"}, {"text": "In what city was the French vaudevillist and song-writer, <PERSON><PERSON><PERSON> (1749-1832), born?"}, {"text": "In what city was the American politician, <PERSON>, born?"}, {"text": "In what city was the French chemist, <PERSON> (1825-1911), born?"}, {"text": "In what city was the Olympic wrestler, <PERSON><PERSON>, born?"}, {"text": "In what city was the American rower, <PERSON><PERSON>, born?"}, {"text": "In what city was the footballer <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was the Swedish ice hockey player, <PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was the artist and sculptor <PERSON><PERSON> born?"}, {"text": "In what city was the Irish hurler, <PERSON>, born?"}, {"text": "In what city was the Brazilian footballer, <PERSON>, born?"}, {"text": "In what city was the English cricketer, <PERSON> (1884-1947), born?"}, {"text": "In what city was the French MEP, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was the Austrian architect, <PERSON> (1607-1678), born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON><PERSON>, the Greek Roman Catholic bishop (1936–2023), born?"}, {"text": "In what city was the Olympic wrestler <PERSON><PERSON> born?"}, {"text": "In what city was the Chinese footballer <PERSON> born?"}, {"text": "In what city was the Olympic sailor, <PERSON><PERSON><PERSON>, born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> (1715-1789), the Roman Catholic archbishop, born?"}, {"text": "In what city was the Canadian actor <PERSON> born?"}, {"text": "What genre is \"Drive On,\" a single by Brother Beyond?"}, {"text": "What genre is \"Mother,\" the 1995 song by <PERSON> Sea?"}, {"text": "What genre is the television series Me and My Friend?"}, {"text": "What genre is Unknown, the 1988 anthology?"}, {"text": "What genre is \"Reach\", an album by <PERSON>?"}, {"text": "What genre is associated with the British painter, <PERSON>?"}, {"text": "What genre of music does <PERSON>, the musician and composer, produce?"}, {"text": "What genre is the 1982 video game, Operation Sabotage?"}, {"text": "What genre is The Gap, a 2006 film?"}, {"text": "What genre is Dark Matter, a book by <PERSON>?"}, {"text": "What genre is Chaotic, a book by <PERSON>?"}, {"text": "What genre is \"Flare\", an album from <PERSON><PERSON>?"}, {"text": "What genre is Brain Slaves, the rock band from New Zealand?"}, {"text": "What genre is The New World, a 2011 film directed by <PERSON><PERSON>?"}, {"text": "What genre is associated with the American artist <PERSON><PERSON><PERSON><PERSON><PERSON> (1888-1976)?"}, {"text": "What genre is Drill, the British band?"}, {"text": "What genre does the American musical group, <PERSON><PERSON>, belong to?"}, {"text": "What genre is Magic Music, a 2006 single by <PERSON><PERSON>?"}, {"text": "What genre is Voyage, the 2011 debut studio album by The Sound of Arrows?"}, {"text": "What genre is <PERSON>, the American musician?"}, {"text": "What genre is associated with the Chinese artist <PERSON><PERSON> (1763-1844)?"}, {"text": "What genre is the single, \"To Mother\"?"}, {"text": "What genre is Magic, the 2013 Indonesian television series?"}, {"text": "What genre is 'The Harrowing', a book by <PERSON>?"}, {"text": "What genre is \"Yellow\", a song by <PERSON><PERSON>?"}, {"text": "What genre is Hara, the sculpture by <PERSON>?"}, {"text": "What genre is the Doctor Who novella, Nightdreamers?"}, {"text": "What genre is The Song of the Suburbs, a 1941 film by <PERSON>?"}, {"text": "What genre is The Club, the 2010 baseball-themed program?"}, {"text": "What genre is Eddie & the Gang with No Name, a book series by <PERSON>?"}, {"text": "What genre is Koko ni Iruzee!?, a 2002 single by Morning Musume?"}, {"text": "What genre is \"Cut\", the 2014 extended play by <PERSON>?"}, {"text": "What genre is \"Stories\", an album by <PERSON><PERSON>?"}, {"text": "What genre is Most of Me?"}, {"text": "What genre is the 1952 film, I Lost My Heart in Heidelberg?"}, {"text": "What genre is \"VS\", a single by <PERSON><PERSON><PERSON>?"}, {"text": "What genre is Seven Veils, an album by <PERSON>?"}, {"text": "What genre is \"Bridge\", an album by Speed?"}, {"text": "What genre is the band Deivos?"}, {"text": "What genre is Martinez, the Swedish musical group?"}, {"text": "What genre is Chariot Race, the 1983 video game?"}, {"text": "What genre is \"Progression\", an album by <PERSON>?"}, {"text": "What genre is \"The Take\" by the UK musical group?"}, {"text": "What genre is \"Conversations\", an album by <PERSON>?"}, {"text": "What genre is Mars, the 1968 film by <PERSON>?"}, {"text": "What genre is Dimensions, the 1955 studio album by <PERSON>?"}, {"text": "What genre is associated with <PERSON><PERSON>, the British musician, rapper, and toaster (1957–2021)?"}, {"text": "What genre is The Angel, a 2007 short film directed by <PERSON>?"}, {"text": "What genre is \"Tempting Danger\", a novel by <PERSON>?"}, {"text": "What genre is \"I Will Be There\", the 1987 single by <PERSON>?"}, {"text": "What genre is Detour for Emmy, a book by <PERSON>?"}, {"text": "What genre is \"Drama\", the EP by <PERSON>?"}, {"text": "What genre is the American singer, <PERSON>?"}, {"text": "What genre is \"Gone\", the 2001 album by Entwine?"}, {"text": "What genre is Compass, the artwork by <PERSON>?"}, {"text": "What genre is the American disco group Apollo?"}, {"text": "What genre is <PERSON>, the harpist and composer, associated with?"}, {"text": "What genre is \"The Box\", a song by <PERSON> Missile?"}, {"text": "What genre is the book \"In Deep\" by <PERSON>?"}, {"text": "What genre is \"Fantasy\", a single by <PERSON>?"}, {"text": "What genre is \"Just a Matter of Time\", a novel by <PERSON>?"}, {"text": "What genre is Reminiscences, the 2010 Peruvian experimental film?"}, {"text": "What genre is \"My Way\", the ninth Cantonese studio album by Hong Kong solo artist <PERSON>?"}, {"text": "What genre is \"Our Time\", a single by <PERSON>?"}, {"text": "What genre is the television series, El honorable <PERSON>?"}, {"text": "What genre is Piel?"}, {"text": "What genre is Collaboration West, the 1953 album by <PERSON>?"}, {"text": "What genre is Thin Ice, the 2013 film?"}, {"text": "What genre is The Promoter, the 2013 film directed by <PERSON>?"}, {"text": "What genre is \"Shine\", a song by <PERSON> Sea?"}, {"text": "What genre is Zones, a book by <PERSON>?"}, {"text": "What genre is \"The Gift\", a song by The McCarters?"}, {"text": "What genre is 'Gene', a novel by <PERSON><PERSON>?"}, {"text": "What genre is \"Evil\", a song by <PERSON><PERSON><PERSON>?"}, {"text": "What genre is associated with <PERSON>, the painter from the Northern Netherlands (1608-1677)?"}, {"text": "What genre is \"Serving You\", an album by <PERSON>?"}, {"text": "What genre is Neighbours, the 1988 Camouflage song?"}, {"text": "What genre is \"In Silence\", a 1996 single by Luna Sea?"}, {"text": "What genre is A Winter of Cyclists, a 2013 film by <PERSON>?"}, {"text": "What genre is \"Back to Back,\" a single by <PERSON>?"}, {"text": "What genre is Strength, the Japanese punk band?"}, {"text": "What genre is \"All the Years\", a song performed by Chicago in 2008?"}, {"text": "What genre is \"Let It Go\", a song by <PERSON>?"}, {"text": "What genre is Drôles de zèbres, a 1977 film by <PERSON>?"}, {"text": "What genre is The <PERSON> Story?"}, {"text": "What genre is Betrayal, the 1932 British film directed by <PERSON>?"}, {"text": "What genre is the book \"Tempting The Gods: The Selected Stories of <PERSON><PERSON>, Volume 1\" by <PERSON><PERSON>?"}, {"text": "What genre is \"Let It Be You\", the 1989 single by <PERSON>?"}, {"text": "What genre is Scorpio, the 2004 single by TRAX?"}, {"text": "What genre is \"Right There\", the 2013 short film directed by <PERSON>?"}, {"text": "What genre is El usurpador, the television series?"}, {"text": "What genre is Fire, the 1990 novel by <PERSON>?"}, {"text": "What genre is The Moment, the 2012 LP from <PERSON>?"}, {"text": "What genre is \"Strangers\", a novel by <PERSON>?"}, {"text": "What genre is Info, the organization?"}, {"text": "What genre is Theatre, the South African band?"}, {"text": "What genre is Background, the 1973 film by <PERSON>?"}, {"text": "What genre does Node, the Italian musical group, play?"}, {"text": "What genre is \"In Deep\", a book by <PERSON>?"}, {"text": "What genre is \"If I Ever\", a song by Red Flag?"}, {"text": "What genre is \"More Love\", the 1988 single by <PERSON><PERSON>?"}, {"text": "What genre is The Remarkable Exploits of <PERSON><PERSON>gs, Spaceman, a book by <PERSON>?"}, {"text": "What genre is My Husband, the 1961 film by <PERSON>?"}, {"text": "What genre is the song \"West\"?"}, {"text": "What genre is \"It Sounds Like\", an album by deadmau5?"}, {"text": "What genre is The Other Man, a 1916 film?"}, {"text": "What genre is Wake Up, the 2012 single by ClariS?"}, {"text": "What genre is The Copper, a 1958 film by <PERSON><PERSON>?"}, {"text": "What genre is the book about astronomy, A Question and Answer Guide to Astronomy?"}, {"text": "What genre is Buono! 2, an album by Buono!?"}, {"text": "What genre is the juvenile novel, The Blue Aura?"}, {"text": "What genre is 'Heaven', the 1999 album by Cosmic Baby?"}, {"text": "What genre is Heist, the 2000 video game?"}, {"text": "Who is the father of the Norwegian politician, <PERSON> (1787-1852)?"}, {"text": "Who is the father of the American-bred Thoroughbred racehorse, <PERSON>?"}, {"text": "Who is the father of the Norwegian professor, geologist and docent, <PERSON>?"}, {"text": "Who is the father of <PERSON>, the Prince of Capua?"}, {"text": "Who is the father of the Chinese warlord, <PERSON>?"}, {"text": "Who is the father of <PERSON><PERSON><PERSON> of Spoleto, the Italian noble?"}, {"text": "Who is the father of the race horse, <PERSON><PERSON>?"}, {"text": "Who is the father of <PERSON><PERSON>, the Australian Thoroughbred racehorse?"}, {"text": "Who is the father of the Canadian-bred Thoroughbred racehorse, <PERSON><PERSON>?"}, {"text": "Who is the father of the journalist, <PERSON>?"}, {"text": "Who is the father of <PERSON><PERSON> mac <PERSON>, the King of Ailech?"}, {"text": "Who is the father of the American murderer, <PERSON>?"}, {"text": "Who is the father of <PERSON> who lived during 1250-1268?"}, {"text": "Who is the father of the Georgian prince, <PERSON><PERSON>?"}, {"text": "Who is the father of the American-bred Thoroughbred racehorse, But Why Not?"}, {"text": "Who is the father of the French-bred Thoroughbred racehorse, Match II?"}, {"text": "Who is the father of the Canadian politician, <PERSON> (1828–1911)?"}, {"text": "Who is the father of <PERSON>, the general of the Chinese Tang Dynasty?"}, {"text": "Who is the father of <PERSON>, the co-king of Georgia who died between 1435 and 1446?"}, {"text": "Who is the father of the American-bred Thoroughbred racehorse, Now What?"}, {"text": "Who is the father of the Italian painter, <PERSON> (1764-1835)?"}, {"text": "Who is the father of Sir <PERSON><PERSON>, 3rd Baronet, the Irish politician?"}, {"text": "Who is the father of the Swedish photographer, <PERSON><PERSON>?"}, {"text": "Who is the father of the German physicist, <PERSON>?"}, {"text": "Who is the father of <PERSON>, the abbot of Saint-Denis from the 9th century?"}, {"text": "Who is the father of the British engineer, <PERSON> (1802-1854)?"}, {"text": "Who is the father of the racehorse, <PERSON><PERSON>?"}, {"text": "In what country is Union State Bank, Wisconsin?"}, {"text": "In what country is Tina, the ghost town in Summers County, West Virginia?"}, {"text": "In what country is the human settlement, <PERSON><PERSON>?"}, {"text": "In what country is the village Sar Giz located?"}, {"text": "In what country is the village Pir <PERSON> located?"}, {"text": "In what country is Dell, a township in Benton County, Missouri?"}, {"text": "In what country is Bandrakottai, a village in Tamil Nadu?"}, {"text": "In what country is Fairview Outdoor School, an outdoor learning facility school located in Clear Spring, Maryland?"}, {"text": "In what country is Kılıçlı Kavlaklı, a village in the District of Emirdağ, Afyonkarahisar Province?"}, {"text": "In what country is Ago, a dissolved municipality in Shima district, Mie prefecture?"}, {"text": "In what country is Égligny, a commune in Seine-et-Marne?"}, {"text": "In what country is Bitchū-Kawamo Station, a railway station located in Takahashi, Okayama prefecture?"}, {"text": "In what country is Borysławice, a village in Łódź Voivodeship?"}, {"text": "In what country is the human settlement, Mato <PERSON>?"}, {"text": "In what country is Tartaczek, a village in Masovian?"}, {"text": "In what country is the village Jelow Girangeh located?"}, {"text": "In what country is the village Ahmadabad-e Razavi located?"}, {"text": "In what country is Freedom, the unincorporated community in Utah located?"}, {"text": "In what country is Ciepień, a village in Kuyavian-Pomeranian?"}, {"text": "In what country is Blenheim, a human settlement in Gloucester Township, New Jersey?"}, {"text": "In what country is Mary, a commune in Saône-et-Loire?"}, {"text": "In what country is the rural gmina, Lubsza?"}, {"text": "In what country is Tsutsui Station, a railway station in Aomori, Aomori Prefecture?"}, {"text": "In what country is Edmundston, a provincial electoral district in New Brunswick?"}, {"text": "In what country is the village of Rahzan located?"}, {"text": "In what country is the village of Kozići?"}, {"text": "In what country is Valdearcos de la Vega, a municipality?"}, {"text": "In what country is the river known as Ciucurul Orbului located?"}, {"text": "In what country is Gaustadalléen, the tram stop?"}, {"text": "In what country is the village Poręba-Kocęby located?"}, {"text": "In what country is the village Dubicze Osoczne located?"}, {"text": "In what country is Joys, the steamboat that sank in Lake Michigan?"}, {"text": "In what country is the human settlement, Laxmipur, Mahakali located?"}, {"text": "In what country is Wiesau, a location known for its river?"}, {"text": "In what country is Lewałd Wielki, a village in the Warmian-Masurian region?"}, {"text": "In what country is Kamioka Station, a railway station in Saiki, Oita prefecture?"}, {"text": "In what country is Quebec Route 213, a highway located in Quebec?"}, {"text": "In what country is Al-Fajr Arabsalim, the association football club?"}, {"text": "In what country is the village of Dąbkowice, located in Łódź Voivodeship?"}, {"text": "In what country is Borzymy, Kolno County, a village in Podlaskie?"}, {"text": "In what country is Fontenay, the former commune in Manche?"}, {"text": "In what country is Valea Seacă River, a tributary of the Valea Podului River?"}, {"text": "In what country is Punghina, a commune in Mehedinți County?"}, {"text": "In what country is the village Ormak, located in Isfahan?"}, {"text": "In what country is Vera, a human settlement in Appomattox County, Virginia?"}, {"text": "In what country is Kuczynka, a village in Greater Poland?"}, {"text": "In what country is West Wyomissing, the census designated place in Berks County, Pennsylvania?"}, {"text": "In what country is Tigra, a village in Haryana?"}, {"text": "In what country is Jauldes, a commune in Charente?"}, {"text": "In what country is the village Nowa Wieś Reszelska located?"}, {"text": "In what country is Colonia Nueva Coneta, a municipality and village in Catamarca Province?"}, {"text": "In what country is Aminabad, a village in Kermanshah?"}, {"text": "In what country is Tholuvankadu, a village in Tamil Nadu?"}, {"text": "In what country is Anaikudam, a village in Tamil Nadu?"}, {"text": "In what country is the learned society, Society of Early Americanists?"}, {"text": "In what country is Denmark Hill Insect Bed?"}, {"text": "In what country is Mavjinjava, a village in Gujarat?"}, {"text": "In what country is Arthur, the ghost town in Elko County, Nevada?"}, {"text": "In what country is the river, Pârâul Bogat?"}, {"text": "In what country is the human settlement, Têbo?"}, {"text": "In what country is Sholoktu, a human settlement?"}, {"text": "In what country is the village of Goldasht, Sistan and Baluchestan located?"}, {"text": "In what country is the village, Eslamabad-e Mashayekh?"}, {"text": "In what country is Kalu, a village in East Azerbaijan?"}, {"text": "In what country is Pierce, a civil town in Kewaunee County, Wisconsin?"}, {"text": "In what country is the body of water known as Chalhuacocha?"}, {"text": "In what country is Footes, a human settlement in New York?"}, {"text": "In what country is Oscar, an unincorporated community in Greenbrier County, West Virginia?"}, {"text": "In what country is Cora, an unincorporated community in Logan County, West Virginia?"}, {"text": "In what country is Madan <PERSON>, a village development committee in the Bagmati Zone?"}, {"text": "In what country is Oborín, a municipality?"}, {"text": "In what country is Ježov, a village in the Hodonín District of the South Moravian region?"}, {"text": "In what country is Drăgăneasa River located?"}, {"text": "In what country is Batsère, a commune in Hautes-Pyrénées?"}, {"text": "In what country is WZRU, the radio station located in Garysburg, North Carolina?"}, {"text": "In what country is Idlorpait, a place in the Kingdom of Denmark?"}, {"text": "In what country is the village of Barice, Donji Vakuf located?"}, {"text": "In what country is Habit, a township in Daviess County, Kentucky?"}, {"text": "In what country is Sabiote, a municipality?"}, {"text": "In what country is Kalateh-ye Safdarabad, a village in North Khorasan?"}, {"text": "In what country is the village of Cham Karim located?"}, {"text": "In what country is Tad, a village in Falavarjan County?"}, {"text": "In what country is the river Fântâneaua Rece located?"}, {"text": "In what country is Panaitoliko, a subdivision of Agrinio Municipality?"}, {"text": "In which country is Villalcampo, a municipality of Zamora Province?"}, {"text": "In what country is Scheidt, a stadtteil of Saarbrücken in Saarland located?"}, {"text": "In what country is Toronto Northwest, a federal electoral district, located?"}, {"text": "In what country is the village of Gornji Davidovići located?"}, {"text": "In what country is the independent record label, Hobbledehoy Record Co.?"}, {"text": "In what country is the television series SWEAT?"}, {"text": "In what country is the village of Dəhnəxəlil located?"}, {"text": "In what country is the village of Khvajeh Soheyl located?"}, {"text": "In what country is Zec Petawaga, a Controlled Harvesting Zone (zone d'exploitation contrôlée)?"}, {"text": "In what country is the Tapay District, located in Arequipa?"}, {"text": "In what country is Cổ Linh, a rural commune in Bắc Kạn?"}, {"text": "In what country is Mahaboboka, a place in Atsimo-Andrefana?"}, {"text": "In what country is the village of Cześniki-Kolonia Górna, located in Lublin?"}, {"text": "In what country is Awe, a township in Lewis County, Kentucky?"}, {"text": "In what country is Mrákotín, a village in the Chrudim District of the Pardubice region?"}, {"text": "In what country is Pichlice, a village in Łódź?"}, {"text": "In what country is Pero, a village in the Benešov District of the Central Bohemian region?"}, {"text": "In what country is Khafr County located?"}, {"text": "In what country is İnstitut, a human settlement?"}, {"text": "In what country is Karimu, a village in South Khorasan located?"}, {"text": "In what country is Graitschen bei Bürgel, a municipality located?"}, {"text": "In what country is Durrenentzen, a commune in Haut-Rhin?"}, {"text": "In what country is the village of Chal Siah Manchatun Jowkar located?"}, {"text": "In what country is the post office in Billings, known as United States Post Office and Courthouse–Billings?"}, {"text": "In what country is Eshkevar-e Sofla Rural District, which is a rural district in Gilan?"}, {"text": "In what country is the village of Rizuiyeh located?"}, {"text": "In what country is Veliko Korenovo, a settlement in the City of Bjelovar, Bjelovar-Bilogora County?"}, {"text": "In what country is Gimenells i el Pla de la Font, a municipality?"}, {"text": "In what country is La Roche-Clermault, a commune in Indre-et-Loire?"}, {"text": "In what country is the village of Biały Kościół, located in Lower Silesian Voivodeship?"}, {"text": "In what country is Content, a house near Centreville in Queen Anne's County, Maryland?"}, {"text": "In which country is Zhukiv, a village in Berezhany Raion, Ternopil Oblast located?"}, {"text": "In what country is Saint-Vincent-de-Salers, a commune in Cantal?"}, {"text": "In what country is Weed, the unincorporated community in Poinsett County, Arkansas?"}, {"text": "In what country is Alder, a human settlement in Saguache County, Colorado?"}, {"text": "In what country is Brizambourg, a commune in Charente-Maritime?"}, {"text": "In what country is the Călmuș River located?"}, {"text": "In what country is Eschbronn, a municipality located?"}, {"text": "In what country is Kondh, Surendranagar, a human settlement?"}, {"text": "In what country is the human settlement known as Rogers?"}, {"text": "In what country is Cos, a commune in Ariège located?"}, {"text": "In what country is Seishi Ursula Gakuen Junior College, a higher education institution in Miyazaki Prefecture?"}, {"text": "In what country is the Selkirk Generating Station, a building in Manitoba?"}, {"text": "In what country is the village of Devalan, located in Vezirköprü, Samsun?"}, {"text": "In what country is the Dârmocsa River located?"}, {"text": "In what country is the village, Gori Balmak?"}, {"text": "In what country is Wiang Kao, a district in Khon Kaen province?"}, {"text": "In what country is Francis, the former electoral district?"}, {"text": "In what country is Łazy, a village in Masovian?"}, {"text": "In which country is Khishig-Öndör, a Sum (district) in Bulgan Province?"}, {"text": "In what country is Gaffarlı, a köy in Göynücek?"}, {"text": "In what country is the human settlement, Crow Harbour, located in New Brunswick?"}, {"text": "In what country is Łodygowo, a village in Warmian-Masurian?"}, {"text": "In what country is the 6th cabinet, led by <PERSON>, located?"}, {"text": "In what country is the canton of Baugy located?"}, {"text": "In what country is Anjoma, a place in Haute Matsiatra?"}, {"text": "In what country is Ittamalliyagoda, a village in Central Province?"}, {"text": "In what country is Abra, a village in Lagunes?"}, {"text": "In what country is Okunakayama-Kōgen Station, located in Ichinohe, Ninohe district, Iwate prefecture?"}, {"text": "In what country is DeWitt Township, which is located in DeWitt County, Illinois?"}, {"text": "In what country is Centre?"}, {"text": "In what country is Asahi Station, the railway station in Kochi, Kochi prefecture?"}, {"text": "In what country is the village Stare Brzóski, located in the Podlaskie region?"}, {"text": "In what country is Bud, an unincorporated community in the Town of Jefferson, Vernon County, Wisconsin?"}, {"text": "In what country is Tangal-e Behdan, a village in South Khorasan?"}, {"text": "In what country was the now defunct radio station, Seed 97.5 FM?"}, {"text": "In what country is Perth, a federal electoral district?"}, {"text": "In what country is the human settlement, Ara?"}, {"text": "In what country is the village of Wir, located in Masovian?"}, {"text": "In what country is the village of Avarzaman located?"}, {"text": "In what country is Anaran Rural District located, a district in Ilam?"}, {"text": "In what country is the town of Barre Denis located?"}, {"text": "In what country is the Otto-Selz-Institute of Applied Psychology, a research institute at the University of Mannheim?"}, {"text": "In what country is Jodłówka gas field?"}, {"text": "In what country is Tupper-Barnett House, which is listed on the NRHP in Georgia?"}, {"text": "In what country is the Izvorul Morarului River located?"}, {"text": "In what country is Mehran Kushk, a village in South Khorasan?"}, {"text": "In what country is Astrodomi Observatory?"}, {"text": "In what country is the human settlement Muratdere?"}, {"text": "In what country is the village of Miętkie-Kolonia, located in Lublin?"}, {"text": "In what country is the organization, Szczecin Scientific Society?"}, {"text": "In what country is Märstetten, a municipality in the canton of Thurgau?"}, {"text": "In which country is the municipality of Riethnordhausen located?"}, {"text": "In what country is Tervola Radio and TV-Mast?"}, {"text": "In what country is the cricket stadium, Abdul <PERSON> Stadium?"}, {"text": "In what country is Alu, a village in Pärnu, Pärnu County?"}, {"text": "In what country is Chotýčany, a village in České Budějovice District of South Bohemian region?"}, {"text": "In what country is the Asseek River, a watercourse in British Columbia?"}, {"text": "In what country is the village of Gąsiorowo located, which is part of Legionowo County?"}, {"text": "In what country is Jeqjeq-e Pain, a place located in Ardabil Province?"}, {"text": "In what country is Dragomirna River, located in Suceava County?"}, {"text": "In what country is the village of Mohammadabad-e Razzaqzadeh, located in South Khorasan?"}, {"text": "In what country is Grant, a place located in the Slovenian Littoral?"}, {"text": "In what country is the Rubim do Norte River, located in Minas Gerais?"}, {"text": "In what country is the learned society, Institute of Chemistry of Ireland?"}, {"text": "In what country is Lima, a community in Wisconsin?"}, {"text": "In what country is KMEI-LP, the low-power radio station (97.3 FM) licensed to Kamiah, Idaho?"}, {"text": "In what country is Záblatí, a village in the Prachatice District of the South Bohemian region?"}, {"text": "In what country is Ba Thín River located?"}, {"text": "In what country is El Carmen Rivero Tórrez, a human settlement?"}, {"text": "In what country is Kawahigashi Station, located in Sukagawa, Fukushima prefecture?"}, {"text": "In what country is the Los Santos mine located?"}, {"text": "In what country is Whited Township, located in Kanabec County, Minnesota?"}, {"text": "In what country is Asalem Rural District, located in Gilan?"}, {"text": "In what country is Contest, a commune in Mayenne?"}, {"text": "In what country is Genoa, the unincorporated community in Olmsted County, Minnesota?"}, {"text": "In what country is Normania Township, located in Yellow Medicine County, Minnesota?"}, {"text": "In what country is the district of Chicche, located in Junín?"}, {"text": "In what country is the canton of Marseille-La Pomme?"}, {"text": "In what country is Devanur, a village in Tamil Nadu?"}, {"text": "In which country is Tegher, a village in the Aragatsotn Province?"}, {"text": "In what country is Kodki, a village in Gujarat?"}, {"text": "In what country is the village Javar Tan located?"}, {"text": "In what country is New England, the former state electoral district of New South Wales?"}, {"text": "In what country is the village of Kowale, located in Lower Silesian?"}, {"text": "In what country is Obeakpu, a village in Imo State?"}, {"text": "In what country is La Couarde-sur-Mer, a commune in Charente-Maritime?"}, {"text": "In what country is Riechheimer Berg, a municipal association in Thuringia?"}, {"text": "In what country is the Alexeni River located?"}, {"text": "In what country is Villers-sous-Foucarmont, a commune in Seine-Maritime?"}, {"text": "In what country is North Lake, a lake located in Nova Scotia?"}, {"text": "In what country is the military unit known as the 112th United States Colored Infantry?"}, {"text": "In what country is Storsteinnes Chapel, a church located in Balsfjord?"}, {"text": "In what country is the mountain Ch'uch'u Apachita located?"}, {"text": "In what country can you find the river Bārta?"}, {"text": "In what country is Urge, a village in Kohila Rural Municipality, Rapla County?"}, {"text": "In what country is Domašov, a village in the Brno-venkov District of South Moravian region?"}, {"text": "In what country is the village of Vaiea located?"}, {"text": "In what country is Monitor House, the historic house located in Ohio?"}, {"text": "In what country is the human settlement, Sagoni, located?"}, {"text": "In what country is the hospital, Eeuwfeestkliniek?"}, {"text": "In what country is the village of Łupiny, located in Masovian?"}, {"text": "In what country is the human settlement known as Xaga?"}, {"text": "In what country is Babino, a rural settlement in Grand'Anse?"}, {"text": "In what country is Hatnagoda, a village in Central Province?"}, {"text": "In what country is the human settlement Deodara located?"}, {"text": "In what country is Puzdrowizna, a village in Masovian?"}, {"text": "In what country is the village of Harisan located?"}, {"text": "In what country is Ločenice, a village in the České Budějovice District of the South Bohemian region?"}, {"text": "In what country is Aki, a dissolved municipality in Higashikunisaki district, Ōita prefecture?"}, {"text": "In what country is the Taia River located?"}, {"text": "In what country is Sjösa, an urban area in Nyköping Municipality?"}, {"text": "In what country is the municipality of Morales de Campos?"}, {"text": "In what country is Dobra River?"}, {"text": "In what country is Karahasanlı, a neighborhood in Karaisalı, Adana?"}, {"text": "In what country is the Ackerman-De<PERSON>nap House, located in Saddle River, New Jersey?"}, {"text": "In what country is Wilcza Jama, a village in Podlaskie, Sokółka County?"}, {"text": "In what country is Givron, a commune in Ardennes?"}, {"text": "In what country is Humane Heritage Museum?"}, {"text": "In what country is Arlington, an unincorporated community in Harrison County, West Virginia?"}, {"text": "In what country is Adams, the unincorporated community in New Jersey?"}, {"text": "In what country is Pira, a municipality, located?"}, {"text": "In what country is the historic Tōhoku History Museum located?"}, {"text": "In what country is Neal, the unincorporated community in Illinois?"}, {"text": "In what country is Korean Magazine Museum?"}, {"text": "In what country is Francheville Aerodrome?"}, {"text": "In what country is Kijevac, a village in Pčinja District?"}, {"text": "In what country is Iron River (CDP), a census-designated place in Bayfield County, Wisconsin?"}, {"text": "In what country is Lätäseno, the river located?"}, {"text": "In what country is Mount Shinten, which is located in Okinawa Prefecture?"}, {"text": "In what country is Dual Plover, the record label, located?"}, {"text": "In what country is Saint-Antonin, a commune in Gers?"}, {"text": "In what country is Peterson, an unincorporated community in Adams County, Indiana located?"}, {"text": "In what country is Joy, the unincorporated community in White County, Arkansas?"}, {"text": "In what country is Valea Pleșii River, a tributary of the Bârsa River?"}, {"text": "In what country is Sutlepa, a village in Lääne-Nigula Rural Municipality, Lääne County located?"}, {"text": "In what country is Movraž, a place in Littoral?"}, {"text": "In what country is Sarnowo, a village in Kuyavian-Pomeranian?"}, {"text": "In what country is Saint-Pierrevillers, a commune in Meuse?"}, {"text": "In what country is the Marine museum, Archipelago Museum, located?"}, {"text": "In what country is Revigliasco d'Asti, an Italian comune?"}, {"text": "In what country is the river Willow River located?"}, {"text": "In what country is Uñón District, located in Arequipa?"}, {"text": "In which country is Ban On, a subdistrict in Ngao district, Lampang province located?"}, {"text": "In what country is Kanaküla, a village in Saarde Rural Municipality, Pärnu County?"}, {"text": "In what country is Breitenfelde, an amt located?"}, {"text": "In what country is Konjsko Brdo, a settlement in the Municipality of Perušić, Lika-Senj County?"}, {"text": "In which country can you find the state highway in Albany County, New York, known as New York State Route 157?"}, {"text": "In what country is Le Moustoir, a commune in Côtes-d'Armor?"}, {"text": "In what country is the Mackay Courthouse, located in Queensland?"}, {"text": "In what country is Landresse, a commune in Doubs?"}, {"text": "In what country is Robinson, an unincorporated community in St. Louis County, Minnesota?"}, {"text": "In what country has the indoor tennis tournament, Lambertz Open by STAWAG, been held since 1991?"}, {"text": "In what country is the village Goreme, located in Strumyani municipality, Blagoevgrad oblast?"}, {"text": "In what country is Gawarzec Dolny, a village in Masovian?"}, {"text": "In what country is the settlement of Studzianka, located in Podlaskie?"}, {"text": "In what country is the railway station Gare de Rosporden located?"}, {"text": "In what country is Earl, an unincorporated community in Washburn County, Wisconsin?"}, {"text": "In what country is the village Donji Matejevac located in Nišava?"}, {"text": "In what country is Rozsochatec, a village in Havlíčkův Brod District of Vysočina region?"}, {"text": "Who was the producer of the 1943 film, <PERSON>, Jr.?"}, {"text": "Who was the producer of the 1961 film, <PERSON> and<PERSON>?"}, {"text": "Who was the producer of the 1963 film, The Hunt?"}, {"text": "Who was the producer of the 1960 film, The Accused?"}, {"text": "Who was the producer of the 2010 film, Just Like Us?"}, {"text": "Who was the producer of Today, an EP released by Everlast?"}, {"text": "Who was the producer of the 1916 film, The Pioneers?"}, {"text": "Who was the producer of the 1983 film, The Deal?"}, {"text": "Who was the producer of the 2006 live album, On Tour?"}, {"text": "Who was the producer of the 1915 silent short film, The Baby on the Barge?"}, {"text": "Who was the producer of the 1913 film, The Trap?"}, {"text": "Who was the producer of the 1917 film, The Hayseeds' Back-blocks Show?"}, {"text": "Who was the producer of the 1997 film, Ghost?"}, {"text": "Who was the producer of the 2007 film, From Now On?"}, {"text": "Who was the producer of the 1920 film, <PERSON>'s Wife?"}, {"text": "Who was the producer of the 2000 film, Italian Style?"}, {"text": "Who was the producer of Strand, a 2009 Iranian experimental film?"}, {"text": "Who was the producer of the 1918 film, The Thing We Love?"}, {"text": "Who was the producer of the 1953 film, One of Those?"}, {"text": "Who was the producer of the 1912 American film, The Lie?"}, {"text": "Who was the producer of the album Early Man?"}, {"text": "Who was the producer of the 1924 film, The Garden of Weeds?"}, {"text": "Who was the producer of Maling Kutang, a 2009 film directed by <PERSON><PERSON>?"}, {"text": "Who was the producer of the 1994 film, Party?"}, {"text": "Who was the producer of the 1922 film, Saturday Morning?"}, {"text": "Who was the producer of the 1924 German silent drama film, <PERSON> and Child?"}, {"text": "Who was the producer of the episode \"Revelations\" from Hell on Wheels (S1 E7)?"}, {"text": "Who was the producer of the album \"Home\" by <PERSON>?"}, {"text": "Who was the producer of the 1935 film, The Test?"}, {"text": "Who was the producer of the 1964 film, Me First?"}, {"text": "Who was the producer of the song \"Shine\" by <PERSON>?"}, {"text": "Who was the producer of the 2004 film, Trains of Winnipeg?"}, {"text": "Who was the producer of the 1971 film, In the Family?"}, {"text": "Who was the producer of The Easiest Way, a 1917 film by <PERSON>?"}, {"text": "Who was the producer of the 1940 film, Hired!?"}, {"text": "Who was the producer of the song, <PERSON>?"}, {"text": "Who was the producer of the 1942 film, <PERSON> Laatste Dagen van een Eiland?"}, {"text": "Who was the director of the 1935 film, City of Beautiful Nonsense?"}, {"text": "Who was the director of the 1914 film, The Sisters?"}, {"text": "Who was the director of the 1929 film, Those Who Love?"}, {"text": "Who was the director of the 2013 documentary film, Chi?"}, {"text": "Who was the director of the 1936 film, The Happy Family?"}, {"text": "Who was the director of the 1924 film, The Only Woman?"}, {"text": "Who was the director of the 1916 short film, The Gamble?"}, {"text": "Who was the director of the 2010 film, Senior Year?"}, {"text": "Who was the director of the 1938 film, Victory?"}, {"text": "Who was the director of the 1964 film, Me First?"}, {"text": "Who was the director of the pilot episode of <PERSON>, the Teenage Witch?"}, {"text": "Who was the director of the 1916 film, La renzoni?"}, {"text": "Who was the director of the 1999 film, <PERSON>?"}, {"text": "Who was the director of the episode \"Homecoming\" from Miss Guided?"}, {"text": "Who was the director of the 1936 film, Thank You, <PERSON>?"}, {"text": "Who was the director of the 1970 film, All the Way Up?"}, {"text": "Who was the director of the 1919 film, <PERSON><PERSON><PERSON><PERSON>?"}, {"text": "Who was the director of the 1984 film, College?"}, {"text": "Who was the director of the 1938 film, Practical Jokers?"}, {"text": "Who was the director of the 1993 film, The Tree?"}, {"text": "Who was the director of the 1916 film, Driven?"}, {"text": "Who was the director of the 1983 film, Son contento?"}, {"text": "Who was the director of the 1929 film, <PERSON><PERSON> at Midnight?"}, {"text": "Who was the director of <PERSON>, an episode of The Following (S2 E11)?"}, {"text": "Who was the director of the 1983 Bulgarian drama film, Balance?"}, {"text": "Who was the director of the 1916 film, Faith?"}, {"text": "Who was the director of the 1958 film, On the Run?"}, {"text": "Who was the director of the 1935 British film, Variety?"}, {"text": "Who was the director of the 1920 film, The Night Riders?"}, {"text": "Who was the director of the 1938 film, <PERSON> and <PERSON>?"}, {"text": "Who was the director of the 1997 film, La cruz?"}, {"text": "Who was the director of the 1933 film, The Love Nest?"}, {"text": "Who was the director of the 1915 film, The Resolve?"}, {"text": "Who was the director of the 1957 short film, Out?"}, {"text": "Who was the director of the 1943 film, While There is Still Time?"}, {"text": "Who was the director of the 1956 film, Den store gavtyv?"}, {"text": "Who was the director of the 1928 film, The Physician?"}, {"text": "Who was the director of the 1956 film, El Último perro?"}, {"text": "Who was the director of the 1917 film, The Easiest Way?"}, {"text": "Who was the director of the 1993 film, The Betrayed?"}, {"text": "Who was the director of the 1917 film, Sacrifice?"}, {"text": "Who was the director of the 1938 film, Women Who Work?"}, {"text": "Who was the director of the 1940 film, Trail?"}, {"text": "Who was the director of the 1955 film, Det var paa <PERSON>?"}, {"text": "Who was the director of the 1937 film, The Barrier?"}, {"text": "Who was the director of the 2003 film, <PERSON><PERSON>?"}, {"text": "Who was the director of the 1925 film, Men and Women?"}, {"text": "Who was the director of the 1915 American silent drama film, Sold?"}, {"text": "Who was the director of the 1941 Bollywood film, The Saint?"}, {"text": "Who was the director of the 1916 film, The Pioneers?"}, {"text": "Who was the director of the 1917 film, Broadway Jones?"}, {"text": "Who was the director of the 1973 film, The Last Word?"}, {"text": "Who was the director of the dramatic anthology television series from the United States, Escape?"}, {"text": "Who was the director of the 1937 film, These Children?"}, {"text": "Who was the director of the 1952 Norwegian film, Emergency Landing?"}, {"text": "Who was the director of the episode titled \"Pilot\" from the show Dirty Sexy Money (S1 E1)?"}, {"text": "Who was the director of the 1955 film, La Rival?"}, {"text": "Who was the director of the 1997 film, <PERSON>?"}, {"text": "Who was the director of the 1919 film, The Trap?"}, {"text": "Who was the director of the 2006 film, Cock<PERSON>?"}, {"text": "Who was the director of the 1934 German drama film, <PERSON> and Child?"}, {"text": "Who was the director of the 1937 film, The Pigskin Palooka?"}, {"text": "Who was the director of the 1916 film, Public Opinion?"}, {"text": "Who was the director of the 1984 film, College?"}, {"text": "Who was the director of the 1961 film, Day by Day?"}, {"text": "Who was the director of the 1914 film, The Day?"}, {"text": "Who was the director of the 1953 film, Le Guérisseur?"}, {"text": "Who was the director of the 2001 film, The Photo?"}, {"text": "Who was the director of the 1974 film, <PERSON><PERSON>?"}, {"text": "Who was the director of the 2006 film, Big Dreams Little Tokyo?"}, {"text": "Who was the director of the 1914 film, A Rowboat Romance?"}, {"text": "Who was the director of the 1961 film, Young People?"}, {"text": "Who was the director of the 1916 silent film, The Kiss?"}, {"text": "Who was the director of the 1929 film, In<PERSON>zienbewei<PERSON>?"}, {"text": "Who was the director of the 1976 film, Accident?"}, {"text": "Who was the director of the 1941 film, Fin<PERSON>?"}, {"text": "Who was the director of the 1964 film, The Girl in Mourning?"}, {"text": "Who was the director of the 1984 film, September?"}, {"text": "Who was the director of the 1979 Croatian film, The Return?"}, {"text": "Who was the director of the 1935 film, <PERSON><PERSON>?"}, {"text": "Who was the director of the 1997 film, Ghost?"}, {"text": "Who was the director of the 1953 film, One of Those?"}, {"text": "Who was one of the directors of the 1965 anthology film, The Key?"}, {"text": "Who was the director of the 1916 film, The Wolf?"}, {"text": "Who was the director of the 1999 film, <PERSON><PERSON>?"}, {"text": "Who was the director of the 2014 film, The Valley?"}, {"text": "Who was the director of the 1974 film, <PERSON> amare <PERSON>?"}, {"text": "Who was the director of the 1921 British silent crime film, The Loudwater Mystery?"}, {"text": "Who was the director of the Pilot episode of Life on a Stick (S1 E1)?"}, {"text": "Who was the director of the episode \"Ha<PERSON><PERSON>'s New Flame\" from season 1 of Moesha?"}, {"text": "Who was the director of the 2010 film, Just Like Us?"}, {"text": "Who was the director of the 1914 film, A Helpful Sisterhood?"}, {"text": "Who was the director of the 1928 film, Panic?"}, {"text": "Who was the director of the 1938 film, Victory?"}, {"text": "Who was the director of the 1925 film, Not So Long Ago?"}, {"text": "What is Kluczewsko, a village in Świętokrzyskie Voivodeship, Poland, the capital of?"}, {"text": "What is Jesús, a human settlement in Peru, the capital of?"}, {"text": "What is Bolsheustyikinskoye, a human settlement in the Republic of Bashkortostan, Russia, the capital of?"}, {"text": "What is Dmitriyev, a town in Russia, the capital of?"}, {"text": "What is Idi Rayeuk, a district in the East Aceh Regency, the capital of?"}, {"text": "Who was the screenwriter for the episode \"Death of a Batman\" of The Avengers?"}, {"text": "Who was the screenwriter for Fear No More, the 20th episode of the first season of Desperate Housewives?"}, {"text": "Who was the screenwriter for the 1927 film, The Fake?"}, {"text": "Who was the screenwriter for the 1974 film, <PERSON><PERSON><PERSON> pintad<PERSON>?"}, {"text": "Who was the screenwriter for the pilot episode of <PERSON>, the Teenage Witch directed by <PERSON><PERSON>?"}, {"text": "Who was the screenwriter for the 1995 film, Goodbye?"}, {"text": "Who was the screenwriter for the 1994 film, Party?"}, {"text": "Who was the screenwriter for the episode \"<PERSON>\" from season 1, episode 4 of Falling Skies?"}, {"text": "Who was the screenwriter for the 1997 film, Ghost?"}, {"text": "Who was the screenwriter for the 1937 film, By og land hand i hand?"}, {"text": "Who was the screenwriter for the 1960 film, The Accused?"}, {"text": "Who was the screenwriter for the 1921 film, Exit the Vamp?"}, {"text": "Who was the screenwriter for the 1920 film, <PERSON>'s Ankle?"}, {"text": "Who was the screenwriter for the 1918 film, Democracy?"}, {"text": "Who was the screenwriter for the episode \"Revelations\" from Season 1 of Hell on Wheels?"}, {"text": "Who was the screenwriter for the 1957 television film, Ending It?"}, {"text": "Who was the screenwriter for the 1989 Swedish short film, <PERSON><PERSON> hö<PERSON>?"}, {"text": "Who was the screenwriter for the episode \"Guilty\" from season 1 of Awake?"}, {"text": "Who was the screenwriter for the 2008 Australian film, Salvation?"}, {"text": "Who was the screenwriter for the 1973 film, The Last Word?"}, {"text": "Who was the screenwriter for the 1976 film, <PERSON><PERSON><PERSON><PERSON>?"}, {"text": "Who was the screenwriter for the 2003 film, White Gold?"}, {"text": "Who was the screenwriter for the 1997 film, The Bride’s Journey?"}, {"text": "Who was the screenwriter for the episode \"Pilot\" from the first season of Franklin & Bash?"}, {"text": "Who was the screenwriter for the 1937 film, These Children?"}, {"text": "Who was the screenwriter for Prototype, the pilot episode of the ABC sitcom Spin City?"}, {"text": "Who was the screenwriter for the 1915 film, <PERSON>'s New Boarder?"}, {"text": "Who was the screenwriter for the 1975 film, Le Fils d'Amr est mort?"}, {"text": "Who was the screenwriter for the 1964 film, <PERSON><PERSON>?"}, {"text": "Who was the screenwriter for the 1994 film, The Worst Years of Our Lives?"}, {"text": "Who was the screenwriter for the 1916 film, The City?"}, {"text": "Who was the screenwriter for the 1995 film, <PERSON>: My Life... Your Fault?"}, {"text": "Who was the screenwriter for the 1959 film, Three Loves in Rio?"}, {"text": "Who was the screenwriter for the episode \"Guilty\" from Season 1, Episode 3 of Awake?"}, {"text": "Who was the screenwriter for the 1979 Croatian film, The Return?"}, {"text": "Who was the screenwriter for the episode \"Oregon\" from the first season of Awake?"}, {"text": "Who was the screenwriter for the 1974 film, <PERSON><PERSON>?"}, {"text": "Who was the screenwriter for \"Impossible\", the 15th episode of the first season of Desperate Housewives?"}, {"text": "Who was the screenwriter for the 1960 film, The Accused?"}, {"text": "Who was the screenwriter for the 1918 American silent drama film, Daybreak?"}, {"text": "Who was the screenwriter for the 2007 film, <PERSON><PERSON><PERSON><PERSON>, masculin?"}, {"text": "Who was the screenwriter for the 1917 British comedy film, <PERSON>?"}, {"text": "Who was the composer of the chamber opera, One?"}, {"text": "Who was the composer of the 1995 single, Hello?"}, {"text": "Who was the composer of the 1997 film, Ghost?"}, {"text": "Who was the composer of the composition, <PERSON>?"}, {"text": "Who was the composer of the 1937 film, To Live?"}, {"text": "Who was the composer of the opera, <PERSON><PERSON>?"}, {"text": "Who was the composer of the song \"To The West\", with lyrics by <PERSON>?"}, {"text": "Who was the composer of the ballet, The Witch?"}, {"text": "Who was the composer of the ballet Images by <PERSON>?"}, {"text": "Who was the composer of <PERSON><PERSON><PERSON>, a song performed by <PERSON><PERSON><PERSON>?"}, {"text": "Who was the composer of the song \"I'm in Love\", originally performed by <PERSON><PERSON> at Melodifestivalen 2011?"}, {"text": "Who was the composer of Prelude in F major, Op. 49, No. 2?"}, {"text": "Who was the composer of the concerto titled Piano Concerto?"}, {"text": "Who was the composer of the cantata, <PERSON><PERSON><PERSON><PERSON> euch <PERSON>, bedr<PERSON><PERSON><PERSON>, BWV 224?"}, {"text": "Who was the composer of the EP, Homecoming?"}, {"text": "Who was the composer of The Greater Good, or the Passion of <PERSON><PERSON> Su<PERSON>, an opera in two acts?"}, {"text": "Who was the composer of the opera, <PERSON><PERSON><PERSON>!?"}, {"text": "Who was the composer of the 1978 TVB television series, The Giants?"}, {"text": "Who was the composer of the song \"To the Sky\" by <PERSON><PERSON><PERSON>?"}, {"text": "Who was the composer of the American musical, Say When?"}, {"text": "Who was the composer of the 2011 album, Alone?"}, {"text": "Who was the composer of the 2010 song, Famous?"}, {"text": "Who was the composer of the song \"Signal\" by <PERSON><PERSON>?"}, {"text": "Who was the composer of the 1929 song, Miss You?"}, {"text": "Who was the composer of the 2012 song, Living with You?"}, {"text": "Who was the composer of the song, <PERSON>?"}, {"text": "Who was the composer of Images, an album of piano pieces played by <PERSON>?"}, {"text": "Who was the composer of The Hope, a work for brass band, percussion, choir, and organ?"}, {"text": "Who was the composer of the composition, Time Machine?"}, {"text": "Who was the composer of the 1991 Pearl Jam song, Porch?"}, {"text": "Who was the composer of the song, <PERSON>?"}, {"text": "Who was the composer of the opera, <PERSON>zze istriane?"}, {"text": "Who was the composer of the musical piece, Overture in G major?"}, {"text": "Who was the composer of the song \"Tea for One\", performed by Led Zeppelin?"}, {"text": "Who was the composer of the 2014 single, Chasing?"}, {"text": "Who was the composer of the musical piece, String Quartet No. 3?"}, {"text": "Who was the composer of the song titled 'That's Right'?"}, {"text": "Who was the composer of the piece known as Symphony No. 33?"}, {"text": "Who was the composer of the piece known as Symphony No. 8?"}, {"text": "Who was the composer of the instrumental composition, <PERSON><PERSON><PERSON>?"}, {"text": "Who was the composer of the 1950 film, Cue Ball Cat?"}, {"text": "Who was the composer of the 2010 song, One More Time?"}, {"text": "Who was the composer of the jazz bebop song, Big Foot?"}, {"text": "Who was the composer of the musical, Sometime?"}, {"text": "Who was the composer of the composition, Prelude for Clarinet?"}, {"text": "Who was the composer of The Moment's Energy, the 2009 live album by <PERSON>tro-Acoustic Ensemble?"}, {"text": "Who was the composer of the composition, <PERSON>?"}, {"text": "Who was the composer of the 1929 song, Miss You?"}, {"text": "What is the religion of <PERSON>, the American bishop (1873-1968)?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON>, who was a bishop from 1944 to 2021?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, the Canadian archbishop (1853–1927)?"}, {"text": "What is the religion of <PERSON><PERSON>, a bishop?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON>, a Polish priest?"}, {"text": "What is the religion of <PERSON>, the Catholic cardinal?"}, {"text": "What is the religion of <PERSON>, the Jamaican bishop?"}, {"text": "What is the religion of <PERSON>, the Catholic archbishop?"}, {"text": "What is the religion of <PERSON>, who was a bishop in India from 1926–1936?"}, {"text": "What is the religion of Guadalupe Missionaries, a missionary society in Mexico?"}, {"text": "What is the religion of <PERSON>, the Bishop of Bedford?"}, {"text": "What is the religion of <PERSON>, the Fijian bishop?"}, {"text": "What is the religion of <PERSON>, who is a nuncio?"}, {"text": "What is the religion of the Irish dean, <PERSON> (1827-1917)?"}, {"text": "What is the religion of <PERSON>, the Romanian bishop?"}, {"text": "What is the religion of the French bishop, <PERSON><PERSON> (1935-2013)?"}, {"text": "What is the religion of <PERSON>, the Austrian bishop (1929-2014)?"}, {"text": "What is the religion of <PERSON>, the Roman Catholic bishop?"}, {"text": "What is the religion of <PERSON>, the Vicar Apostolic of Alaska?"}, {"text": "What is the religion of <PERSON>, the bishop and prelate?"}, {"text": "What was the religion of <PERSON>, the archbishop who lived between 1927 and 2010?"}, {"text": "What is the religion of <PERSON>, a bishop?"}, {"text": "What is the religion of <PERSON>, known as an Anglican bishop?"}, {"text": "What is the religion of <PERSON><PERSON> (1932-2011), who was an Archbishop of Lecce?"}, {"text": "What is the religion of <PERSON>, the archbishop?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON><PERSON>, the Catholic cardinal?"}, {"text": "What is the religion of <PERSON>, the Irish Bishop?"}, {"text": "What is the religion of <PERSON>, the Ivorian cardinal?"}, {"text": "What is the religion of <PERSON>, the bishop?"}, {"text": "What is the religion of <PERSON>, the Japanese bishop?"}, {"text": "What is the religion of <PERSON> Je<PERSON>í<PERSON>, the Nicaraguan bishop?"}, {"text": "What is the religion of <PERSON>, the bishop of Calcutta?"}, {"text": "What is the religion of <PERSON>, the British bishop?"}, {"text": "What is the religion of <PERSON>, the archbishop from Madagascar?"}, {"text": "What is the religion of Ecclesiastical Statistics?"}, {"text": "What is the religion of <PERSON><PERSON> (1896-1981)?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON>, who served as an archbishop from 1930-2009?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON>, the Catholic archbishop?"}, {"text": "What is the religion of <PERSON>, known as an Anglican bishop?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON>, the Brazilian bishop?"}, {"text": "What sport does the 2012 season of the Georgetown Hoyas men's team play?"}, {"text": "What sport did <PERSON>, the Canadian player (1913-1993), play?"}, {"text": "What sport does the Brazilian athlete <PERSON><PERSON> play?"}, {"text": "What sport does the Malagasy athlete <PERSON> play?"}, {"text": "What sport does the Kazakhstani athlete <PERSON><PERSON><PERSON> play?"}, {"text": "What sport is played in the 2006 Korea Open Championships, which is a type of badminton championships?"}, {"text": "What sport does the judoka <PERSON> play?"}, {"text": "What sport does the 2006–07 Primera B Nacional season play?"}, {"text": "What sport does the German player/agent, <PERSON><PERSON><PERSON>, play?"}, {"text": "What sport does the 1994 edition of the Swedish Open play?"}, {"text": "What sport does <PERSON><PERSON><PERSON>, the Iraqi athlete, play?"}, {"text": "What sport is associated with the 2004 Legg Mason Tennis Classic tournament?"}, {"text": "What sport does the Argentine athlete <PERSON> play?"}, {"text": "What sport does the football tournament season of 1988–89 FA Cup Qualifying Rounds involve?"}, {"text": "What sport does the Spanish athlete <PERSON> play?"}, {"text": "What sport does <PERSON> (1944-2023), a French athlete, play?"}, {"text": "What sport does the Spanish athlete <PERSON> play?"}, {"text": "What sport does 1989–90 1. Slovenská národná hokejová liga season play?"}, {"text": "What sport does the entity known as \"1923 in Brazilian football\", which refers to a specific season, play?"}, {"text": "What sport does the Chinese athlete, <PERSON>, play?"}, {"text": "What sport does the Italian athlete <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON>, the Estonian athlete, play?"}, {"text": "What sport does <PERSON><PERSON>, a German athlete, play?"}, {"text": "What sport is played in the 2014 Powiat Poznański Open, a $50,000 ITF Women's Circuit tournament?"}, {"text": "What sport does the collegiate event, 1997 Conference USA Baseball Tournament, play?"}, {"text": "What sport does the Russian athlete, <PERSON><PERSON><PERSON>, play?"}, {"text": "What sport does <PERSON>, the Kazakhstani athlete, play?"}, {"text": "What sport does the Italian athlete <PERSON> play?"}, {"text": "What sport did <PERSON><PERSON><PERSON> (1899-1934), a Romanian athlete, play?"}, {"text": "What sport does the Scottish player <PERSON> play?"}, {"text": "What sport does <PERSON>, the athlete, play?"}, {"text": "What sport does <PERSON>, born in 1989, play?"}, {"text": "What sport does <PERSON><PERSON><PERSON>, the Slovak player, participate in?"}, {"text": "What sport is played in the international competition, 2002 Euro Beach Soccer Cup?"}, {"text": "What sport does the Uruguayan athlete <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON>, the South Korean athlete, play?"}, {"text": "What sport did <PERSON> (1925-2005) play?"}, {"text": "What sport does the Czech athlete, <PERSON><PERSON>, play?"}, {"text": "What sport does <PERSON>, the Czech athlete, play?"}, {"text": "What sport does <PERSON> (1876-?) play?"}, {"text": "What sport is played in the 2013 edition of the Torneo di Viareggio tournament?"}, {"text": "What sport does the Uruguayan athlete <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does the Spanish athlete <PERSON> play?"}, {"text": "What sport does the Russian athlete, <PERSON><PERSON>, play?"}, {"text": "What sport does the Russian athlete <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does the French athlete, <PERSON><PERSON><PERSON>, play?"}, {"text": "What sport does the 2010–11 South West Peninsula League, a competition in England, play?"}, {"text": "What sport did <PERSON> (1912-1987), a Belgian athlete, play?"}, {"text": "What sport did <PERSON> (1927-2014), an Austrian athlete, play?"}, {"text": "What sport did <PERSON> (1881-1950) play?"}, {"text": "What sport does the sports season of 1990–91 British Basketball League pertain to?"}, {"text": "What sport does <PERSON><PERSON>, a player from Bosnia and Herzegovina, play?"}, {"text": "What sport does the national team, Zanzibar national under-20, play?"}, {"text": "What sport does the Italian athlete <PERSON> play?"}, {"text": "What sport is played in the 2001–02 Division 1 season in Sweden?"}, {"text": "What sport does the American professional, <PERSON>, play?"}, {"text": "What sport does <PERSON>, the Chinese athlete, play?"}, {"text": "What sport does the Brazilian athlete, <PERSON> Andrade, play?"}, {"text": "What sport does the Ugandan athlete, <PERSON>, play?"}, {"text": "What sport does the Spanish athlete <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does the Swiss athlete <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does the Brazilian athlete <PERSON> play?"}, {"text": "What sport does the athlete <PERSON> play?"}, {"text": "What sport does the Indonesia Education League, a football league, play?"}, {"text": "What sport does the Peruvian athlete <PERSON> play?"}, {"text": "What sport does the Argentine athlete, <PERSON>, play?"}, {"text": "What sport did <PERSON><PERSON> (1962-2014), a Russian athlete, play?"}, {"text": "What sport does the Algerian and French athlete, <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON> participate in?"}, {"text": "What sport does <PERSON><PERSON>, a Japanese athlete, play?"}, {"text": "What sport does WTA South Orange, a women's tournament, play?"}, {"text": "What sport does the Japanese athlete <PERSON><PERSON> play?"}, {"text": "What sport does the Belgian athlete <PERSON> play?"}, {"text": "What sport does the Japanese athlete <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport did <PERSON> (1898-1978), an English athlete, play?"}, {"text": "What sport does the Russian athlete <PERSON> play?"}, {"text": "What sport does the South Korean athlete <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON>, born in 1992, play?"}, {"text": "What sport does the Canadian defenceman, <PERSON> play?"}, {"text": "What sport does the Spanish athlete <PERSON> play?"}, {"text": "What sport does the Bulgarian athlete <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does 1994–95 FIBA Women's European Champions Cup play?"}, {"text": "What sport did <PERSON><PERSON> (1902-1963) play?"}, {"text": "What sport did <PERSON> (1926-2000) play?"}, {"text": "What sport does the Australian athlete <PERSON> play?"}, {"text": "What sport does <PERSON>, the Scottish athlete born in 1934, play?"}, {"text": "What sport does <PERSON><PERSON>, the Liberian athlete, play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> participate in?"}, {"text": "What sport does the Moldovan athlete <PERSON><PERSON> play?"}, {"text": "What sport does the French athlete <PERSON> play?"}, {"text": "What sport does the American college 2011-12 Elon Phoenix men's team play?"}, {"text": "What sport does the Mexican athlete <PERSON> play?"}, {"text": "What sport does the FIBT World Championships 1939, which took place in St. Moritz, Switzerland and Cortina d'Ampezzo, Italy, play?"}, {"text": "What sport did <PERSON> (1880-1971) play?"}, {"text": "What sport does the Romanian athlete <PERSON> play?"}, {"text": "What sport is played in the 1973 Virginia Slims of Fort Lauderdale, a tennis tournament?"}, {"text": "What sport does the Malaysian athlete <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does the team from the 1949 France tour of Argentina play?"}, {"text": "What sport does the Argentine athlete <PERSON><PERSON> play?"}, {"text": "What sport does the team from the All-Ireland Senior Club Camogie Championship 1970 play?"}, {"text": "What sport does the Belarusian athlete <PERSON> play?"}, {"text": "What sport did <PERSON> (1952-2015), a Scottish athlete, play?"}, {"text": "What sport does <PERSON><PERSON>, the Croatian athlete, play?"}, {"text": "What sport does <PERSON>, born in 1944, play?"}, {"text": "What sport does <PERSON>, who was born in 1900, play?"}, {"text": "What sport does the Brazilian athlete, <PERSON><PERSON><PERSON><PERSON>, play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON>, the Argentinian athlete born in 1984, play?"}, {"text": "What sport does <PERSON><PERSON>, the athlete, play?"}, {"text": "What sport does the football league season of 2008–09 National Indoor Soccer League play?"}, {"text": "What sport does the women's football season, 1994–95 Fußball-Bundesliga, play?"}, {"text": "What sport did <PERSON> (1944-2014), the Spanish athlete, play?"}, {"text": "What sport does the Swiss athlete <PERSON><PERSON> play?"}, {"text": "What sport does Granada Lions, the team based in Granada, Andalusia, Spain, play?"}, {"text": "What sport does <PERSON>, the Spanish player, participate in?"}, {"text": "What sport does the Bolivian athlete, <PERSON>, play?"}, {"text": "What sport does the footballer, <PERSON>, play?"}, {"text": "What sport does the club Afyonkarahisarspor play?"}, {"text": "What sport does canoeing at the 2014 Asian Games – women's K-4 500 metres play?"}, {"text": "What sport does <PERSON>, the Italian-Argentine athlete, play?"}, {"text": "What sport does the Austrian athlete <PERSON> play?"}, {"text": "What sport does the Slovak athlete <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON>, the Hungarian player, participate in?"}, {"text": "What sport does the 1995 Cook Islands Round Cup, a football league season, play?"}, {"text": "What sport does the Swiss athlete <PERSON> play?"}, {"text": "What sport does the Algerian athlete <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON>, who was born in 1984, play?"}, {"text": "What sport does Sofia Anker-Kofoed play?"}, {"text": "What sport does Kiribati men's national basketball team play?"}, {"text": "What sport does <PERSON><PERSON>, the Moroccan athlete, play?"}, {"text": "What sport does Turkish Seniors Open play?"}, {"text": "What sport does Njurunda SK, a team in Njurunda, Sweden, play?"}, {"text": "What sport does the 2009 Ukrainian Cup Final, an association football match, play?"}, {"text": "What sport does the Bulgarian athlete <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON>, the Czech athlete, play?"}, {"text": "What sport does the Iranian athlete <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does the Canadian athlete <PERSON><PERSON> play?"}, {"text": "What sport does the Slovak athlete <PERSON> play?"}, {"text": "What sport does the Canadian athlete <PERSON> play?"}, {"text": "What sport does the French player, <PERSON>, participate in?"}, {"text": "What sport does Sandar IL, a Norwegian sports club in Sandefjord, play?"}, {"text": "What sport does <PERSON>, the racing cyclist, play?"}, {"text": "What sport does the Brazilian athlete, <PERSON>, play?"}, {"text": "What sport does the Algerian club, E Sour El Ghozlane, play?"}, {"text": "What sport does the Croatian athlete, <PERSON><PERSON>, play?"}, {"text": "What sport does the Swiss athlete <PERSON> play?"}, {"text": "What sport is played in the 1998–99 season of the Slovenian Basketball League?"}, {"text": "What sport does the Welsh athlete, <PERSON> (born 1950), play?"}, {"text": "What sport does the Wikimedia list article, \"List of Azerbaijan football transfers winter 2012\", pertain to?"}, {"text": "What sport does <PERSON><PERSON><PERSON>, the Syrian athlete, play?"}, {"text": "What sport does VOKO-Irodion play?"}, {"text": "What sport does <PERSON>, born in 1951, play?"}, {"text": "What sport does Guyana women's national field hockey team play?"}, {"text": "What sport does the Bulgarian player and manager, <PERSON><PERSON><PERSON>, play?"}, {"text": "What sport does the German athlete, <PERSON><PERSON><PERSON>, play?"}, {"text": "What sport does the Mexican club, Lobos BUAP Premier, play?"}, {"text": "What sport does <PERSON>, who lived from 1881-1964, play?"}, {"text": "What sport does the American player <PERSON> play?"}, {"text": "What sport does <PERSON> (1967-2017) play?"}, {"text": "What sport did <PERSON> (1917-2003), an English athlete, play?"}, {"text": "What sport does the Australian athlete <PERSON><PERSON> play?"}, {"text": "What sport does the Chinese athlete <PERSON> play?"}, {"text": "What sport does the Turkish athlete <PERSON><PERSON><PERSON>ü<PERSON> play?"}, {"text": "What sport did <PERSON><PERSON> (1915-1995), a Dutch player, participate in?"}, {"text": "What sport does the football player <PERSON> play?"}, {"text": "What sport does the Montenegrin athlete, <PERSON><PERSON>, play?"}, {"text": "What sport does the South Korean athlete, <PERSON><PERSON>, play?"}, {"text": "What sport does <PERSON>, born in 1965, play?"}, {"text": "What sport does the Czech athlete <PERSON><PERSON> play?"}, {"text": "What sport does the national governing body of cycle racing, Colombian Cycling Federation, play?"}, {"text": "What sport does the 1920–21 season of the Northern Football League play?"}, {"text": "What sport does <PERSON>, the footballer, play?"}, {"text": "What sport does <PERSON>, the Montserratian athlete born in 1979, play?"}, {"text": "What sport does the Beninese club, Université Nationale du Bénin FC, play?"}, {"text": "What sport does the football league season, 2012 Uzbekistan First League, play?"}, {"text": "What sport did <PERSON><PERSON><PERSON> (1943-2014) play?"}, {"text": "What sport does the Polish athlete <PERSON><PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does the Chilean athlete <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON>, the athlete from Bosnia and Herzegovina, play?"}, {"text": "What sport does <PERSON>, the Portuguese athlete, play?"}, {"text": "What sport does <PERSON><PERSON><PERSON>, the Brazilian player, participate in?"}, {"text": "What sport does the Romanian athlete, <PERSON>, play?"}, {"text": "What sport does the Portuguese athlete <PERSON> play?"}, {"text": "What sport does the Greek athlete, <PERSON><PERSON>, play?"}, {"text": "What sport does <PERSON><PERSON><PERSON>, an athlete, play?"}, {"text": "What sport did <PERSON> (1893-1979), an English athlete, play?"}, {"text": "What sport does <PERSON>, an English athlete from the late 19th century, play?"}, {"text": "What sport does the Argentine athlete <PERSON> play?"}, {"text": "What sport is played in the 2011 season of the Chatham Cup?"}, {"text": "What sport is played in the Maltese Women's Cup tournament?"}, {"text": "What sport is played in the 2009 Atlantic Coast Conference, a collegiate baseball tournament?"}, {"text": "What sport does the Slovak athlete, <PERSON><PERSON>, play?"}, {"text": "What sport does the Zambian athlete <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON>, the Haitian athlete, play?"}, {"text": "What sport does <PERSON><PERSON> (1937-2021), who was English, play?"}, {"text": "What sport does the Cuban athlete <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "Who is the author of the feminist literature play, After<PERSON>, published on 1883-01-01?"}, {"text": "Who is the author of the fiction short story collection, <PERSON>, published by Melville House Publishing on 2010-09-07?"}, {"text": "Who is the author of the horror literature book, Watchers at the Strait Gate, published by Arkham House on 1984-01-01?"}, {"text": "Who is the author of the crime novel, <PERSON>, published by Ballantine Books on 2008-01-01?"}, {"text": "Who is the author of the short story, Only Human?"}, {"text": "Who is the author of the fantasy book, Out of the Dark, published by House of Anansi Press on 1995-01-01?"}, {"text": "Who is the author of the book, The National Dream, published on 1974-01-01?"}, {"text": "Who is the author of the book titled Saints of Big Harbour, published on 2002-01-01?"}, {"text": "Who is the author of the book, Endpeace, published by HarperCollins on 1996-01-01?"}, {"text": "Who is the author of the science fiction book, Turning On, published by Doubleday on 1966-01-01?"}, {"text": "Who is the author of the science fiction novel, Something More, published by Gollancz on 2001-01-01?"}, {"text": "Who is the author of the book titled 'The Romantic', which was published on 2003-01-01?"}, {"text": "Who is the author of the novel, <PERSON><PERSON><PERSON>, published by Oxford University Press on 2011-02-03?"}, {"text": "Who is the author of the short story, Time Enough?"}, {"text": "Who is the author of the play, <PERSON><PERSON>?"}, {"text": "Who is the author of the book, <PERSON><PERSON>, published by Little, Brown and Company on 2008-06-10?"}, {"text": "Who is the author of the 1990 apocalyptic fiction novel, Fire, published on 1990-01-01?"}, {"text": "Who is the author of the young adult fantasy novel, Carnival of Souls, published by HarperCollins on 2012-09-04?"}, {"text": "Who is the author of the book, <PERSON><PERSON><PERSON>, published on 1886-01-01?"}, {"text": "Who is the author of the crime novel, <PERSON>, published by Ballantine Books on 2005-01-01?"}, {"text": "Who is the author of the book titled \"Kid\", published on 1992-01-01?"}, {"text": "Who is the author of the Indonesian novel, It's Not an All Night Fair, published by <PERSON><PERSON><PERSON> Sadar on 1964-01-01?"}, {"text": "Who is the co-author of the science fiction novel, Heaven, published on 2004-01-01?"}, {"text": "Who is the author of the sword and sorcery novel, <PERSON> the Valiant, published on 1988-10-01?"}, {"text": "Who is the author of the 2006 novel, <PERSON><PERSON>, published on 2006-01-01?"}, {"text": "Who is the author of the book titled Regeneration, published on 1910-01-01?"}, {"text": "Who is the author of the book, The Latimers, published on 1898-01-01?"}, {"text": "Who is the author of the book titled <PERSON>, published on 2013-09-03?"}, {"text": "Who is the author of the play, <PERSON><PERSON>is <PERSON> Dew?"}, {"text": "Who is the author of the anthology of sci-fi and fantasy short stories, World of Wonder, a fantasy genre, published by Gale?"}, {"text": "Who is the author of the 1987 Australian novel, Dancing on Coral, published by Viking Press on 1987-01-01?"}, {"text": "Who is the author of the book, New Keywords, published by Wiley-Blackwell on 2005-01-01?"}, {"text": "Who is the author of the 1978 novel, Getting Free, published by Oxford University Press?"}, {"text": "Who is the author of the crime fiction novel, <PERSON>, published by HarperCollins on 2001-01-01?"}, {"text": "Who is the author of the science fiction book, Looking Forward, published on 1953-01-01?"}, {"text": "Who is the author of the science fiction book The World Before, published by HarperCollins on 2005-10-01?"}, {"text": "Who is the author of the 1972 play, <PERSON>?"}, {"text": "Who is the author of the book, The End of the Soul, published on 2003-01-01?"}, {"text": "Who is the author of the Franco-Belgian one shot comic, Western?"}, {"text": "Who is the author of the science fiction book, The Warriors of Spider, published on 1988-01-01?"}, {"text": "Who is the author of the fantasy short story, Homecoming, published by Tor Books on 2003-01-01?"}, {"text": "Who is the author of the novella, The Amazon, published by Otechestvennye Zapiski on 1866-01-01?"}, {"text": "Who is the author of the book, O dia das calças roladas, published on 1982-01-01?"}, {"text": "Who is the author of the book, Visionseeker: Shared Wisdom from the Place of Refuge, published on 2002-01-01?"}, {"text": "Who is the author of Out of This World, a fantasy novel in The Worlds of Shadow trilogy, published on 1993-01-01?"}, {"text": "Who is the author of the play, Stand By Your Screen?"}, {"text": "Who is the author of the book titled '<PERSON>', published by The Century Company on 1905-04-01?"}, {"text": "Who is the author of the book, The Interior, published by HarperCollins on 1999-01-01?"}, {"text": "Who is the author of the short story, Memory?"}, {"text": "Who is the author of the book titled <PERSON>, published on 1975-01-01?"}, {"text": "Who is the author of School for Coquettes?"}, {"text": "Who is the author of the book \"Trust Me\", published by <PERSON> A<PERSON> on 1987-01-01?"}, {"text": "Who is the author of the science fiction book titled Recursion, published on 2004-01-01?"}, {"text": "Who is the author of The Bishop's Heir, a fantasy novel published by Ballantine Books in 1984?"}, {"text": "Who is the author of the comic, <PERSON>?"}, {"text": "Who is the author of the 1996 comedy novel, This Is It, published on 1996-01-01?"}, {"text": "Who is the author of the book, A Survey, published on 1921-01-01?"}, {"text": "Who is the author of the play, Skyscraper?"}, {"text": "Who is the author of <PERSON>, a Star Trek: Section 31 science fiction novel, published by Pocket Books on 2001-06-01?"}, {"text": "Who is the author of the poetry journal titled \"This\", which falls under the poetry genre and was published on 1971-01-01?"}, {"text": "Who is the author of the book, <PERSON>, My Friend?"}, {"text": "Who is the author of the fantasy book, The Great World and the Small: More Tales of the Ominous and Magical, published on 2001-01-01?"}, {"text": "Who is the author of the science fiction book titled Robots, published by Ace Books on 2005-01-01?"}, {"text": "Who is the author of the book, The Outdoor Survival Handbook, published on 1992-01-01?"}, {"text": "Who is the author of the novel, Millennial Rites, published by Virgin Books on 1995-10-01?"}, {"text": "Who is the author of the crime novel \"Shame\", written by a Swedish crime-writer, published by Canongate Books on 2005-01-01?"}, {"text": "Who is the author of the book, The Burning, published by BBC Books on 2000-08-01?"}, {"text": "Who is the author of the 1964 novel, Second Generation, published on 1964-01-01?"}, {"text": "Who is the author of the novel, The Guard, published by Elain Publishing House on 2008-01-01?"}, {"text": "Who is the author of the short story, <PERSON>?"}, {"text": "Who is the author of the book titled Nuclear Alert, published on 1983-05-01?"}, {"text": "Who is the author of the play, <PERSON><PERSON><PERSON><PERSON>?"}, {"text": "Who is the author of the novel, <PERSON><PERSON>, published by Viking Press on 1983-10-26?"}, {"text": "Who is the author of <PERSON>, the Star Trek: Section 31 novel, a science fiction novel published by Pocket Books on 2001-06-01?"}, {"text": "Who is the author of the book The Museum of Abandoned Secrets, published on 2009-01-01?"}, {"text": "Who is the author of the book titled \"Responsibility\", published on 2005-01-01?"}, {"text": "Who is the author of the book, <PERSON> Amalia, published on 2006-01-01?"}, {"text": "Who is the author of the science fiction book titled Zones, published on 1997-01-01?"}, {"text": "Who is the author of the 2004 fantasy novel, <PERSON>, published by HarperCollins on 2004-12-15?"}, {"text": "Who is the author of the non-fiction book titled \"Beyond\", published by W. W. Norton & Company on 2015-04-13?"}, {"text": "Who is the author of the book titled 'The Other Place', published by HarperCollins on 1999-08-19?"}, {"text": "Who is the author of the short story, A Positive?"}, {"text": "Who is the author of the four-issue American comic book limited series, Down?"}, {"text": "Who is the author of the children's book series, <PERSON>?"}, {"text": "Who is the author of the book \"What You Make It\", published by HarperCollins on 1999-01-01?"}, {"text": "Who is the author of the fantasy book, Great Short Novels of Adult Fantasy I, published by Ballantine Books on 1972-01-01?"}, {"text": "Who is the author of the poem, The Voice, published on 1912-12-01?"}, {"text": "Who is the author of the book, Follow The Music, published on 2000-08-30?"}, {"text": "Who is the author of the 1985 science fiction novel, Time After Time, published on 1985-01-01?"}, {"text": "Who is the author of the book, Across Many Mountains, published by Random House on 2010-11-09?"}, {"text": "Who is the author of the science fiction book, Small Changes, published by Doubleday on 1969-01-01?"}, {"text": "Who is the author of the book \"<PERSON>\" by <PERSON>, published on 2009-01-01?"}, {"text": "Who is the author of the novel, <PERSON>, published on 2009-01-01?"}, {"text": "Who is the author of the book, The Techniques of Democracy?"}, {"text": "Who is the author of the 1938 crime fiction novel, Death in Five Boxes, published by William Morrow and Company?"}, {"text": "Who is the author of the fantasy book, The Wizard in Wonderland, published by Candlewick Press on 1991-01-01?"}, {"text": "Who is the author of the science fiction novel titled Transcension, published by Tor Books on 2002-02-19?"}, {"text": "Who is the author of the non-fiction book, With Women, published by Australian College of Midwives on 2007-01-01?"}, {"text": "Who is the author of the play, Come On Over?"}, {"text": "Who is the author of the poetry book \"For a Living\", published by University of Illinois Press on 1995-09-01, as written by <PERSON>?"}, {"text": "Who is the author of the 2000 novel, <PERSON>, a fantasy book published by Random House on 2000-01-01?"}, {"text": "Who is the author of the thriller novel, <PERSON><PERSON>, published on 1996-09-01?"}, {"text": "Who is the author of the novel, <PERSON>, which was published on 2003-01-01?"}, {"text": "Who is the author of the graphic novel series, <PERSON>?"}, {"text": "Who is the author of the drama play, The Burning?"}, {"text": "Who is the author of the fantasy anime and manga series, The Sword of Shibito?"}, {"text": "Who is the author of the play, <PERSON><PERSON><PERSON><PERSON><PERSON>?"}, {"text": "Who is the author of the 2003 fantasy novel, The Aware, published by HarperCollins on 2003-10-29?"}, {"text": "Who is the author of the play, <PERSON>?"}, {"text": "Who is the author of the science fiction book, Science-Fantasy Quintette, published by Fantasy Publishing Company, Inc. on 1953-01-01?"}, {"text": "Who is the author of the novel, <PERSON>, published on 2007-01-01?"}, {"text": "Who is the author of the novel, Weekend, published on 2006-01-01?"}, {"text": "Who is the author of the collection of short stories titled Empire, published on 1981-01-01?"}, {"text": "Who is the author of the play, The Empire?"}, {"text": "Who is the author of the novel, One of the Family, published on 1993-01-01?"}, {"text": "Who is the author of the book, The Culture of Collaboration, published on 2007-01-01?"}, {"text": "Who is the author of the play, Old Money?"}, {"text": "Who is the author of the Tramelogedy play, <PERSON>?"}, {"text": "Who is the author of the mystery fiction book, Se<PERSON>r <PERSON>, published by Hodder & Stoughton on 1958-01-01?"}, {"text": "Who is the author of the novel, Het uur tussen hond en wolf, published on 1987-01-01?"}, {"text": "Who is the author of the original novel, <PERSON>, published by Black Flame on 2004-08-01?"}, {"text": "Who is the author of the comedy book, The Valley, published by Bloomsbury Publishing on 2005-01-01?"}, {"text": "Who is the author of the book Facing the Future by <PERSON>, published on 1998-07-01?"}, {"text": "Who is the author of the children's fairy tale, The Squirrel Wife, a piece of children's literature, published by Longman on 1971-01-01?"}, {"text": "Who is the author of the book, Moving Day, published by Highlights for Children on 2006-11-01?"}, {"text": "Who is the author of the book, Close to Home, published by <PERSON>, Sons on 1979-01-01?"}, {"text": "Who is the author of the science fiction book, The Chaos Code, published on 2007-01-01?"}, {"text": "Who is the author of the novel, August, published on 2001-01-01?"}, {"text": "Who is the author of the novel, <PERSON><PERSON>, published on 1997-01-01?"}, {"text": "Who is the author of the book, America's Secret War, by the founder of Stratfor, published by Doubleday on 2004-01-01?"}, {"text": "Who is the author of the play, <PERSON>?"}, {"text": "Who is the author of the 1904 novel, The Test, published by Charles <PERSON>'s Sons on 1904-01-01?"}, {"text": "Who is the author of the collection of short stories titled Darkness, published by Fawcett Publications on 1985-01-01?"}, {"text": "Who is the author of the book, Chelsea on the Edge, published on 1991-01-01?"}, {"text": "Who is the author of the 1890 play, Men and Women?"}, {"text": "Who is the author of the book \"One More Time\", published by Random House on 1986-01-01?"}, {"text": "Who is the author of the 1988 anthology, Unknown, a fantasy book published by Baen Books on 1988-01-01?"}, {"text": "Who is the author of the 1973 novel, <PERSON>, which was published on 1973-01-01?"}, {"text": "Who is the author of the fantasy book, Time to Come, published by Farrar, St<PERSON><PERSON> and Giro<PERSON> on 1954-04-01?"}, {"text": "Who is the author of the science fiction book, <PERSON><PERSON><PERSON>, published on 2008-01-01?"}, {"text": "Who is the author of the book titled 'American Dream, Global Nightmare', published on 2004-01-01?"}, {"text": "Who is the author of the 1998 play, <PERSON><PERSON>?"}, {"text": "Who is the author of the book, Neglected Aspects of Sufi Study, published by Octagon Press on 1977-01-01?"}, {"text": "Who is the author of the crime fiction novel, <PERSON>, published on 2005-01-01?"}, {"text": "Who is the author of the drama fiction novel, The Great Perhaps, published by W. W. Norton & Company on 2009-05-11?"}, {"text": "Who is the author of the book, The Universe Around Us?"}, {"text": "Who is the author of the military science fiction novel, Against the Odds, published by Baen Books on 2000-01-01?"}, {"text": "Who is the author of the novel, <PERSON><PERSON>, published on 2000-01-01?"}, {"text": "Who is the author of the 1976 book, New York?"}, {"text": "Who is the author of the book titled Challenge, published by Hodder & Stoughton on 1937-01-01?"}, {"text": "Who is the author of the novella, Dreams, which was published in 1904?"}, {"text": "Who is the author of the book, <PERSON>, published on 2008-01-01?"}, {"text": "Who is the author of the 1921 four-act comedy play, Nice People, belonging to the comedy genre?"}, {"text": "Who is the author of the novel, <PERSON>, published on 1994-01-01?"}, {"text": "Who is the author of the 1974 novel, Love All, published on 1974-01-01?"}, {"text": "Who is the author of the poem, The Hero?"}, {"text": "Who is the author of the book, The Sun Chemist, published by Jonathan Cape on 1976-01-01?"}, {"text": "Who is the author of the play, <PERSON><PERSON>'s Gift?"}, {"text": "Who is the author of the novel, <PERSON><PERSON><PERSON>, published on 2008-01-01?"}, {"text": "Who is the author of the science fiction short story, <PERSON>, published on 2007-04-01?"}, {"text": "Who is the author of the non-fiction book, The Economics and Ethics of Private Property, published by Springer Science+Business Media on 1993-01-01?"}, {"text": "Who is the author of the book, The Every Boy, published on 2005-01-01?"}, {"text": "Who is the author of the autobiography, The Middle Years, published by <PERSON>, Sons on 1917-01-01?"}, {"text": "Who is the author of the fantasy book, <PERSON><PERSON>, published by Viking Press on 2006-01-01?"}, {"text": "Who is the author of the book titled \"Embrace\", an LGBT-related literature, published by Hachette Book Group on 2001-02-01?"}, {"text": "Who is the author of the comic book series \"Resistance\", published by Wildstorm?"}, {"text": "Who is the author of the fantasy book, Into the Woods, published on 2006-01-01?"}, {"text": "Who is the author of the 1978 drama radio play, <PERSON>?"}, {"text": "Who is the author of the novel, Just a Matter of Time, published on 1973-01-01?"}, {"text": "Who is the author of the book titled Fruits, published on 1997-01-01?"}, {"text": "Who is the author of the book Shift, published by Crown Publishing Group on 2010-08-10?"}, {"text": "Who is the author of the collection of short stories titled \"Federation\", published by Ace Books on 1981-01-01?"}, {"text": "Who is the author of the crime novel, <PERSON>, published by Ballantine Books in 2004?"}, {"text": "Who is the author of the book, <PERSON><PERSON><PERSON> Chicken, published on 1979-01-01?"}, {"text": "Who is the author of the 2006 play, <PERSON>?"}, {"text": "Who is the author of the book, <PERSON><PERSON><PERSON><PERSON><PERSON>?"}, {"text": "Who is the author of the 2000 novel, Prime Time, published by BBC Books on 2000-07-01?"}, {"text": "Who is the author of the science fiction novel, Trust Territory, published by Roc Books on 1992-03-03?"}, {"text": "Who is the author of the Star Trek novel, Balance of Power, published on 1994-01-01?"}, {"text": "Who is the author of the 1935 drama play, <PERSON>, which was published on 1935-01-01?"}, {"text": "Who is the author of the book, The Lie, published on 1970-01-01?"}, {"text": "Who is the author of the crime fiction book, Skinner's Rules, published by Headline Publishing Group on 1993-01-01?"}, {"text": "Who is the author of the book, <PERSON><PERSON>t katten har djuren själ!, published on 1994-01-01?"}, {"text": "Who is the author of the science fiction short story, Let's Not, published on 1954-01-01?"}, {"text": "Who is the author of the novel \"Pursuit\" by <PERSON>, published on 2003-01-01?"}, {"text": "Who is the author of the play, Incoming?"}, {"text": "Who is the author of the sword and sorcery novel, <PERSON>, Lord of the Black River, published on 1996-04-01?"}, {"text": "Who is the author of the young adult novel, <PERSON>, a children's novel, published on 2006-06-29?"}, {"text": "Who is the author of the short story titled <PERSON>, published on 1999-01-01?"}, {"text": "Who is the author of the book titled <PERSON>, published on 2009-11-21?"}, {"text": "Who is the author of the 1997 audiobook titled \"Everything\", released on 1997-01-01?"}, {"text": "Who is the author of the biography memoir, Find <PERSON>, published on 2002-01-01?"}, {"text": "Who is the author of the drama anime and manga series, Partner?"}, {"text": "Who is the author of the play, The Ball?"}, {"text": "Who is the author of the book titled Suicide, published on 2000-01-01?"}, {"text": "Who is the author of the poetry book, <PERSON><PERSON>, published by Gotham Book Mart on 1973-01-01?"}, {"text": "Who is the author of the non-fiction book, On the Road, published on 2012-09-01?"}, {"text": "Who is the author of the short story, The Outing?"}, {"text": "Who is the mother of <PERSON>, the 9th century abbot of Saint-Denis?"}, {"text": "What is the capital of Ungheni County, located in Moldova?"}, {"text": "What is the capital of Gmina Secemin, a rural gmina of Poland?"}, {"text": "What is the capital of Yunguyo Province, a province of Puno, Peru?"}, {"text": "What is the capital of the French canton of Saint-Doulchard?"}, {"text": "What is the capital of the arrondissement of Castellane, an arrondissement of France?"}, {"text": "What is the capital of Sánchez Carrión Province, located in La Libertad, Peru?"}, {"text": "What is the capital of Chiprovtsi Municipality, located in Montana oblast, Bulgaria?"}, {"text": "What is the capital of the French canton of Antibes-Biot?"}, {"text": "What is the capital of the French canton of Harnes?"}, {"text": "What is the capital of the municipality on the island of Sal, Cape Verde?"}, {"text": "What is the capital of Kareličy District, a district of Belarus?"}, {"text": "What is the capital of Kambarsky District, a municipal district in Russia?"}, {"text": "What is the capital of Gmina Brzeszcze, an urban-rural gmina of Poland?"}, {"text": "What is the capital of Tarussky District, an administrative and municipal district in Russia?"}, {"text": "What is the capital of Gmina Czorsztyn, a rural gmina of Poland?"}, {"text": "What is the capital of Verbandsgemeinde Bad Ems, a Verbandsgemeinde in Rhineland-Palatinate, Germany?"}, {"text": "What was the capital of the canton of Gordes, which was a canton of France until March 2015?"}, {"text": "What is the capital of Gmina Andrespol, a rural gmina of Poland?"}, {"text": "What is the capital of Vozhegodsky District, a human settlement in Russia?"}, {"text": "What is the capital of arrondissement of Nogent-le-Rotrou, an arrondissement of France?"}, {"text": "What is the capital of the arrondissement of Lannion, an arrondissement of France?"}, {"text": "What is the capital of the canton of Mirambeau, which was a part of France until March 2015?"}, {"text": "What is the capital of Saanen District, a former district of the canton of Bern?"}, {"text": "What is the capital of Plaza, the second-level administrative division in the Miranda State, Venezuela?"}, {"text": "What is the capital of the French arrondissement of Florac?"}, {"text": "What is the capital of Yalutorovsky District, a human settlement in Russia?"}, {"text": "What is the capital of Gmina Osiecznica, a rural gmina of Poland?"}, {"text": "What is the capital of Gmina Radomsko, a rural gmina of Poland?"}, {"text": "What is the capital of Gmina Babice located in the Lesser Poland Voivodeship, Poland?"}]