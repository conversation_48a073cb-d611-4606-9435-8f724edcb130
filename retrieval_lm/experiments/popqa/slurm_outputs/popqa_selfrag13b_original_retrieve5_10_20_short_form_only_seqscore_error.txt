/opt/conda/bin/conda: line 3: import: command not found
/opt/conda/bin/conda: line 6: syntax error near unexpected token `sys.argv'
/opt/conda/bin/conda: line 6: `if len(sys.argv) > 1 and sys.argv[1].startswith('shell.') and sys.path and sys.path[0] == '':'
python: can't open file 'run_short_form.py': [Errno 2] No such file or directory
python: can't open file 'run_short_form.py': [Errno 2] No such file or directory
python: can't open file 'run_short_form.py': [Errno 2] No such file or directory
python: can't open file 'run_short_form.py': [Errno 2] No such file or directory
python: can't open file 'run_short_form.py': [Errno 2] No such file or directory
python: can't open file 'run_short_form.py': [Errno 2] No such file or directory
