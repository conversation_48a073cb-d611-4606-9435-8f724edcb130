from vllm import LLM, SamplingParams

model = LLM("selfrag/selfrag_llama2_13b", download_dir="/home/<USER>/model_cache", dtype="half")
sampling_params = SamplingParams(temperature=0.0, top_p=0.95, max_tokens=100, skip_special_tokens=False)

def format_prompt(input, paragraph=None):
    prompt = "### Instruction:\n{0}\n\n### Response:\n".format(input)
    if paragraph is not None:
        prompt += "[Retrieval]<paragraph>{0}</paragraph>".format(paragraph)
    return prompt

query_1 = "In what country is Givron?"
query_2 = "In what country is Normania Township?"
query_3 = "What is <PERSON>'s occupation?"
query_4 = "In what country is the community Vera in?"
query_5 = "In what city was <PERSON> born?"
query_6 = "Who was the producer of Strand?"
query_7 = "In what city was <PERSON>ek<PERSON><PERSON> born?"
query_8 = "What is the name of the screenwriter of The Bride\u2019s Journey?"
query_9 = "What is the religion of <PERSON><PERSON><PERSON>?"
query_10 = "In what country is Kuczynka?"

queries = [query_1, query_2, query_3, query_4, query_5, query_6, query_7, query_8, query_9, query_10]

# for a query that doesn't require retrieval
preds = model.generate([format_prompt(query) for query in queries], sampling_params)
for pred in preds:
    print("Model prediction: {0}".format(pred.outputs[0].text))
