from vllm import LLM, SamplingParams
import pandas as pd

# Define paths and model parameters
model_name = "selfrag/selfrag_llama2_13b"
download_dir = "/home/<USER>/model_cache"
input_file = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/long_tail_popqa.csv"
output_file = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/popqa_predictions_bfloat16.csv"

# Load the model
model = LLM(model_name, download_dir=download_dir)

# Define sampling parameters
sampling_params = SamplingParams(temperature=0.0, top_p=1.0, max_tokens=100, skip_special_tokens=True)

# Placeholder for retrieval (replace this with actual retrieval logic)
def retrieve_passages(question):
    # Retrieve relevant content from a database or other source
    retrieved_paragraph = "This is the retrieved content."
    return retrieved_paragraph

# Function to format the input for the model
def format_prompt(input, paragraph=None):
    prompt = "### Instruction:\n{0}\n\n### Response:\n".format(input)
    if paragraph is not None:
        prompt += "[Retrieval]<paragraph>{0}</paragraph>".format(paragraph)
    return prompt

# Load the dataset
df = pd.read_csv(input_file)

# Prepare to store results
results = []

# Iterate over the dataset and make predictions
for index, row in df.iterrows():
    question = row['question']
    
    # Retrieve relevant passages for the question
    retrieved_content = retrieve_passages(question)
    
    # Format the prompt including the retrieved content
    formatted_prompt = format_prompt(question, retrieved_content)
    
    # Generate predictions
    preds = model.generate([formatted_prompt], sampling_params)
    
    # Extract the prediction
    prediction = preds[0].outputs[0].text
    
    # Store the result along with the original data
    results.append({
        "question": question,
        "ground_truth": row['possible_answers'],
        "prediction": prediction,
        "retrieved_content": retrieved_content  # Store the retrieved content for debugging
    })

# Convert results to a DataFrame for easier analysis
results_df = pd.DataFrame(results)

# Save the results to a CSV file
results_df.to_csv(output_file, index=False)
