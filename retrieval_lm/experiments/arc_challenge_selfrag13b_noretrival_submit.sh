#!/bin/bash
#SBATCH --job-name=arc_challenge_selfrag13B
#SBATCH --output=arc_challenge_selfrag_13B_output.txt
#SBATCH --error=arc_challenge_selfrag_13B_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=02:00:00
#SBATCH --partition=ampere 

# Activate conda environment
source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

# Run the Python script
python arc_challenge_test_selfrag13b_noretrieval.py

