WARNING 07-17 16:07:37 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 07-17 16:07:37 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 07-17 16:08:14 llm_engine.py:223] # GPU blocks: 1929, # CPU blocks: 512
INFO 07-17 16:08:16 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 07-17 16:08:22 model_runner.py:437] Graph capturing finished in 5 secs.
Model prediction: Devils on Horseback.[Utility:5]</s>
Model prediction: Vesuvius[Utility:5]</s>
Model prediction: <PERSON>[Utility:5]</s>
Model prediction: Nadine Dorries[Utility:5]</s>
Model prediction: Sugar syrup[Utility:5]</s>
Model prediction: Dead Belgians Don’t Count[Utility:5]</s>
Model prediction: Senator Joe McCarthly represented which state?[Utility:5]</s>
Model prediction: Gamp (disambiguation)[Utility:5]</s>
Model prediction: Universalism[Utility:5]</s>
Model prediction: pouch[Utility:5]</s>
