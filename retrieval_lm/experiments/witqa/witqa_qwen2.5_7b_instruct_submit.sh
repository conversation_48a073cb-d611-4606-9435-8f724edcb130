#!/bin/bash
#SBATCH --job-name=witqa_qwen2_7b_test
#SBATCH --output=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/slurm_outputs/witqa_qwen2_7b_test_%j.out
#SBATCH --error=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/slurm_outputs/witqa_qwen2_7b_test_%j.err
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=12:00:00
#SBATCH --partition=ampere

# Activate conda environment
source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name Qwen/Qwen2.5-7B-Instruct \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_original_retrieve20_contrievermsm.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_original_retrieve10_contrievermsm_qwen2_7b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 10 \
    --prompt_name "prompt_no_input_retrieval" \
    --download_dir /home/<USER>/.cache/huggingface

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name Qwen/Qwen2.5-7B-Instruct \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_original_retrieve20_contrievermsm.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_original_retrieve15_contrievermsm_qwen2_7b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 15 \
    --prompt_name "prompt_no_input_retrieval" \
    --download_dir /home/<USER>/.cache/huggingface

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name Qwen/Qwen2.5-7B-Instruct \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_original_retrieve20_contrievermsm.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_original_retrieve20_contrievermsm_qwen2_7b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 20 \
    --prompt_name "prompt_no_input_retrieval" \
    --download_dir /home/<USER>/.cache/huggingface

# Disambig version with different ndocs (5, 10, 15, 20)
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name Qwen/Qwen2.5-7B-Instruct \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disambig_retrieve5_contrievermsm_qwen2_7b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 5 \
    --prompt_name "prompt_no_input_retrieval" \
    --download_dir /home/<USER>/.cache/huggingface

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name Qwen/Qwen2.5-7B-Instruct \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disambig_retrieve10_contrievermsm_qwen2_7b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 10 \
    --prompt_name "prompt_no_input_retrieval" \
    --download_dir /home/<USER>/.cache/huggingface

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name Qwen/Qwen2.5-7B-Instruct \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disambig_retrieve15_contrievermsm_qwen2_7b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 15 \
    --prompt_name "prompt_no_input_retrieval" \
    --download_dir /home/<USER>/.cache/huggingface

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name Qwen/Qwen2.5-7B-Instruct \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disambig_retrieve20_contrievermsm_qwen2_7b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 20 \
    --prompt_name "prompt_no_input_retrieval" \
    --download_dir /home/<USER>/.cache/huggingface