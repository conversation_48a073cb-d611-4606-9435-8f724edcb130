#!/bin/bash
#SBATCH --job-name=witqa_baseline_disambig
#SBATCH --output=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/slurm_outputs/witqa_baseline_disambig_%j.out
#SBATCH --error=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/slurm_outputs/witqa_baseline_disambig_%j.err
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=24:00:00
#SBATCH --partition=ampere

source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

# Run Llama2-7B on disambiguated WitQA with different ndocs
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-7b-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_baseline_disambig_retrieve5_contrievermsm_llama2_7b_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 5 \
    --prompt_name "llama_chat_prompt_retrieval"

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-7b-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_baseline_disambig_retrieve10_contrievermsm_llama2_7b_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 10 \
    --prompt_name "llama_chat_prompt_retrieval"

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-7b-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_baseline_disambig_retrieve15_contrievermsm_llama2_7b_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 15 \
    --prompt_name "llama_chat_prompt_retrieval"

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-7b-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_baseline_disambig_retrieve20_contrievermsm_llama2_7b_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 20 \
    --prompt_name "llama_chat_prompt_retrieval"

# Run Llama2-13B on disambiguated WitQA with different ndocs
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-13b-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_baseline_disambig_retrieve5_contrievermsm_llama2_13b_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 5 \
    --prompt_name "llama_chat_prompt_retrieval"

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-13b-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_baseline_disambig_retrieve10_contrievermsm_llama2_13b_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 10 \
    --prompt_name "llama_chat_prompt_retrieval"

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-13b-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_baseline_disambig_retrieve15_contrievermsm_llama2_13b_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 15 \
    --prompt_name "llama_chat_prompt_retrieval"

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-13b-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_baseline_disambig_retrieve20_contrievermsm_llama2_13b_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 20 \
    --prompt_name "llama_chat_prompt_retrieval"