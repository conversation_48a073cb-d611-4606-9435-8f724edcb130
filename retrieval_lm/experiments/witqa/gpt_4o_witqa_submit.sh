#!/bin/bash
#SBATCH --job-name=witqa_gpt4o
#SBATCH --output=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/slurm_outputs/witqa_gpt4o_%j.out
#SBATCH --error=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/slurm_outputs/witqa_gpt4o_%j.err
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --partition=ampere
#SBATCH --nodes=1
#SBATCH --gpus-per-node=1
#SBATCH --time=48:00:00

# Activate conda environment
source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

# Run the GPT-4o script
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/gpt4o_witqa.py