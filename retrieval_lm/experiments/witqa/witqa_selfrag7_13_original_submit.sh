#!/bin/bash
#SBATCH --job-name=witqa_SR7B_SR13B_orig
#SBATCH --output=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/slurm_outputs/witqa_SR7B_SR13B_orig__%j.out
#SBATCH --error=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/slurm_outputs/witqa_SR7B_SR13B_orig__%j.err
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=24:00:00
#SBATCH --partition=ampere  

# Activate conda environment
source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

# Run SelfRAG-7B on original WitQA with different ndocs
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name selfrag/selfrag_llama2_7b \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_original_retrieve10_contrievermsm_selfrag7b_results.jsonl \
    --metric match --ndocs 10 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name selfrag/selfrag_llama2_7b \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_original_retrieve15_contrievermsm_selfrag7b_results.jsonl \
    --metric match --ndocs 15 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name selfrag/selfrag_llama2_7b \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_original_retrieve20_contrievermsm_selfrag7b_results.jsonl \
    --metric match --ndocs 20 --use_groundness --use_utility --use_seqscore \
    --dtype half

# Run SelfRAG-13B on original WitQA with different ndocs
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name selfrag/selfrag_llama2_13b \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_original_retrieve10_contrievermsm_selfrag13b_results.jsonl \
    --metric match --ndocs 10 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name selfrag/selfrag_llama2_13b \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_original_retrieve15_contrievermsm_selfrag13b_results.jsonl \
    --metric match --ndocs 15 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name selfrag/selfrag_llama2_13b \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_original_retrieve20_contrievermsm_selfrag13b_results.jsonl \
    --metric match --ndocs 20 --use_groundness --use_utility --use_seqscore \
    --dtype half

