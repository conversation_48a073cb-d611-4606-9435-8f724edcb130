#!/bin/bash
#SBATCH --job-name=witqa_multi_model
#SBATCH --output=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/slurm_outputs/witqa_multi_model_%j.out
#SBATCH --error=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/slurm_outputs/witqa_multi_model_%j.err
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=24:00:00
#SBATCH --partition=ampere

# Activate conda environment
source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

# Llama2-13B with ndocs=1
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-13b-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disamb_gold.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disamb_gold_retrieve1_llama2_13b_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 1 \
    --prompt_name "prompt_no_input_retrieval" \
    --download_dir "/home/<USER>/model_cache"

# Llama2-13B with ndocs=6
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-13b-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disamb_gold.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disamb_gold_retrieve6_llama2_13b_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 6 \
    --prompt_name "prompt_no_input_retrieval" \
    --download_dir "/home/<USER>/model_cache"

# Llama2-13B-Instruct with ndocs=1
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-13b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disamb_gold.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disamb_gold_retrieve1_llama2_13b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 1 \
    --prompt_name "prompt_no_input_retrieval" \
    --download_dir "/home/<USER>/model_cache"

# Llama2-13B-Instruct with ndocs=6
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-13b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disamb_gold.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disamb_gold_retrieve6_llama2_13b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 6 \
    --prompt_name "prompt_no_input_retrieval" \
    --download_dir "/home/<USER>/model_cache"

# Qwen2.5-7B-Instruct with ndocs=1
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name Qwen/Qwen2.5-7B-Instruct \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disamb_gold.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disamb_gold_retrieve1_qwen2_7b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 1 \
    --prompt_name "prompt_no_input_retrieval" \
    --download_dir "/home/<USER>/model_cache"

# Qwen2.5-7B-Instruct with ndocs=6
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name Qwen/Qwen2.5-7B-Instruct \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disamb_gold.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disamb_gold_retrieve6_qwen2_7b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 6 \
    --prompt_name "prompt_no_input_retrieval" \
    --download_dir "/home/<USER>/model_cache"
