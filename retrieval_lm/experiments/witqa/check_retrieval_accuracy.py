import pandas as pd
import json
import ast

def check_retrieval_success(csv_file, json_file):
    """
    Check if the top 5, 10, 15, 20 retrieved documents contain any of the possible answers.
    The possible answers are taken from the CSV file.
    """
    # Load the CSV into a DataFrame
    df = pd.read_csv(csv_file)

    # Initialize lists to store the results for top 5, 10, 15, and 20
    top5_results = []
    top10_results = []
    top15_results = []
    top20_results = []

    # Function to check if any retrieved document contains the answer
    def check_retrieval(ctxs, answers, top_k):
        answers_lower = [answer.lower() for answer in answers]  # Convert answers to lowercase
        for ctx in ctxs[:top_k]:  # Look at the top_k retrieved documents
            text_lower = ctx["text"].lower()  # Convert text to lowercase
            if any(answer in text_lower for answer in answers_lower):  # Check for substring match
                return True
        return False

    # Load the entire JSON file
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # Extract the list of records from the "results" key
    records = data

    # Iterate over each record in the loaded JSON data, matching it with the corresponding row in CSV
    for i, record in enumerate(records):
        # Extract possible answers from the CSV for the corresponding row
        possible_answers_str = df.loc[i, 'expanded_object_label']

        # Convert the string representation of the list into an actual list
        answers = ast.literal_eval(possible_answers_str)

        # Access the retrieved documents from the current record
        ctxs = record["ctxs"]

        # Check for matches in top 5, top 10, top 15, and top 20 documents
        top5_results.append(check_retrieval(ctxs, answers, top_k=5))
        top10_results.append(check_retrieval(ctxs, answers, top_k=10))
        top15_results.append(check_retrieval(ctxs, answers, top_k=15))
        top20_results.append(check_retrieval(ctxs, answers, top_k=20))

    # Add the results to the DataFrame
    df['disambig_contrievermsm_2020_top5_contains_answer'] = top5_results
    df['disambig_contrievermsm_2020_top10_contains_answer'] = top10_results
    df['disambig_contrievermsm_2020_top15_contains_answer'] = top15_results
    df['disambig_contrievermsm_2020_top20_contains_answer'] = top20_results

    # Save the updated DataFrame back to the CSV file
    df.to_csv(csv_file, index=False, encoding='utf-8')

    print(f"Columns for top 5, 10, 15, and 20 retrieval results have been appended to {csv_file}")

# File paths
csv_file = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail.csv"
# json_file = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_original_retrieve20_contrievermsm.json"
json_file = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json"

check_retrieval_success(csv_file, json_file)
