#!/bin/bash
#SBATCH --job-name=witqa_L2I7B_L2I13B_disambig
#SBATCH --output=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/slurm_outputs/witqa_L2I7B_L2I13B_disambig_%j.out
#SBATCH --error=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/slurm_outputs/witqa_L2I7B_L2I13B_disambig_%j.err
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=24:00:00
#SBATCH --partition=ampere  

# Activate conda environment
source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

# Run Llama2-7B-Instruct on disambiguated WitQA with different ndocs
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-7b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disambiguated_retrieve5_contrievermsm_llama2_7b_instruct_results.jsonl \
    --metric match --ndocs 5 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-7b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disambiguated_retrieve10_contrievermsm_llama2_7b_instruct_results.jsonl \
    --metric match --ndocs 10 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-7b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disambiguated_retrieve15_contrievermsm_llama2_7b_instruct_results.jsonl \
    --metric match --ndocs 15 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-7b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disambiguated_retrieve20_contrievermsm_llama2_7b_instruct_results.jsonl \
    --metric match --ndocs 20 --use_groundness --use_utility --use_seqscore \
    --dtype half

# Run Llama2-13B-Instruct on disambiguated WitQA with different ndocs
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-13b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disambiguated_retrieve5_contrievermsm_llama2_13b_instruct_results.jsonl \
    --metric match --ndocs 5 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-13b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disambiguated_retrieve10_contrievermsm_llama2_13b_instruct_results.jsonl \
    --metric match --ndocs 10 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-13b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disambiguated_retrieve15_contrievermsm_llama2_13b_instruct_results.jsonl \
    --metric match --ndocs 15 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-13b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_disambiguated_retrieve20_contrievermsm_llama2_13b_instruct_results.jsonl \
    --metric match --ndocs 20 --use_groundness --use_utility --use_seqscore \
    --dtype half