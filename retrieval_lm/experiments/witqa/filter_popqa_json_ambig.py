import pandas as pd
import json

def filter_predictions(csv_path, json_path, output_json_path):
    """
    Filters JSON predictions to keep only those corresponding to CSV rows 
    where relevant_entity_count > 1
    """
    # Read the CSV file
    df = pd.read_csv(csv_path)
    
    # Get indices where relevant_entity_count > 1
    filtered_indices = df[df['relevant_entity_count'] > 1].index.tolist()
    
    # Read the JSON file
    with open(json_path, 'r') as f:
        json_data = json.load(f)
    
    # Check if JSON is in the expected format (with "preds" key)
    if isinstance(json_data, dict) and "preds" in json_data:
        filtered_preds = [json_data["preds"][i] for i in filtered_indices]
        filtered_json = {"preds": filtered_preds}
    else:
        # Assume it's a list or other structure
        filtered_json = [json_data[i] for i in filtered_indices]
    
    # Save filtered predictions
    with open(output_json_path, 'w') as f:
        json.dump(filtered_json, f, indent=2)
    
    print(f"Original number of predictions: {len(df)}")
    print(f"Number of filtered predictions: {len(filtered_indices)}")
    print(f"Filtered predictions saved to: {output_json_path}")

# Example usage:
csv_path = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail.csv"
json_path = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_contrievermsm_retrieved_top20.json"
output_json_path = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_disambig_subset_retrieve20_contrievermsm.json"

filter_predictions(csv_path, json_path, output_json_path)