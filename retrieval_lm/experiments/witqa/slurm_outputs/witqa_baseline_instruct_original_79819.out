==========================================
SLURM_JOB_ID = 79819
SLURM_NODELIST = gpunode03
==========================================
WARNING 04-23 19:12:01 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 04-23 19:12:11 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-7b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-7b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-7b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 04-23 19:12:13 model_runner.py:1056] Starting to load model meta-llama/Llama-2-7b-chat-hf...
INFO 04-23 19:12:14 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 04-23 19:12:48 model_runner.py:1067] Loading model weights took 12.5523 GB
INFO 04-23 19:12:49 gpu_executor.py:122] # GPU blocks: 2879, # CPU blocks: 512
INFO 04-23 19:12:49 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 11.25x
INFO 04-23 19:12:52 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-23 19:12:52 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-23 19:13:04 model_runner.py:1523] Graph capturing finished in 12 secs.
overall result: 0.0
WARNING 04-23 19:24:05 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 04-23 19:24:15 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-7b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-7b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-7b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 04-23 19:24:17 model_runner.py:1056] Starting to load model meta-llama/Llama-2-7b-chat-hf...
INFO 04-23 19:24:17 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 04-23 19:24:20 model_runner.py:1067] Loading model weights took 12.5523 GB
INFO 04-23 19:24:21 gpu_executor.py:122] # GPU blocks: 2879, # CPU blocks: 512
INFO 04-23 19:24:21 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 11.25x
INFO 04-23 19:24:25 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-23 19:24:25 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-23 19:24:38 model_runner.py:1523] Graph capturing finished in 12 secs.
overall result: 0.0
WARNING 04-23 19:38:31 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 04-23 19:38:39 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-7b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-7b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-7b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 04-23 19:38:41 model_runner.py:1056] Starting to load model meta-llama/Llama-2-7b-chat-hf...
INFO 04-23 19:38:41 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 04-23 19:38:43 model_runner.py:1067] Loading model weights took 12.5523 GB
INFO 04-23 19:38:44 gpu_executor.py:122] # GPU blocks: 2879, # CPU blocks: 512
INFO 04-23 19:38:44 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 11.25x
INFO 04-23 19:38:47 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-23 19:38:47 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-23 19:39:02 model_runner.py:1523] Graph capturing finished in 16 secs.
WARNING 04-23 19:44:14 scheduler.py:895] Input prompt (4319 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:50:42 scheduler.py:895] Input prompt (4231 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:51:04 scheduler.py:895] Input prompt (4411 tokens) is too long and exceeds limit of 4096
overall result: 0.0
WARNING 04-23 19:56:09 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 04-23 19:56:17 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-7b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-7b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-7b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 04-23 19:56:19 model_runner.py:1056] Starting to load model meta-llama/Llama-2-7b-chat-hf...
INFO 04-23 19:56:19 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 04-23 19:56:22 model_runner.py:1067] Loading model weights took 12.5523 GB
INFO 04-23 19:56:23 gpu_executor.py:122] # GPU blocks: 2879, # CPU blocks: 512
INFO 04-23 19:56:23 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 11.25x
INFO 04-23 19:56:25 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-23 19:56:25 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-23 19:56:37 model_runner.py:1523] Graph capturing finished in 12 secs.
WARNING 04-23 19:56:38 scheduler.py:895] Input prompt (4837 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:56:54 scheduler.py:895] Input prompt (4156 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:10 scheduler.py:895] Input prompt (4272 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:13 scheduler.py:895] Input prompt (4177 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:16 scheduler.py:895] Input prompt (4495 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:16 scheduler.py:895] Input prompt (4261 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:19 scheduler.py:895] Input prompt (4251 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:21 scheduler.py:895] Input prompt (5156 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:21 scheduler.py:895] Input prompt (4371 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:29 scheduler.py:895] Input prompt (4153 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:40 scheduler.py:895] Input prompt (4449 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:47 scheduler.py:895] Input prompt (4367 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:02 scheduler.py:895] Input prompt (4936 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:09 scheduler.py:895] Input prompt (4106 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:17 scheduler.py:895] Input prompt (4421 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:18 scheduler.py:895] Input prompt (4401 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:18 scheduler.py:895] Input prompt (4534 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:20 scheduler.py:895] Input prompt (4236 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:20 scheduler.py:895] Input prompt (4743 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:25 scheduler.py:895] Input prompt (4161 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:31 scheduler.py:895] Input prompt (4556 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:36 scheduler.py:895] Input prompt (4621 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:59:00 scheduler.py:895] Input prompt (4253 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:59:00 scheduler.py:895] Input prompt (4695 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:59:33 scheduler.py:895] Input prompt (4653 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:59:39 scheduler.py:895] Input prompt (4277 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:59:41 scheduler.py:895] Input prompt (4281 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:59:56 scheduler.py:895] Input prompt (4708 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:02 scheduler.py:895] Input prompt (4615 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:13 scheduler.py:895] Input prompt (4569 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:27 scheduler.py:895] Input prompt (4130 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:27 scheduler.py:895] Input prompt (4533 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:34 scheduler.py:895] Input prompt (4135 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:34 scheduler.py:895] Input prompt (4182 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:36 scheduler.py:895] Input prompt (4249 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:44 scheduler.py:895] Input prompt (4201 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:46 scheduler.py:895] Input prompt (4703 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:46 scheduler.py:895] Input prompt (4298 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:54 scheduler.py:895] Input prompt (4210 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:57 scheduler.py:895] Input prompt (4107 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:01:08 scheduler.py:895] Input prompt (4354 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:01:12 scheduler.py:895] Input prompt (4446 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:01:15 scheduler.py:895] Input prompt (4099 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:01:16 scheduler.py:895] Input prompt (4193 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:01:18 scheduler.py:895] Input prompt (4563 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:01:43 scheduler.py:895] Input prompt (4598 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:01:49 scheduler.py:895] Input prompt (4317 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:01:52 scheduler.py:895] Input prompt (4229 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:02:11 scheduler.py:895] Input prompt (4995 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:02:21 scheduler.py:895] Input prompt (4602 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:02:23 scheduler.py:895] Input prompt (4422 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:02:52 scheduler.py:895] Input prompt (4314 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:02:53 scheduler.py:895] Input prompt (4184 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:02:53 scheduler.py:895] Input prompt (4632 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:02:57 scheduler.py:895] Input prompt (4768 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:03:12 scheduler.py:895] Input prompt (4459 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:03:14 scheduler.py:895] Input prompt (4155 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:03:26 scheduler.py:895] Input prompt (4155 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:03:28 scheduler.py:895] Input prompt (4198 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:03:43 scheduler.py:895] Input prompt (4125 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:03:48 scheduler.py:895] Input prompt (4679 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:04:25 scheduler.py:895] Input prompt (4749 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:04:27 scheduler.py:895] Input prompt (4124 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:04:33 scheduler.py:895] Input prompt (4112 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:04:33 scheduler.py:895] Input prompt (4249 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:04:41 scheduler.py:895] Input prompt (4360 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:04:49 scheduler.py:895] Input prompt (4454 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:04:51 scheduler.py:895] Input prompt (4239 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:05:24 scheduler.py:895] Input prompt (4162 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:05:37 scheduler.py:895] Input prompt (4209 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:05:53 scheduler.py:895] Input prompt (4138 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:05:53 scheduler.py:895] Input prompt (4183 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:06:08 scheduler.py:895] Input prompt (4325 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:06:09 scheduler.py:895] Input prompt (4111 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:06:10 scheduler.py:895] Input prompt (4381 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:06:17 scheduler.py:895] Input prompt (4482 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:06:30 scheduler.py:895] Input prompt (4344 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:10 scheduler.py:895] Input prompt (4267 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:10 scheduler.py:895] Input prompt (4215 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:18 scheduler.py:895] Input prompt (4234 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:22 scheduler.py:895] Input prompt (4099 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:27 scheduler.py:895] Input prompt (4611 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:41 scheduler.py:895] Input prompt (4175 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:47 scheduler.py:895] Input prompt (4413 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:50 scheduler.py:895] Input prompt (4241 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:50 scheduler.py:895] Input prompt (4306 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:50 scheduler.py:895] Input prompt (4594 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:55 scheduler.py:895] Input prompt (4166 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:56 scheduler.py:895] Input prompt (4291 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:59 scheduler.py:895] Input prompt (4305 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:08:04 scheduler.py:895] Input prompt (4259 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:08:06 scheduler.py:895] Input prompt (4206 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:08:18 scheduler.py:895] Input prompt (4809 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:08:28 scheduler.py:895] Input prompt (4441 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:08:33 scheduler.py:895] Input prompt (4427 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:08:49 scheduler.py:895] Input prompt (4260 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:08:51 scheduler.py:895] Input prompt (4527 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:00 scheduler.py:895] Input prompt (4452 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:18 scheduler.py:895] Input prompt (4841 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:23 scheduler.py:895] Input prompt (4297 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:28 scheduler.py:895] Input prompt (4655 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:31 scheduler.py:895] Input prompt (4303 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:31 scheduler.py:895] Input prompt (5863 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:42 scheduler.py:895] Input prompt (4285 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:45 scheduler.py:895] Input prompt (4425 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:45 scheduler.py:895] Input prompt (5014 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:47 scheduler.py:895] Input prompt (4214 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:55 scheduler.py:895] Input prompt (5292 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:57 scheduler.py:895] Input prompt (4745 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:10:03 scheduler.py:895] Input prompt (4110 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:10:16 scheduler.py:895] Input prompt (4421 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:10:20 scheduler.py:895] Input prompt (4753 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:10:26 scheduler.py:895] Input prompt (4159 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:10:35 scheduler.py:895] Input prompt (4302 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:10:36 scheduler.py:895] Input prompt (4214 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:10:49 scheduler.py:895] Input prompt (4646 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:11:10 scheduler.py:895] Input prompt (4733 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:11:28 scheduler.py:895] Input prompt (4204 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:11:30 scheduler.py:895] Input prompt (4666 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:11:39 scheduler.py:895] Input prompt (4347 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:11:40 scheduler.py:895] Input prompt (4234 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:11:47 scheduler.py:895] Input prompt (4154 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:11:47 scheduler.py:895] Input prompt (4528 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:12:06 scheduler.py:895] Input prompt (4383 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:12:09 scheduler.py:895] Input prompt (4248 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:12:09 scheduler.py:895] Input prompt (4431 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:12:29 scheduler.py:895] Input prompt (4471 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:12:34 scheduler.py:895] Input prompt (4673 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:12:45 scheduler.py:895] Input prompt (4240 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:12:47 scheduler.py:895] Input prompt (4264 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:13:10 scheduler.py:895] Input prompt (4147 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:13:30 scheduler.py:895] Input prompt (4222 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:13:47 scheduler.py:895] Input prompt (4111 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:13:59 scheduler.py:895] Input prompt (4462 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:13:59 scheduler.py:895] Input prompt (4130 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:07 scheduler.py:895] Input prompt (4497 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:13 scheduler.py:895] Input prompt (4184 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:13 scheduler.py:895] Input prompt (4122 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:17 scheduler.py:895] Input prompt (4732 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:33 scheduler.py:895] Input prompt (4142 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:36 scheduler.py:895] Input prompt (4218 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:39 scheduler.py:895] Input prompt (4230 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:42 scheduler.py:895] Input prompt (5097 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:42 scheduler.py:895] Input prompt (4298 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:50 scheduler.py:895] Input prompt (4802 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:15:08 scheduler.py:895] Input prompt (4196 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:15:10 scheduler.py:895] Input prompt (4660 tokens) is too long and exceeds limit of 4096
overall result: 0.0
WARNING 04-23 20:15:36 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 04-23 20:15:44 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 04-23 20:15:46 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-chat-hf...
INFO 04-23 20:15:46 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 04-23 20:16:44 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 04-23 20:16:45 gpu_executor.py:122] # GPU blocks: 873, # CPU blocks: 327
INFO 04-23 20:16:45 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 3.41x
INFO 04-23 20:16:48 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-23 20:16:48 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-23 20:17:03 model_runner.py:1523] Graph capturing finished in 14 secs.
overall result: 0.0
WARNING 04-23 20:34:16 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 04-23 20:34:25 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 04-23 20:34:26 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-chat-hf...
INFO 04-23 20:34:26 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 04-23 20:34:32 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 04-23 20:34:32 gpu_executor.py:122] # GPU blocks: 873, # CPU blocks: 327
INFO 04-23 20:34:32 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 3.41x
INFO 04-23 20:34:36 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-23 20:34:36 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-23 20:34:53 model_runner.py:1523] Graph capturing finished in 17 secs.
overall result: 0.0
WARNING 04-23 20:57:19 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 04-23 20:57:27 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 04-23 20:57:29 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-chat-hf...
INFO 04-23 20:57:29 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 04-23 20:57:33 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 04-23 20:57:34 gpu_executor.py:122] # GPU blocks: 873, # CPU blocks: 327
INFO 04-23 20:57:34 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 3.41x
INFO 04-23 20:57:38 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-23 20:57:38 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-23 20:57:51 model_runner.py:1523] Graph capturing finished in 13 secs.
WARNING 04-23 21:06:18 scheduler.py:895] Input prompt (4319 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:17:16 scheduler.py:895] Input prompt (4231 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:17:49 scheduler.py:895] Input prompt (4411 tokens) is too long and exceeds limit of 4096
overall result: 0.0
WARNING 04-23 21:26:12 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 04-23 21:26:24 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 04-23 21:26:25 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-chat-hf...
INFO 04-23 21:26:26 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 04-23 21:26:30 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 04-23 21:26:31 gpu_executor.py:122] # GPU blocks: 873, # CPU blocks: 327
INFO 04-23 21:26:31 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 3.41x
INFO 04-23 21:26:34 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-23 21:26:34 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-23 21:26:47 model_runner.py:1523] Graph capturing finished in 13 secs.
WARNING 04-23 21:26:50 scheduler.py:895] Input prompt (4837 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:27:22 scheduler.py:895] Input prompt (4156 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:27:52 scheduler.py:895] Input prompt (4272 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:28:00 scheduler.py:895] Input prompt (4177 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:28:04 scheduler.py:895] Input prompt (4495 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:28:04 scheduler.py:895] Input prompt (4261 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:28:10 scheduler.py:895] Input prompt (4251 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:28:13 scheduler.py:895] Input prompt (5156 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:28:14 scheduler.py:895] Input prompt (4371 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:28:28 scheduler.py:895] Input prompt (4153 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:28:47 scheduler.py:895] Input prompt (4449 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:28:59 scheduler.py:895] Input prompt (4367 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:29:26 scheduler.py:895] Input prompt (4936 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:29:38 scheduler.py:895] Input prompt (4106 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:29:52 scheduler.py:895] Input prompt (4421 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:29:54 scheduler.py:895] Input prompt (4401 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:29:54 scheduler.py:895] Input prompt (4534 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:29:56 scheduler.py:895] Input prompt (4236 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:29:56 scheduler.py:895] Input prompt (4743 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:30:04 scheduler.py:895] Input prompt (4161 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:30:15 scheduler.py:895] Input prompt (4556 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:30:26 scheduler.py:895] Input prompt (4621 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:31:05 scheduler.py:895] Input prompt (4253 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:31:06 scheduler.py:895] Input prompt (4695 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:32:03 scheduler.py:895] Input prompt (4653 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:32:15 scheduler.py:895] Input prompt (4277 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:32:17 scheduler.py:895] Input prompt (4281 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:32:40 scheduler.py:895] Input prompt (4708 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:32:50 scheduler.py:895] Input prompt (4615 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:33:09 scheduler.py:895] Input prompt (4569 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:33:32 scheduler.py:895] Input prompt (4130 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:33:32 scheduler.py:895] Input prompt (4533 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:33:45 scheduler.py:895] Input prompt (4135 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:33:45 scheduler.py:895] Input prompt (4182 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:33:47 scheduler.py:895] Input prompt (4249 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:34:01 scheduler.py:895] Input prompt (4201 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:34:02 scheduler.py:895] Input prompt (4703 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:34:02 scheduler.py:895] Input prompt (4298 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:34:15 scheduler.py:895] Input prompt (4210 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:34:19 scheduler.py:895] Input prompt (4107 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:34:40 scheduler.py:895] Input prompt (4354 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:34:48 scheduler.py:895] Input prompt (4446 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:34:54 scheduler.py:895] Input prompt (4099 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:34:54 scheduler.py:895] Input prompt (4193 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:34:56 scheduler.py:895] Input prompt (4563 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:35:42 scheduler.py:895] Input prompt (4598 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:35:53 scheduler.py:895] Input prompt (4317 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:35:58 scheduler.py:895] Input prompt (4229 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:36:32 scheduler.py:895] Input prompt (4995 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:36:51 scheduler.py:895] Input prompt (4602 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:36:54 scheduler.py:895] Input prompt (4422 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:37:44 scheduler.py:895] Input prompt (4314 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:37:45 scheduler.py:895] Input prompt (4184 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:37:45 scheduler.py:895] Input prompt (4632 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:37:51 scheduler.py:895] Input prompt (4768 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:38:17 scheduler.py:895] Input prompt (4459 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:38:20 scheduler.py:895] Input prompt (4155 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:38:42 scheduler.py:895] Input prompt (4155 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:38:47 scheduler.py:895] Input prompt (4198 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:39:13 scheduler.py:895] Input prompt (4125 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:39:21 scheduler.py:895] Input prompt (4679 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:40:27 scheduler.py:895] Input prompt (4749 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:40:31 scheduler.py:895] Input prompt (4124 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:40:40 scheduler.py:895] Input prompt (4112 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:40:41 scheduler.py:895] Input prompt (4249 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:40:55 scheduler.py:895] Input prompt (4360 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:41:11 scheduler.py:895] Input prompt (4454 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:41:15 scheduler.py:895] Input prompt (4239 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:42:12 scheduler.py:895] Input prompt (4162 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:42:36 scheduler.py:895] Input prompt (4209 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:43:04 scheduler.py:895] Input prompt (4138 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:43:04 scheduler.py:895] Input prompt (4183 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:43:32 scheduler.py:895] Input prompt (4325 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:43:32 scheduler.py:895] Input prompt (4111 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:43:33 scheduler.py:895] Input prompt (4381 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:43:43 scheduler.py:895] Input prompt (4482 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:44:05 scheduler.py:895] Input prompt (4344 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:45:17 scheduler.py:895] Input prompt (4267 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:45:18 scheduler.py:895] Input prompt (4215 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:45:30 scheduler.py:895] Input prompt (4234 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:45:35 scheduler.py:895] Input prompt (4099 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:45:44 scheduler.py:895] Input prompt (4611 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:09 scheduler.py:895] Input prompt (4175 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:22 scheduler.py:895] Input prompt (4413 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:26 scheduler.py:895] Input prompt (4241 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:26 scheduler.py:895] Input prompt (4306 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:26 scheduler.py:895] Input prompt (4594 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:35 scheduler.py:895] Input prompt (4166 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:36 scheduler.py:895] Input prompt (4291 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:43 scheduler.py:895] Input prompt (4305 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:51 scheduler.py:895] Input prompt (4259 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:56 scheduler.py:895] Input prompt (4206 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:47:18 scheduler.py:895] Input prompt (4809 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:47:37 scheduler.py:895] Input prompt (4441 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:47:48 scheduler.py:895] Input prompt (4427 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:48:15 scheduler.py:895] Input prompt (4260 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:48:17 scheduler.py:895] Input prompt (4527 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:48:33 scheduler.py:895] Input prompt (4452 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:04 scheduler.py:895] Input prompt (4841 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:13 scheduler.py:895] Input prompt (4297 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:23 scheduler.py:895] Input prompt (4655 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:28 scheduler.py:895] Input prompt (4303 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:30 scheduler.py:895] Input prompt (5863 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:47 scheduler.py:895] Input prompt (4285 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:52 scheduler.py:895] Input prompt (4425 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:52 scheduler.py:895] Input prompt (5014 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:55 scheduler.py:895] Input prompt (4214 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:50:10 scheduler.py:895] Input prompt (5292 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:50:14 scheduler.py:895] Input prompt (4745 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:50:23 scheduler.py:895] Input prompt (4110 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:50:45 scheduler.py:895] Input prompt (4421 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:50:51 scheduler.py:895] Input prompt (4753 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:50:59 scheduler.py:895] Input prompt (4159 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:51:17 scheduler.py:895] Input prompt (4302 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:51:18 scheduler.py:895] Input prompt (4214 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:51:42 scheduler.py:895] Input prompt (4646 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:52:22 scheduler.py:895] Input prompt (4733 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:52:54 scheduler.py:895] Input prompt (4204 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:52:58 scheduler.py:895] Input prompt (4666 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:53:11 scheduler.py:895] Input prompt (4347 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:53:12 scheduler.py:895] Input prompt (4234 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:53:26 scheduler.py:895] Input prompt (4154 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:53:26 scheduler.py:895] Input prompt (4528 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:53:55 scheduler.py:895] Input prompt (4383 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:53:59 scheduler.py:895] Input prompt (4248 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:53:59 scheduler.py:895] Input prompt (4431 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:54:32 scheduler.py:895] Input prompt (4471 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:54:41 scheduler.py:895] Input prompt (4673 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:54:58 scheduler.py:895] Input prompt (4240 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:55:02 scheduler.py:895] Input prompt (4264 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:55:45 scheduler.py:895] Input prompt (4147 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:56:21 scheduler.py:895] Input prompt (4222 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:56:50 scheduler.py:895] Input prompt (4111 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:57:09 scheduler.py:895] Input prompt (4462 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:57:10 scheduler.py:895] Input prompt (4130 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:57:21 scheduler.py:895] Input prompt (4497 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:57:31 scheduler.py:895] Input prompt (4184 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:57:31 scheduler.py:895] Input prompt (4122 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:57:38 scheduler.py:895] Input prompt (4732 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:58:10 scheduler.py:895] Input prompt (4142 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:58:15 scheduler.py:895] Input prompt (4218 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:58:20 scheduler.py:895] Input prompt (4230 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:58:25 scheduler.py:895] Input prompt (5097 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:58:25 scheduler.py:895] Input prompt (4298 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:58:41 scheduler.py:895] Input prompt (4802 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:59:13 scheduler.py:895] Input prompt (4196 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:59:16 scheduler.py:895] Input prompt (4660 tokens) is too long and exceeds limit of 4096
overall result: 0.0
