==========================================
SLURM_JOB_ID = 79820
SLURM_NODELIST = gpunode04
==========================================
WARNING 04-23 19:12:06 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 04-23 19:12:17 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-7b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-7b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-7b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 04-23 19:12:19 model_runner.py:1056] Starting to load model meta-llama/Llama-2-7b-chat-hf...
INFO 04-23 19:12:22 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 04-23 19:12:48 model_runner.py:1067] Loading model weights took 12.5523 GB
INFO 04-23 19:12:49 gpu_executor.py:122] # GPU blocks: 2879, # CPU blocks: 512
INFO 04-23 19:12:49 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 11.25x
INFO 04-23 19:12:54 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-23 19:12:54 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-23 19:13:08 model_runner.py:1523] Graph capturing finished in 14 secs.
overall result: 0.0
WARNING 04-23 19:24:02 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 04-23 19:24:11 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-7b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-7b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-7b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 04-23 19:24:13 model_runner.py:1056] Starting to load model meta-llama/Llama-2-7b-chat-hf...
INFO 04-23 19:24:13 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 04-23 19:24:16 model_runner.py:1067] Loading model weights took 12.5523 GB
INFO 04-23 19:24:18 gpu_executor.py:122] # GPU blocks: 2879, # CPU blocks: 512
INFO 04-23 19:24:18 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 11.25x
INFO 04-23 19:24:23 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-23 19:24:23 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-23 19:24:42 model_runner.py:1523] Graph capturing finished in 19 secs.
overall result: 0.0
WARNING 04-23 19:38:22 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 04-23 19:38:33 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-7b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-7b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-7b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 04-23 19:38:35 model_runner.py:1056] Starting to load model meta-llama/Llama-2-7b-chat-hf...
INFO 04-23 19:38:35 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 04-23 19:38:38 model_runner.py:1067] Loading model weights took 12.5523 GB
INFO 04-23 19:38:39 gpu_executor.py:122] # GPU blocks: 2879, # CPU blocks: 512
INFO 04-23 19:38:39 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 11.25x
INFO 04-23 19:38:41 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-23 19:38:41 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-23 19:38:58 model_runner.py:1523] Graph capturing finished in 17 secs.
WARNING 04-23 19:44:05 scheduler.py:895] Input prompt (4319 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:50:30 scheduler.py:895] Input prompt (4231 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:50:51 scheduler.py:895] Input prompt (4411 tokens) is too long and exceeds limit of 4096
overall result: 0.0
WARNING 04-23 19:55:55 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 04-23 19:56:05 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-7b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-7b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-7b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 04-23 19:56:10 model_runner.py:1056] Starting to load model meta-llama/Llama-2-7b-chat-hf...
INFO 04-23 19:56:11 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 04-23 19:56:13 model_runner.py:1067] Loading model weights took 12.5523 GB
INFO 04-23 19:56:14 gpu_executor.py:122] # GPU blocks: 2879, # CPU blocks: 512
INFO 04-23 19:56:14 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 11.25x
INFO 04-23 19:56:16 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-23 19:56:16 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-23 19:56:31 model_runner.py:1523] Graph capturing finished in 14 secs.
WARNING 04-23 19:56:32 scheduler.py:895] Input prompt (4837 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:56:50 scheduler.py:895] Input prompt (4156 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:07 scheduler.py:895] Input prompt (4317 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:07 scheduler.py:895] Input prompt (4272 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:11 scheduler.py:895] Input prompt (4177 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:13 scheduler.py:895] Input prompt (4495 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:13 scheduler.py:895] Input prompt (4261 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:16 scheduler.py:895] Input prompt (4251 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:17 scheduler.py:895] Input prompt (5156 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:18 scheduler.py:895] Input prompt (4371 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:26 scheduler.py:895] Input prompt (4153 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:37 scheduler.py:895] Input prompt (4449 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:43 scheduler.py:895] Input prompt (4367 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:57:59 scheduler.py:895] Input prompt (4936 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:07 scheduler.py:895] Input prompt (4106 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:14 scheduler.py:895] Input prompt (4421 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:15 scheduler.py:895] Input prompt (4401 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:15 scheduler.py:895] Input prompt (4534 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:16 scheduler.py:895] Input prompt (4236 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:17 scheduler.py:895] Input prompt (4743 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:22 scheduler.py:895] Input prompt (4161 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:28 scheduler.py:895] Input prompt (4556 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:34 scheduler.py:895] Input prompt (4621 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:57 scheduler.py:895] Input prompt (4253 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:58:58 scheduler.py:895] Input prompt (4695 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:59:30 scheduler.py:895] Input prompt (4653 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:59:37 scheduler.py:895] Input prompt (4277 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:59:39 scheduler.py:895] Input prompt (4281 tokens) is too long and exceeds limit of 4096
WARNING 04-23 19:59:52 scheduler.py:895] Input prompt (4708 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:00 scheduler.py:895] Input prompt (4615 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:01 scheduler.py:895] Input prompt (4711 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:10 scheduler.py:895] Input prompt (4569 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:23 scheduler.py:895] Input prompt (4130 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:23 scheduler.py:895] Input prompt (4533 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:23 scheduler.py:895] Input prompt (4624 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:30 scheduler.py:895] Input prompt (4135 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:30 scheduler.py:895] Input prompt (4182 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:32 scheduler.py:895] Input prompt (4249 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:37 scheduler.py:895] Input prompt (4101 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:38 scheduler.py:895] Input prompt (4201 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:40 scheduler.py:895] Input prompt (5088 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:40 scheduler.py:895] Input prompt (4298 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:47 scheduler.py:895] Input prompt (4210 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:00:49 scheduler.py:895] Input prompt (4107 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:01:02 scheduler.py:895] Input prompt (4354 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:01:07 scheduler.py:895] Input prompt (4446 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:01:10 scheduler.py:895] Input prompt (4099 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:01:10 scheduler.py:895] Input prompt (4193 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:01:11 scheduler.py:895] Input prompt (4563 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:01:36 scheduler.py:895] Input prompt (4598 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:01:44 scheduler.py:895] Input prompt (4317 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:01:46 scheduler.py:895] Input prompt (4229 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:02:03 scheduler.py:895] Input prompt (4995 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:02:13 scheduler.py:895] Input prompt (4602 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:02:14 scheduler.py:895] Input prompt (4422 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:02:42 scheduler.py:895] Input prompt (4314 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:02:43 scheduler.py:895] Input prompt (4184 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:02:43 scheduler.py:895] Input prompt (4632 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:02:45 scheduler.py:895] Input prompt (4995 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:02:48 scheduler.py:895] Input prompt (4768 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:03:02 scheduler.py:895] Input prompt (4459 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:03:05 scheduler.py:895] Input prompt (4155 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:03:10 scheduler.py:895] Input prompt (4758 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:03:18 scheduler.py:895] Input prompt (4155 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:03:21 scheduler.py:895] Input prompt (4198 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:03:35 scheduler.py:895] Input prompt (4125 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:03:40 scheduler.py:895] Input prompt (4679 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:04:15 scheduler.py:895] Input prompt (4143 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:04:16 scheduler.py:895] Input prompt (4749 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:04:18 scheduler.py:895] Input prompt (4124 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:04:23 scheduler.py:895] Input prompt (4112 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:04:24 scheduler.py:895] Input prompt (4249 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:04:32 scheduler.py:895] Input prompt (4360 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:04:42 scheduler.py:895] Input prompt (4454 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:04:44 scheduler.py:895] Input prompt (4239 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:05:18 scheduler.py:895] Input prompt (4162 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:05:30 scheduler.py:895] Input prompt (4209 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:05:45 scheduler.py:895] Input prompt (4138 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:05:46 scheduler.py:895] Input prompt (4183 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:05:59 scheduler.py:895] Input prompt (4474 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:06:01 scheduler.py:895] Input prompt (4325 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:06:01 scheduler.py:895] Input prompt (4111 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:06:02 scheduler.py:895] Input prompt (4381 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:06:08 scheduler.py:895] Input prompt (4482 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:06:21 scheduler.py:895] Input prompt (4344 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:06:47 scheduler.py:895] Input prompt (4407 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:06:55 scheduler.py:895] Input prompt (4145 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:06:59 scheduler.py:895] Input prompt (4267 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:06:59 scheduler.py:895] Input prompt (4215 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:05 scheduler.py:895] Input prompt (4120 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:06 scheduler.py:895] Input prompt (4234 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:10 scheduler.py:895] Input prompt (4099 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:16 scheduler.py:895] Input prompt (4611 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:29 scheduler.py:895] Input prompt (4175 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:36 scheduler.py:895] Input prompt (4854 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:39 scheduler.py:895] Input prompt (4241 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:39 scheduler.py:895] Input prompt (4306 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:39 scheduler.py:895] Input prompt (4594 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:44 scheduler.py:895] Input prompt (4166 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:44 scheduler.py:895] Input prompt (4291 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:48 scheduler.py:895] Input prompt (4305 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:52 scheduler.py:895] Input prompt (4259 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:07:55 scheduler.py:895] Input prompt (4206 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:08:08 scheduler.py:895] Input prompt (4574 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:08:17 scheduler.py:895] Input prompt (4441 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:08:22 scheduler.py:895] Input prompt (4427 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:08:39 scheduler.py:895] Input prompt (4260 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:08:40 scheduler.py:895] Input prompt (4527 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:08:50 scheduler.py:895] Input prompt (4452 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:08 scheduler.py:895] Input prompt (4841 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:12 scheduler.py:895] Input prompt (4297 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:17 scheduler.py:895] Input prompt (4655 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:20 scheduler.py:895] Input prompt (4303 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:21 scheduler.py:895] Input prompt (5863 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:30 scheduler.py:895] Input prompt (4285 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:33 scheduler.py:895] Input prompt (4425 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:33 scheduler.py:895] Input prompt (5014 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:35 scheduler.py:895] Input prompt (4214 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:42 scheduler.py:895] Input prompt (5292 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:43 scheduler.py:895] Input prompt (4745 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:09:49 scheduler.py:895] Input prompt (4110 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:10:00 scheduler.py:895] Input prompt (4658 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:10:02 scheduler.py:895] Input prompt (4421 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:10:07 scheduler.py:895] Input prompt (4753 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:10:13 scheduler.py:895] Input prompt (4159 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:10:24 scheduler.py:895] Input prompt (4302 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:10:24 scheduler.py:895] Input prompt (4214 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:10:37 scheduler.py:895] Input prompt (4646 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:10:44 scheduler.py:895] Input prompt (4510 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:10:57 scheduler.py:895] Input prompt (4733 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:11:14 scheduler.py:895] Input prompt (4204 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:11:17 scheduler.py:895] Input prompt (4666 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:11:27 scheduler.py:895] Input prompt (4347 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:11:27 scheduler.py:895] Input prompt (4234 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:11:34 scheduler.py:895] Input prompt (4154 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:11:34 scheduler.py:895] Input prompt (4528 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:11:51 scheduler.py:895] Input prompt (4383 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:11:53 scheduler.py:895] Input prompt (4248 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:11:53 scheduler.py:895] Input prompt (4431 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:12:12 scheduler.py:895] Input prompt (4471 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:12:15 scheduler.py:895] Input prompt (4713 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:12:16 scheduler.py:895] Input prompt (4673 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:12:26 scheduler.py:895] Input prompt (4240 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:12:28 scheduler.py:895] Input prompt (4264 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:12:52 scheduler.py:895] Input prompt (4147 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:13:13 scheduler.py:895] Input prompt (4222 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:13:29 scheduler.py:895] Input prompt (4111 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:13:40 scheduler.py:895] Input prompt (4462 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:13:41 scheduler.py:895] Input prompt (4130 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:13:44 scheduler.py:895] Input prompt (4455 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:13:47 scheduler.py:895] Input prompt (4497 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:13:53 scheduler.py:895] Input prompt (4184 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:13:53 scheduler.py:895] Input prompt (4122 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:13:57 scheduler.py:895] Input prompt (4732 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:13 scheduler.py:895] Input prompt (4142 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:16 scheduler.py:895] Input prompt (4218 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:18 scheduler.py:895] Input prompt (4230 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:21 scheduler.py:895] Input prompt (5097 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:21 scheduler.py:895] Input prompt (4298 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:30 scheduler.py:895] Input prompt (4802 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:46 scheduler.py:895] Input prompt (4196 tokens) is too long and exceeds limit of 4096
WARNING 04-23 20:14:48 scheduler.py:895] Input prompt (4660 tokens) is too long and exceeds limit of 4096
overall result: 0.0
WARNING 04-23 20:15:14 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 04-23 20:15:24 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 04-23 20:15:36 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-chat-hf...
INFO 04-23 20:15:39 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 04-23 20:16:44 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 04-23 20:16:45 gpu_executor.py:122] # GPU blocks: 873, # CPU blocks: 327
INFO 04-23 20:16:45 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 3.41x
INFO 04-23 20:16:55 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-23 20:16:55 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-23 20:17:14 model_runner.py:1523] Graph capturing finished in 18 secs.
overall result: 0.0
WARNING 04-23 20:34:23 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 04-23 20:34:32 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 04-23 20:34:34 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-chat-hf...
INFO 04-23 20:34:35 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 04-23 20:34:40 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 04-23 20:34:41 gpu_executor.py:122] # GPU blocks: 873, # CPU blocks: 327
INFO 04-23 20:34:41 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 3.41x
INFO 04-23 20:34:45 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-23 20:34:45 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-23 20:34:59 model_runner.py:1523] Graph capturing finished in 13 secs.
overall result: 0.0
WARNING 04-23 20:57:27 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 04-23 20:57:36 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 04-23 20:57:39 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-chat-hf...
INFO 04-23 20:57:39 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 04-23 20:57:44 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 04-23 20:57:45 gpu_executor.py:122] # GPU blocks: 873, # CPU blocks: 327
INFO 04-23 20:57:45 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 3.41x
INFO 04-23 20:57:49 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-23 20:57:49 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-23 20:58:00 model_runner.py:1523] Graph capturing finished in 10 secs.
WARNING 04-23 21:06:31 scheduler.py:895] Input prompt (4319 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:17:26 scheduler.py:895] Input prompt (4231 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:17:58 scheduler.py:895] Input prompt (4411 tokens) is too long and exceeds limit of 4096
overall result: 0.0
WARNING 04-23 21:26:19 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 04-23 21:26:28 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 04-23 21:26:30 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-chat-hf...
INFO 04-23 21:26:31 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 04-23 21:26:36 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 04-23 21:26:37 gpu_executor.py:122] # GPU blocks: 873, # CPU blocks: 327
INFO 04-23 21:26:37 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 3.41x
INFO 04-23 21:26:40 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-23 21:26:40 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-23 21:26:57 model_runner.py:1523] Graph capturing finished in 17 secs.
WARNING 04-23 21:26:58 scheduler.py:895] Input prompt (4837 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:27:30 scheduler.py:895] Input prompt (4156 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:27:59 scheduler.py:895] Input prompt (4317 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:27:59 scheduler.py:895] Input prompt (4272 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:28:06 scheduler.py:895] Input prompt (4177 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:28:10 scheduler.py:895] Input prompt (4495 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:28:10 scheduler.py:895] Input prompt (4261 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:28:15 scheduler.py:895] Input prompt (4251 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:28:19 scheduler.py:895] Input prompt (5156 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:28:20 scheduler.py:895] Input prompt (4371 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:28:35 scheduler.py:895] Input prompt (4153 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:28:54 scheduler.py:895] Input prompt (4449 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:29:04 scheduler.py:895] Input prompt (4367 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:29:32 scheduler.py:895] Input prompt (4936 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:29:44 scheduler.py:895] Input prompt (4106 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:29:58 scheduler.py:895] Input prompt (4421 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:29:59 scheduler.py:895] Input prompt (4401 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:29:59 scheduler.py:895] Input prompt (4534 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:30:02 scheduler.py:895] Input prompt (4236 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:30:02 scheduler.py:895] Input prompt (4743 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:30:11 scheduler.py:895] Input prompt (4161 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:30:23 scheduler.py:895] Input prompt (4556 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:30:33 scheduler.py:895] Input prompt (4621 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:31:13 scheduler.py:895] Input prompt (4253 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:31:14 scheduler.py:895] Input prompt (4695 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:32:12 scheduler.py:895] Input prompt (4653 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:32:23 scheduler.py:895] Input prompt (4277 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:32:26 scheduler.py:895] Input prompt (4281 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:32:49 scheduler.py:895] Input prompt (4708 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:33:01 scheduler.py:895] Input prompt (4615 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:33:03 scheduler.py:895] Input prompt (4711 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:33:19 scheduler.py:895] Input prompt (4569 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:33:44 scheduler.py:895] Input prompt (4130 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:33:44 scheduler.py:895] Input prompt (4533 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:33:44 scheduler.py:895] Input prompt (4624 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:33:58 scheduler.py:895] Input prompt (4135 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:33:58 scheduler.py:895] Input prompt (4182 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:33:59 scheduler.py:895] Input prompt (4249 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:34:09 scheduler.py:895] Input prompt (4101 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:34:12 scheduler.py:895] Input prompt (4201 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:34:14 scheduler.py:895] Input prompt (5088 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:34:14 scheduler.py:895] Input prompt (4298 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:34:28 scheduler.py:895] Input prompt (4210 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:34:31 scheduler.py:895] Input prompt (4107 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:34:54 scheduler.py:895] Input prompt (4354 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:35:02 scheduler.py:895] Input prompt (4446 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:35:06 scheduler.py:895] Input prompt (4099 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:35:07 scheduler.py:895] Input prompt (4193 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:35:10 scheduler.py:895] Input prompt (4563 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:35:52 scheduler.py:895] Input prompt (4598 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:36:04 scheduler.py:895] Input prompt (4317 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:36:10 scheduler.py:895] Input prompt (4229 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:36:45 scheduler.py:895] Input prompt (4995 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:37:02 scheduler.py:895] Input prompt (4602 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:37:05 scheduler.py:895] Input prompt (4422 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:37:56 scheduler.py:895] Input prompt (4314 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:37:57 scheduler.py:895] Input prompt (4184 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:37:57 scheduler.py:895] Input prompt (4632 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:38:00 scheduler.py:895] Input prompt (4995 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:38:03 scheduler.py:895] Input prompt (4768 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:38:28 scheduler.py:895] Input prompt (4459 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:38:32 scheduler.py:895] Input prompt (4155 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:38:42 scheduler.py:895] Input prompt (4758 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:38:54 scheduler.py:895] Input prompt (4155 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:38:59 scheduler.py:895] Input prompt (4198 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:39:25 scheduler.py:895] Input prompt (4125 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:39:33 scheduler.py:895] Input prompt (4679 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:40:37 scheduler.py:895] Input prompt (4143 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:40:38 scheduler.py:895] Input prompt (4749 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:40:42 scheduler.py:895] Input prompt (4124 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:40:51 scheduler.py:895] Input prompt (4112 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:40:52 scheduler.py:895] Input prompt (4249 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:41:07 scheduler.py:895] Input prompt (4360 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:41:24 scheduler.py:895] Input prompt (4454 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:41:28 scheduler.py:895] Input prompt (4239 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:42:25 scheduler.py:895] Input prompt (4162 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:42:50 scheduler.py:895] Input prompt (4209 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:43:19 scheduler.py:895] Input prompt (4138 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:43:19 scheduler.py:895] Input prompt (4183 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:43:42 scheduler.py:895] Input prompt (4474 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:43:46 scheduler.py:895] Input prompt (4325 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:43:46 scheduler.py:895] Input prompt (4111 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:43:48 scheduler.py:895] Input prompt (4381 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:43:58 scheduler.py:895] Input prompt (4482 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:44:19 scheduler.py:895] Input prompt (4344 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:45:08 scheduler.py:895] Input prompt (4407 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:45:20 scheduler.py:895] Input prompt (4145 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:45:28 scheduler.py:895] Input prompt (4267 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:45:29 scheduler.py:895] Input prompt (4215 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:45:39 scheduler.py:895] Input prompt (4120 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:45:40 scheduler.py:895] Input prompt (4234 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:45:47 scheduler.py:895] Input prompt (4099 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:45:55 scheduler.py:895] Input prompt (4611 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:18 scheduler.py:895] Input prompt (4175 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:30 scheduler.py:895] Input prompt (4854 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:33 scheduler.py:895] Input prompt (4241 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:33 scheduler.py:895] Input prompt (4306 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:33 scheduler.py:895] Input prompt (4594 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:42 scheduler.py:895] Input prompt (4166 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:43 scheduler.py:895] Input prompt (4291 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:48 scheduler.py:895] Input prompt (4305 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:46:56 scheduler.py:895] Input prompt (4259 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:47:01 scheduler.py:895] Input prompt (4206 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:47:22 scheduler.py:895] Input prompt (4574 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:47:40 scheduler.py:895] Input prompt (4441 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:47:48 scheduler.py:895] Input prompt (4427 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:48:16 scheduler.py:895] Input prompt (4260 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:48:19 scheduler.py:895] Input prompt (4527 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:48:37 scheduler.py:895] Input prompt (4452 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:08 scheduler.py:895] Input prompt (4841 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:17 scheduler.py:895] Input prompt (4297 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:24 scheduler.py:895] Input prompt (4655 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:29 scheduler.py:895] Input prompt (4303 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:31 scheduler.py:895] Input prompt (5863 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:47 scheduler.py:895] Input prompt (4285 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:52 scheduler.py:895] Input prompt (4425 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:52 scheduler.py:895] Input prompt (5014 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:49:56 scheduler.py:895] Input prompt (4214 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:50:09 scheduler.py:895] Input prompt (5292 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:50:12 scheduler.py:895] Input prompt (4745 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:50:22 scheduler.py:895] Input prompt (4110 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:50:39 scheduler.py:895] Input prompt (4658 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:50:43 scheduler.py:895] Input prompt (4421 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:50:51 scheduler.py:895] Input prompt (4753 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:51:00 scheduler.py:895] Input prompt (4159 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:51:18 scheduler.py:895] Input prompt (4302 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:51:19 scheduler.py:895] Input prompt (4214 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:51:42 scheduler.py:895] Input prompt (4646 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:51:54 scheduler.py:895] Input prompt (4510 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:52:20 scheduler.py:895] Input prompt (4733 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:52:50 scheduler.py:895] Input prompt (4204 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:52:55 scheduler.py:895] Input prompt (4666 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:53:08 scheduler.py:895] Input prompt (4347 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:53:09 scheduler.py:895] Input prompt (4234 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:53:22 scheduler.py:895] Input prompt (4154 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:53:22 scheduler.py:895] Input prompt (4528 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:53:54 scheduler.py:895] Input prompt (4383 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:53:59 scheduler.py:895] Input prompt (4248 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:53:59 scheduler.py:895] Input prompt (4431 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:54:31 scheduler.py:895] Input prompt (4471 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:54:35 scheduler.py:895] Input prompt (4713 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:54:37 scheduler.py:895] Input prompt (4673 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:54:55 scheduler.py:895] Input prompt (4240 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:54:59 scheduler.py:895] Input prompt (4264 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:55:37 scheduler.py:895] Input prompt (4147 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:56:14 scheduler.py:895] Input prompt (4222 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:56:43 scheduler.py:895] Input prompt (4111 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:57:02 scheduler.py:895] Input prompt (4462 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:57:03 scheduler.py:895] Input prompt (4130 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:57:11 scheduler.py:895] Input prompt (4455 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:57:15 scheduler.py:895] Input prompt (4497 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:57:26 scheduler.py:895] Input prompt (4184 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:57:26 scheduler.py:895] Input prompt (4122 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:57:33 scheduler.py:895] Input prompt (4732 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:58:03 scheduler.py:895] Input prompt (4142 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:58:09 scheduler.py:895] Input prompt (4218 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:58:14 scheduler.py:895] Input prompt (4230 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:58:19 scheduler.py:895] Input prompt (5097 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:58:19 scheduler.py:895] Input prompt (4298 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:58:34 scheduler.py:895] Input prompt (4802 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:59:06 scheduler.py:895] Input prompt (4196 tokens) is too long and exceeds limit of 4096
WARNING 04-23 21:59:09 scheduler.py:895] Input prompt (4660 tokens) is too long and exceeds limit of 4096
overall result: 0.0
