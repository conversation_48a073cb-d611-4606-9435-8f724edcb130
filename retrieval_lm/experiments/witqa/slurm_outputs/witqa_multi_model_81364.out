==========================================
SLURM_JOB_ID = 81364
SLURM_NODELIST = gpunode01
==========================================
WARNING 05-03 19:40:09 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 19:40:21 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 19:40:22 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-hf...
INFO 05-03 19:40:23 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 19:41:05 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 05-03 19:41:06 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 19:41:06 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 19:41:09 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 19:41:09 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 19:41:21 model_runner.py:1523] Graph capturing finished in 12 secs.
WARNING 05-03 19:41:22 scheduler.py:895] Input prompt (5957 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:41:26 scheduler.py:895] Input prompt (7829 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:41:53 scheduler.py:895] Input prompt (7163 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:42:21 scheduler.py:895] Input prompt (4322 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:42:39 scheduler.py:895] Input prompt (9385 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:42:39 scheduler.py:895] Input prompt (8842 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:42:43 scheduler.py:895] Input prompt (8740 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:43:12 scheduler.py:895] Input prompt (7973 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:43:22 scheduler.py:895] Input prompt (7385 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:44:34 scheduler.py:895] Input prompt (7759 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:44:38 scheduler.py:895] Input prompt (4573 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:47:35 scheduler.py:895] Input prompt (7444 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:48:05 scheduler.py:895] Input prompt (4334 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:48:33 scheduler.py:895] Input prompt (8116 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:50:09 scheduler.py:895] Input prompt (8877 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:50:18 scheduler.py:895] Input prompt (7480 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:55:39 scheduler.py:895] Input prompt (8985 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:58:23 scheduler.py:895] Input prompt (6054 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:59:53 scheduler.py:895] Input prompt (7590 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:00:11 scheduler.py:895] Input prompt (4330 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:02:16 scheduler.py:895] Input prompt (4162 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:03:36 scheduler.py:895] Input prompt (6028 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:03:41 scheduler.py:895] Input prompt (4169 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:03:41 scheduler.py:895] Input prompt (6166 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:03:41 scheduler.py:895] Input prompt (5663 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:03:45 scheduler.py:895] Input prompt (7767 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:03:45 scheduler.py:895] Input prompt (4411 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:03:45 scheduler.py:895] Input prompt (9053 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:03:46 scheduler.py:895] Input prompt (9500 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:03:46 scheduler.py:895] Input prompt (8057 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:03:52 scheduler.py:895] Input prompt (5238 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:03:52 scheduler.py:895] Input prompt (8492 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:03:57 scheduler.py:895] Input prompt (8506 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:04:36 scheduler.py:895] Input prompt (7920 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:04:36 scheduler.py:895] Input prompt (6430 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:04:47 scheduler.py:895] Input prompt (5493 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:04:52 scheduler.py:895] Input prompt (6317 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:04:57 scheduler.py:895] Input prompt (8655 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:05:12 scheduler.py:895] Input prompt (4424 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:05:17 scheduler.py:895] Input prompt (5601 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:05:17 scheduler.py:895] Input prompt (9515 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:05:22 scheduler.py:895] Input prompt (8680 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:05:22 scheduler.py:895] Input prompt (9086 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:05:55 scheduler.py:895] Input prompt (8751 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:06:00 scheduler.py:895] Input prompt (4550 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:06:05 scheduler.py:895] Input prompt (6841 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:06:09 scheduler.py:895] Input prompt (4127 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:06:15 scheduler.py:895] Input prompt (7020 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:06:19 scheduler.py:895] Input prompt (8717 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:06:26 scheduler.py:895] Input prompt (4360 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:06:26 scheduler.py:895] Input prompt (10154 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:06:31 scheduler.py:895] Input prompt (5744 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:06:36 scheduler.py:895] Input prompt (8635 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:06:36 scheduler.py:895] Input prompt (9179 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:06:46 scheduler.py:895] Input prompt (6124 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:07:14 scheduler.py:895] Input prompt (4109 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:07:24 scheduler.py:895] Input prompt (9035 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:07:29 scheduler.py:895] Input prompt (4190 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:07:34 scheduler.py:895] Input prompt (4373 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:07:34 scheduler.py:895] Input prompt (8492 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:07:43 scheduler.py:895] Input prompt (9242 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:08:15 scheduler.py:895] Input prompt (9177 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:08:20 scheduler.py:895] Input prompt (8073 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:08:31 scheduler.py:895] Input prompt (7801 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:08:37 scheduler.py:895] Input prompt (7457 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:08:54 scheduler.py:895] Input prompt (8376 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:08:54 scheduler.py:895] Input prompt (8250 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:09:06 scheduler.py:895] Input prompt (5842 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:09:17 scheduler.py:895] Input prompt (8023 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:09:24 scheduler.py:895] Input prompt (5577 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:09:24 scheduler.py:895] Input prompt (8578 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:09:34 scheduler.py:895] Input prompt (6843 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:09:34 scheduler.py:895] Input prompt (7826 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:09:34 scheduler.py:895] Input prompt (6980 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:09:36 scheduler.py:895] Input prompt (4265 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:09:45 scheduler.py:895] Input prompt (8327 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:09:50 scheduler.py:895] Input prompt (6838 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:09:50 scheduler.py:895] Input prompt (6601 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:09:50 scheduler.py:895] Input prompt (5065 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:09:50 scheduler.py:895] Input prompt (9155 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:09:52 scheduler.py:895] Input prompt (7162 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:09:58 scheduler.py:895] Input prompt (7090 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:10:03 scheduler.py:895] Input prompt (6629 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:10:14 scheduler.py:895] Input prompt (5739 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:10:20 scheduler.py:895] Input prompt (7402 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:10:25 scheduler.py:895] Input prompt (8156 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:10:37 scheduler.py:895] Input prompt (5625 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:10:37 scheduler.py:895] Input prompt (8736 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:10:43 scheduler.py:895] Input prompt (6317 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:10:54 scheduler.py:895] Input prompt (4137 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:10:54 scheduler.py:895] Input prompt (8312 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:10:59 scheduler.py:895] Input prompt (8783 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:11:02 scheduler.py:895] Input prompt (8578 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:11:20 scheduler.py:895] Input prompt (4950 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:11:25 scheduler.py:895] Input prompt (7003 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:11:30 scheduler.py:895] Input prompt (8143 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:11:40 scheduler.py:895] Input prompt (8033 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:11:52 scheduler.py:895] Input prompt (4098 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:11:58 scheduler.py:895] Input prompt (7471 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:11:58 scheduler.py:895] Input prompt (4319 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:01 scheduler.py:895] Input prompt (7785 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:01 scheduler.py:895] Input prompt (6507 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:01 scheduler.py:895] Input prompt (4382 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:05 scheduler.py:895] Input prompt (7735 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:17 scheduler.py:895] Input prompt (7276 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:22 scheduler.py:895] Input prompt (4601 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:22 scheduler.py:895] Input prompt (4347 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:28 scheduler.py:895] Input prompt (9085 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:29 scheduler.py:895] Input prompt (8492 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:34 scheduler.py:895] Input prompt (5369 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:34 scheduler.py:895] Input prompt (9045 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:34 scheduler.py:895] Input prompt (6192 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:36 scheduler.py:895] Input prompt (8933 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:36 scheduler.py:895] Input prompt (5259 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:36 scheduler.py:895] Input prompt (7290 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:40 scheduler.py:895] Input prompt (8192 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:44 scheduler.py:895] Input prompt (5182 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:49 scheduler.py:895] Input prompt (4670 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:55 scheduler.py:895] Input prompt (9111 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:13:14 scheduler.py:895] Input prompt (6654 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:13:21 scheduler.py:895] Input prompt (5362 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:13:28 scheduler.py:895] Input prompt (7896 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:13:33 scheduler.py:895] Input prompt (6344 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:13:40 scheduler.py:895] Input prompt (5333 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:13:52 scheduler.py:895] Input prompt (4757 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:14:05 scheduler.py:895] Input prompt (5742 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:14:05 scheduler.py:895] Input prompt (8258 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:14:11 scheduler.py:895] Input prompt (5513 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:14:16 scheduler.py:895] Input prompt (7764 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:14:21 scheduler.py:895] Input prompt (7760 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:14:21 scheduler.py:895] Input prompt (5181 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:14:27 scheduler.py:895] Input prompt (6634 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:14:27 scheduler.py:895] Input prompt (6449 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:14:28 scheduler.py:895] Input prompt (6838 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:14:33 scheduler.py:895] Input prompt (6195 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:14:58 scheduler.py:895] Input prompt (7451 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:15:10 scheduler.py:895] Input prompt (7440 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:15:19 scheduler.py:895] Input prompt (7613 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:15:25 scheduler.py:895] Input prompt (9078 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:16:35 scheduler.py:895] Input prompt (6141 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:16:35 scheduler.py:895] Input prompt (8260 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:16:40 scheduler.py:895] Input prompt (4246 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:16:40 scheduler.py:895] Input prompt (4816 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:16:40 scheduler.py:895] Input prompt (6320 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:16:44 scheduler.py:895] Input prompt (5205 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:16:50 scheduler.py:895] Input prompt (5857 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:16:56 scheduler.py:895] Input prompt (4168 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:17:01 scheduler.py:895] Input prompt (6537 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:17:01 scheduler.py:895] Input prompt (6705 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:17:07 scheduler.py:895] Input prompt (8569 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:17:11 scheduler.py:895] Input prompt (7575 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:17:16 scheduler.py:895] Input prompt (5705 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:17:16 scheduler.py:895] Input prompt (8318 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:17:18 scheduler.py:895] Input prompt (8265 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:17:23 scheduler.py:895] Input prompt (4870 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:17:23 scheduler.py:895] Input prompt (8419 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:17:23 scheduler.py:895] Input prompt (4473 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:17:28 scheduler.py:895] Input prompt (9515 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:17:28 scheduler.py:895] Input prompt (5157 tokens) is too long and exceeds limit of 4096
overall result: 0.0
WARNING 05-03 20:17:40 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 20:17:51 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 20:17:52 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-hf...
INFO 05-03 20:17:53 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 20:18:39 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 05-03 20:18:40 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 20:18:40 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 20:18:43 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 20:18:43 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 20:18:56 model_runner.py:1523] Graph capturing finished in 12 secs.
WARNING 05-03 20:18:57 scheduler.py:895] Input prompt (6888 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:19:04 scheduler.py:895] Input prompt (8708 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:19:09 scheduler.py:895] Input prompt (4131 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:19:27 scheduler.py:895] Input prompt (4487 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:19:34 scheduler.py:895] Input prompt (7887 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:20:08 scheduler.py:895] Input prompt (4350 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:20:13 scheduler.py:895] Input prompt (4988 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:20:36 scheduler.py:895] Input prompt (10634 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:20:36 scheduler.py:895] Input prompt (9802 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:20:42 scheduler.py:895] Input prompt (9805 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:21:20 scheduler.py:895] Input prompt (8865 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:21:32 scheduler.py:895] Input prompt (8175 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:22:05 scheduler.py:895] Input prompt (4298 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:23:05 scheduler.py:895] Input prompt (8575 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:23:10 scheduler.py:895] Input prompt (5650 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:27:04 scheduler.py:895] Input prompt (8134 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:27:44 scheduler.py:895] Input prompt (5306 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:28:21 scheduler.py:895] Input prompt (9088 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:30:26 scheduler.py:895] Input prompt (9603 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:30:37 scheduler.py:895] Input prompt (8108 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:31:01 scheduler.py:895] Input prompt (4553 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:34:01 scheduler.py:895] Input prompt (4508 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:37:30 scheduler.py:895] Input prompt (9990 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:38:00 scheduler.py:895] Input prompt (4260 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:40:53 scheduler.py:895] Input prompt (4271 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:41:10 scheduler.py:895] Input prompt (6976 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:43:07 scheduler.py:895] Input prompt (8127 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:43:30 scheduler.py:895] Input prompt (5190 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:43:59 scheduler.py:895] Input prompt (4193 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:46:10 scheduler.py:895] Input prompt (5501 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:47:09 scheduler.py:895] Input prompt (4320 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:47:50 scheduler.py:895] Input prompt (7006 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:47:57 scheduler.py:895] Input prompt (4869 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:47:57 scheduler.py:895] Input prompt (7319 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:47:57 scheduler.py:895] Input prompt (6602 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:48:01 scheduler.py:895] Input prompt (8590 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:48:01 scheduler.py:895] Input prompt (5312 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:48:01 scheduler.py:895] Input prompt (10022 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:48:05 scheduler.py:895] Input prompt (10488 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:48:05 scheduler.py:895] Input prompt (8831 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:48:05 scheduler.py:895] Input prompt (4880 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:48:08 scheduler.py:895] Input prompt (6027 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:48:08 scheduler.py:895] Input prompt (9346 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:48:13 scheduler.py:895] Input prompt (9456 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:48:57 scheduler.py:895] Input prompt (5219 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:49:03 scheduler.py:895] Input prompt (8797 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:49:04 scheduler.py:895] Input prompt (7521 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:49:16 scheduler.py:895] Input prompt (6236 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:49:23 scheduler.py:895] Input prompt (7096 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:49:28 scheduler.py:895] Input prompt (9506 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:49:40 scheduler.py:895] Input prompt (4589 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:49:45 scheduler.py:895] Input prompt (5336 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:49:51 scheduler.py:895] Input prompt (6455 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:49:51 scheduler.py:895] Input prompt (10181 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:49:57 scheduler.py:895] Input prompt (9612 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:49:57 scheduler.py:895] Input prompt (10085 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:50:02 scheduler.py:895] Input prompt (4420 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:50:08 scheduler.py:895] Input prompt (4260 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:50:08 scheduler.py:895] Input prompt (4357 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:50:35 scheduler.py:895] Input prompt (9833 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:50:41 scheduler.py:895] Input prompt (5284 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:50:47 scheduler.py:895] Input prompt (8748 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:50:52 scheduler.py:895] Input prompt (4486 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:51:00 scheduler.py:895] Input prompt (7992 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:51:05 scheduler.py:895] Input prompt (9459 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:51:13 scheduler.py:895] Input prompt (5896 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:51:13 scheduler.py:895] Input prompt (11120 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:51:16 scheduler.py:895] Input prompt (6349 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:51:23 scheduler.py:895] Input prompt (9537 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:51:23 scheduler.py:895] Input prompt (10000 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:51:35 scheduler.py:895] Input prompt (6927 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:51:56 scheduler.py:895] Input prompt (4313 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:51:56 scheduler.py:895] Input prompt (4495 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:52:08 scheduler.py:895] Input prompt (5003 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:52:20 scheduler.py:895] Input prompt (10138 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:52:27 scheduler.py:895] Input prompt (5081 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:52:33 scheduler.py:895] Input prompt (5559 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:52:33 scheduler.py:895] Input prompt (9394 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:52:43 scheduler.py:895] Input prompt (10071 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:53:25 scheduler.py:895] Input prompt (10071 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:53:31 scheduler.py:895] Input prompt (8856 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:53:31 scheduler.py:895] Input prompt (4112 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:53:44 scheduler.py:895] Input prompt (8660 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:53:50 scheduler.py:895] Input prompt (8194 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:54:11 scheduler.py:895] Input prompt (9226 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:54:11 scheduler.py:895] Input prompt (9293 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:54:29 scheduler.py:895] Input prompt (6586 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:54:34 scheduler.py:895] Input prompt (4247 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:54:36 scheduler.py:895] Input prompt (8578 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:54:44 scheduler.py:895] Input prompt (5033 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:54:44 scheduler.py:895] Input prompt (6493 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:54:44 scheduler.py:895] Input prompt (9533 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:54:54 scheduler.py:895] Input prompt (7374 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:54:54 scheduler.py:895] Input prompt (8451 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:54:54 scheduler.py:895] Input prompt (7611 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:54:59 scheduler.py:895] Input prompt (5141 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:55:12 scheduler.py:895] Input prompt (9106 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:55:17 scheduler.py:895] Input prompt (7643 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:55:17 scheduler.py:895] Input prompt (7302 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:55:21 scheduler.py:895] Input prompt (5637 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:55:21 scheduler.py:895] Input prompt (10085 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:55:26 scheduler.py:895] Input prompt (8013 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:55:34 scheduler.py:895] Input prompt (7806 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:55:39 scheduler.py:895] Input prompt (7425 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:55:45 scheduler.py:895] Input prompt (4298 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:55:48 scheduler.py:895] Input prompt (4944 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:55:49 scheduler.py:895] Input prompt (6756 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:55:54 scheduler.py:895] Input prompt (8283 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:56:00 scheduler.py:895] Input prompt (8948 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:56:07 scheduler.py:895] Input prompt (4526 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:56:12 scheduler.py:895] Input prompt (6547 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:56:12 scheduler.py:895] Input prompt (9696 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:56:18 scheduler.py:895] Input prompt (7018 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:56:24 scheduler.py:895] Input prompt (4649 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:56:31 scheduler.py:895] Input prompt (4835 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:56:31 scheduler.py:895] Input prompt (4415 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:56:31 scheduler.py:895] Input prompt (8835 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:56:34 scheduler.py:895] Input prompt (9726 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:56:40 scheduler.py:895] Input prompt (9535 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:56:47 scheduler.py:895] Input prompt (4122 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:57:01 scheduler.py:895] Input prompt (5507 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:57:06 scheduler.py:895] Input prompt (4515 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:57:06 scheduler.py:895] Input prompt (7597 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:57:12 scheduler.py:895] Input prompt (8815 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:57:23 scheduler.py:895] Input prompt (4555 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:57:23 scheduler.py:895] Input prompt (4184 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:57:23 scheduler.py:895] Input prompt (9041 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:57:34 scheduler.py:895] Input prompt (4616 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:57:34 scheduler.py:895] Input prompt (4169 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:57:34 scheduler.py:895] Input prompt (4834 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:57:39 scheduler.py:895] Input prompt (8462 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:57:39 scheduler.py:895] Input prompt (5015 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:57:45 scheduler.py:895] Input prompt (8894 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:57:45 scheduler.py:895] Input prompt (7430 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:57:45 scheduler.py:895] Input prompt (5410 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:57:49 scheduler.py:895] Input prompt (8619 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:57:55 scheduler.py:895] Input prompt (4158 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:58:03 scheduler.py:895] Input prompt (8009 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:58:08 scheduler.py:895] Input prompt (5526 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:58:09 scheduler.py:895] Input prompt (5287 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:58:15 scheduler.py:895] Input prompt (9999 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:58:17 scheduler.py:895] Input prompt (9328 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:58:22 scheduler.py:895] Input prompt (6193 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:58:22 scheduler.py:895] Input prompt (9724 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:58:22 scheduler.py:895] Input prompt (6621 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:58:24 scheduler.py:895] Input prompt (9952 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:58:24 scheduler.py:895] Input prompt (6189 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:58:24 scheduler.py:895] Input prompt (7960 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:58:31 scheduler.py:895] Input prompt (8924 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:58:36 scheduler.py:895] Input prompt (6012 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:58:42 scheduler.py:895] Input prompt (5217 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:58:47 scheduler.py:895] Input prompt (10149 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:59:04 scheduler.py:895] Input prompt (4642 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:59:10 scheduler.py:895] Input prompt (7239 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:59:11 scheduler.py:895] Input prompt (4689 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:59:17 scheduler.py:895] Input prompt (5942 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:59:18 scheduler.py:895] Input prompt (4583 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:59:23 scheduler.py:895] Input prompt (8934 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:59:29 scheduler.py:895] Input prompt (7119 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:59:36 scheduler.py:895] Input prompt (6343 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:59:51 scheduler.py:895] Input prompt (5790 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:59:58 scheduler.py:895] Input prompt (4370 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:00:06 scheduler.py:895] Input prompt (6437 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:00:06 scheduler.py:895] Input prompt (8637 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:00:06 scheduler.py:895] Input prompt (4687 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:00:11 scheduler.py:895] Input prompt (6159 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:00:17 scheduler.py:895] Input prompt (8548 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:00:24 scheduler.py:895] Input prompt (8512 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:00:24 scheduler.py:895] Input prompt (6131 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:00:30 scheduler.py:895] Input prompt (4452 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:00:30 scheduler.py:895] Input prompt (7564 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:00:30 scheduler.py:895] Input prompt (7048 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:00:30 scheduler.py:895] Input prompt (4436 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:00:30 scheduler.py:895] Input prompt (7250 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:00:31 scheduler.py:895] Input prompt (7107 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:00:37 scheduler.py:895] Input prompt (4501 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:00:58 scheduler.py:895] Input prompt (4543 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:00:58 scheduler.py:895] Input prompt (8355 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:01:12 scheduler.py:895] Input prompt (8285 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:01:23 scheduler.py:895] Input prompt (8441 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:01:34 scheduler.py:895] Input prompt (9846 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:01:46 scheduler.py:895] Input prompt (5108 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:02:18 scheduler.py:895] Input prompt (4436 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:02:53 scheduler.py:895] Input prompt (4457 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:02:58 scheduler.py:895] Input prompt (6556 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:02:59 scheduler.py:895] Input prompt (9097 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:05 scheduler.py:895] Input prompt (4894 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:05 scheduler.py:895] Input prompt (5953 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:05 scheduler.py:895] Input prompt (7173 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:09 scheduler.py:895] Input prompt (5862 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:13 scheduler.py:895] Input prompt (4471 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:13 scheduler.py:895] Input prompt (6639 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:20 scheduler.py:895] Input prompt (4995 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:26 scheduler.py:895] Input prompt (4927 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:26 scheduler.py:895] Input prompt (7244 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:26 scheduler.py:895] Input prompt (7604 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:29 scheduler.py:895] Input prompt (9448 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:34 scheduler.py:895] Input prompt (8569 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:39 scheduler.py:895] Input prompt (6661 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:39 scheduler.py:895] Input prompt (9340 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:44 scheduler.py:895] Input prompt (8849 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:51 scheduler.py:895] Input prompt (5977 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:51 scheduler.py:895] Input prompt (9349 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:51 scheduler.py:895] Input prompt (5371 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:56 scheduler.py:895] Input prompt (10607 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:03:56 scheduler.py:895] Input prompt (5936 tokens) is too long and exceeds limit of 4096
overall result: 0.0
WARNING 05-03 21:04:13 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 21:04:23 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 21:04:24 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-chat-hf...
INFO 05-03 21:04:25 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 21:05:10 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 05-03 21:05:11 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 21:05:11 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 21:05:14 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 21:05:14 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 21:05:26 model_runner.py:1523] Graph capturing finished in 12 secs.
WARNING 05-03 21:05:27 scheduler.py:895] Input prompt (5957 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:05:32 scheduler.py:895] Input prompt (7829 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:05:53 scheduler.py:895] Input prompt (7163 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:06:13 scheduler.py:895] Input prompt (4322 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:06:29 scheduler.py:895] Input prompt (9385 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:06:29 scheduler.py:895] Input prompt (8842 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:06:34 scheduler.py:895] Input prompt (8740 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:07:01 scheduler.py:895] Input prompt (7973 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:07:10 scheduler.py:895] Input prompt (7385 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:08:02 scheduler.py:895] Input prompt (7759 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:08:03 scheduler.py:895] Input prompt (4573 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:10:30 scheduler.py:895] Input prompt (7444 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:10:46 scheduler.py:895] Input prompt (4334 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:11:12 scheduler.py:895] Input prompt (8116 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:12:24 scheduler.py:895] Input prompt (8877 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:12:32 scheduler.py:895] Input prompt (7480 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:16:21 scheduler.py:895] Input prompt (8985 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:18:09 scheduler.py:895] Input prompt (6054 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:19:17 scheduler.py:895] Input prompt (7590 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:19:29 scheduler.py:895] Input prompt (4330 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:21:08 scheduler.py:895] Input prompt (4162 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:22:01 scheduler.py:895] Input prompt (6028 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:22:03 scheduler.py:895] Input prompt (4169 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:22:03 scheduler.py:895] Input prompt (6166 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:22:03 scheduler.py:895] Input prompt (5663 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:22:06 scheduler.py:895] Input prompt (7767 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:22:06 scheduler.py:895] Input prompt (4411 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:22:06 scheduler.py:895] Input prompt (9053 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:22:07 scheduler.py:895] Input prompt (9500 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:22:07 scheduler.py:895] Input prompt (8057 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:22:13 scheduler.py:895] Input prompt (5238 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:22:13 scheduler.py:895] Input prompt (8492 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:22:18 scheduler.py:895] Input prompt (8506 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:22:49 scheduler.py:895] Input prompt (7920 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:22:49 scheduler.py:895] Input prompt (6430 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:23:00 scheduler.py:895] Input prompt (5493 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:23:03 scheduler.py:895] Input prompt (6317 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:23:05 scheduler.py:895] Input prompt (8655 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:23:18 scheduler.py:895] Input prompt (4424 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:23:20 scheduler.py:895] Input prompt (5601 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:23:20 scheduler.py:895] Input prompt (9515 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:23:25 scheduler.py:895] Input prompt (8680 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:23:25 scheduler.py:895] Input prompt (9086 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:23:52 scheduler.py:895] Input prompt (8751 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:23:54 scheduler.py:895] Input prompt (4550 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:23:58 scheduler.py:895] Input prompt (6841 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:24:02 scheduler.py:895] Input prompt (4127 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:24:05 scheduler.py:895] Input prompt (7020 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:24:09 scheduler.py:895] Input prompt (8717 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:24:13 scheduler.py:895] Input prompt (4360 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:24:13 scheduler.py:895] Input prompt (10154 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:24:17 scheduler.py:895] Input prompt (5744 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:24:21 scheduler.py:895] Input prompt (8635 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:24:21 scheduler.py:895] Input prompt (9179 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:24:27 scheduler.py:895] Input prompt (6124 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:24:46 scheduler.py:895] Input prompt (4109 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:24:55 scheduler.py:895] Input prompt (9035 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:24:58 scheduler.py:895] Input prompt (4190 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:25:02 scheduler.py:895] Input prompt (4373 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:25:02 scheduler.py:895] Input prompt (8492 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:25:11 scheduler.py:895] Input prompt (9242 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:25:43 scheduler.py:895] Input prompt (9177 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:25:46 scheduler.py:895] Input prompt (8073 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:25:56 scheduler.py:895] Input prompt (7801 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:26:01 scheduler.py:895] Input prompt (7457 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:26:18 scheduler.py:895] Input prompt (8376 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:26:18 scheduler.py:895] Input prompt (8250 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:26:28 scheduler.py:895] Input prompt (5842 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:26:37 scheduler.py:895] Input prompt (8023 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:26:44 scheduler.py:895] Input prompt (5577 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:26:44 scheduler.py:895] Input prompt (8578 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:26:52 scheduler.py:895] Input prompt (6843 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:26:52 scheduler.py:895] Input prompt (7826 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:26:52 scheduler.py:895] Input prompt (6980 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:26:54 scheduler.py:895] Input prompt (4265 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:26:59 scheduler.py:895] Input prompt (8327 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:27:01 scheduler.py:895] Input prompt (6838 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:27:01 scheduler.py:895] Input prompt (6601 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:27:02 scheduler.py:895] Input prompt (5065 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:27:02 scheduler.py:895] Input prompt (9155 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:27:04 scheduler.py:895] Input prompt (7162 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:27:09 scheduler.py:895] Input prompt (7090 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:27:11 scheduler.py:895] Input prompt (6629 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:27:21 scheduler.py:895] Input prompt (5739 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:27:26 scheduler.py:895] Input prompt (7402 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:27:29 scheduler.py:895] Input prompt (8156 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:27:39 scheduler.py:895] Input prompt (5625 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:27:39 scheduler.py:895] Input prompt (8736 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:27:45 scheduler.py:895] Input prompt (6317 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:27:56 scheduler.py:895] Input prompt (4137 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:27:56 scheduler.py:895] Input prompt (8312 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:01 scheduler.py:895] Input prompt (8783 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:06 scheduler.py:895] Input prompt (8578 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:16 scheduler.py:895] Input prompt (4950 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:18 scheduler.py:895] Input prompt (7003 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:20 scheduler.py:895] Input prompt (8143 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:29 scheduler.py:895] Input prompt (8033 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:38 scheduler.py:895] Input prompt (4098 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:40 scheduler.py:895] Input prompt (7471 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:40 scheduler.py:895] Input prompt (4319 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:41 scheduler.py:895] Input prompt (7785 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:41 scheduler.py:895] Input prompt (6507 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:41 scheduler.py:895] Input prompt (4382 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:42 scheduler.py:895] Input prompt (7735 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:50 scheduler.py:895] Input prompt (7276 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:52 scheduler.py:895] Input prompt (4601 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:52 scheduler.py:895] Input prompt (4347 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:58 scheduler.py:895] Input prompt (9085 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:28:58 scheduler.py:895] Input prompt (8492 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:29:03 scheduler.py:895] Input prompt (5369 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:29:03 scheduler.py:895] Input prompt (9045 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:29:03 scheduler.py:895] Input prompt (6192 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:29:07 scheduler.py:895] Input prompt (8933 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:29:07 scheduler.py:895] Input prompt (5259 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:29:07 scheduler.py:895] Input prompt (7290 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:29:10 scheduler.py:895] Input prompt (8192 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:29:11 scheduler.py:895] Input prompt (5182 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:29:13 scheduler.py:895] Input prompt (4670 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:29:15 scheduler.py:895] Input prompt (9111 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:29:31 scheduler.py:895] Input prompt (6654 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:29:38 scheduler.py:895] Input prompt (5362 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:29:42 scheduler.py:895] Input prompt (7896 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:29:47 scheduler.py:895] Input prompt (6344 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:29:51 scheduler.py:895] Input prompt (5333 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:30:00 scheduler.py:895] Input prompt (4757 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:30:11 scheduler.py:895] Input prompt (5742 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:30:11 scheduler.py:895] Input prompt (8258 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:30:17 scheduler.py:895] Input prompt (5513 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:30:22 scheduler.py:895] Input prompt (7764 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:30:24 scheduler.py:895] Input prompt (7760 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:30:24 scheduler.py:895] Input prompt (5181 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:30:30 scheduler.py:895] Input prompt (6634 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:30:30 scheduler.py:895] Input prompt (6449 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:30:31 scheduler.py:895] Input prompt (6838 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:30:33 scheduler.py:895] Input prompt (6195 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:30:52 scheduler.py:895] Input prompt (7451 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:31:02 scheduler.py:895] Input prompt (7440 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:31:12 scheduler.py:895] Input prompt (7613 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:31:18 scheduler.py:895] Input prompt (9078 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:32:13 scheduler.py:895] Input prompt (6141 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:32:13 scheduler.py:895] Input prompt (8260 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:32:18 scheduler.py:895] Input prompt (4246 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:32:18 scheduler.py:895] Input prompt (4816 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:32:18 scheduler.py:895] Input prompt (6320 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:32:23 scheduler.py:895] Input prompt (5205 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:32:29 scheduler.py:895] Input prompt (5857 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:32:35 scheduler.py:895] Input prompt (4168 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:32:41 scheduler.py:895] Input prompt (6537 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:32:41 scheduler.py:895] Input prompt (6705 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:32:43 scheduler.py:895] Input prompt (8569 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:32:48 scheduler.py:895] Input prompt (7575 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:32:53 scheduler.py:895] Input prompt (5705 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:32:53 scheduler.py:895] Input prompt (8318 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:32:57 scheduler.py:895] Input prompt (8265 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:33:00 scheduler.py:895] Input prompt (4870 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:33:00 scheduler.py:895] Input prompt (8419 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:33:00 scheduler.py:895] Input prompt (4473 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:33:05 scheduler.py:895] Input prompt (9515 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:33:05 scheduler.py:895] Input prompt (5157 tokens) is too long and exceeds limit of 4096
overall result: 0.0
WARNING 05-03 21:33:17 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 21:33:28 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 21:33:30 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-chat-hf...
INFO 05-03 21:33:30 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 21:34:17 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 05-03 21:34:18 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 21:34:18 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 21:34:21 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 21:34:21 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 21:34:34 model_runner.py:1523] Graph capturing finished in 12 secs.
WARNING 05-03 21:34:35 scheduler.py:895] Input prompt (6888 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:34:42 scheduler.py:895] Input prompt (8708 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:34:43 scheduler.py:895] Input prompt (4131 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:34:59 scheduler.py:895] Input prompt (4487 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:35:05 scheduler.py:895] Input prompt (7887 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:35:34 scheduler.py:895] Input prompt (4350 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:35:37 scheduler.py:895] Input prompt (4988 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:35:56 scheduler.py:895] Input prompt (10634 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:35:56 scheduler.py:895] Input prompt (9802 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:36:02 scheduler.py:895] Input prompt (9805 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:36:37 scheduler.py:895] Input prompt (8865 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:36:48 scheduler.py:895] Input prompt (8175 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:37:11 scheduler.py:895] Input prompt (4298 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:38:02 scheduler.py:895] Input prompt (8575 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:38:04 scheduler.py:895] Input prompt (5650 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:41:26 scheduler.py:895] Input prompt (8134 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:41:54 scheduler.py:895] Input prompt (5306 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:42:28 scheduler.py:895] Input prompt (9088 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:44:13 scheduler.py:895] Input prompt (9603 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:44:22 scheduler.py:895] Input prompt (8108 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:44:37 scheduler.py:895] Input prompt (4553 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:47:10 scheduler.py:895] Input prompt (4508 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:50:00 scheduler.py:895] Input prompt (9990 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:50:25 scheduler.py:895] Input prompt (4260 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:52:42 scheduler.py:895] Input prompt (4271 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:52:57 scheduler.py:895] Input prompt (6976 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:54:26 scheduler.py:895] Input prompt (8127 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:54:43 scheduler.py:895] Input prompt (5190 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:55:06 scheduler.py:895] Input prompt (4193 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:56:58 scheduler.py:895] Input prompt (5501 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:57:46 scheduler.py:895] Input prompt (4320 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:58:20 scheduler.py:895] Input prompt (7006 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:58:27 scheduler.py:895] Input prompt (4869 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:58:27 scheduler.py:895] Input prompt (7319 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:58:27 scheduler.py:895] Input prompt (6602 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:58:29 scheduler.py:895] Input prompt (8590 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:58:29 scheduler.py:895] Input prompt (5312 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:58:29 scheduler.py:895] Input prompt (10022 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:58:32 scheduler.py:895] Input prompt (10488 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:58:32 scheduler.py:895] Input prompt (8831 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:58:32 scheduler.py:895] Input prompt (4880 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:58:36 scheduler.py:895] Input prompt (6027 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:58:37 scheduler.py:895] Input prompt (9346 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:58:42 scheduler.py:895] Input prompt (9456 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:59:17 scheduler.py:895] Input prompt (5219 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:59:23 scheduler.py:895] Input prompt (8797 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:59:23 scheduler.py:895] Input prompt (7521 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:59:35 scheduler.py:895] Input prompt (6236 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:59:41 scheduler.py:895] Input prompt (7096 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:59:45 scheduler.py:895] Input prompt (9506 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:59:55 scheduler.py:895] Input prompt (4589 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:59:58 scheduler.py:895] Input prompt (5336 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:00:04 scheduler.py:895] Input prompt (6455 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:00:04 scheduler.py:895] Input prompt (10181 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:00:10 scheduler.py:895] Input prompt (9612 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:00:10 scheduler.py:895] Input prompt (10085 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:00:12 scheduler.py:895] Input prompt (4420 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:00:18 scheduler.py:895] Input prompt (4260 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:00:18 scheduler.py:895] Input prompt (4357 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:00:41 scheduler.py:895] Input prompt (9833 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:00:45 scheduler.py:895] Input prompt (5284 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:00:51 scheduler.py:895] Input prompt (8748 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:00:56 scheduler.py:895] Input prompt (4486 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:01:02 scheduler.py:895] Input prompt (7992 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:01:07 scheduler.py:895] Input prompt (9459 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:01:14 scheduler.py:895] Input prompt (5896 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:01:14 scheduler.py:895] Input prompt (11120 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:01:20 scheduler.py:895] Input prompt (6349 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:01:25 scheduler.py:895] Input prompt (9537 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:01:25 scheduler.py:895] Input prompt (10000 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:01:37 scheduler.py:895] Input prompt (6927 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:01:50 scheduler.py:895] Input prompt (4313 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:01:50 scheduler.py:895] Input prompt (4495 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:02:01 scheduler.py:895] Input prompt (5003 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:02:10 scheduler.py:895] Input prompt (10138 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:02:17 scheduler.py:895] Input prompt (5081 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:02:22 scheduler.py:895] Input prompt (5559 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:02:22 scheduler.py:895] Input prompt (9394 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:02:32 scheduler.py:895] Input prompt (10071 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:03:14 scheduler.py:895] Input prompt (10071 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:03:17 scheduler.py:895] Input prompt (8856 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:03:17 scheduler.py:895] Input prompt (4112 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:03:27 scheduler.py:895] Input prompt (8660 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:03:33 scheduler.py:895] Input prompt (8194 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:03:54 scheduler.py:895] Input prompt (9226 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:03:54 scheduler.py:895] Input prompt (9293 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:04:10 scheduler.py:895] Input prompt (6586 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:04:13 scheduler.py:895] Input prompt (4247 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:04:18 scheduler.py:895] Input prompt (8578 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:04:25 scheduler.py:895] Input prompt (5033 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:04:25 scheduler.py:895] Input prompt (6493 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:04:25 scheduler.py:895] Input prompt (9533 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:04:31 scheduler.py:895] Input prompt (7374 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:04:31 scheduler.py:895] Input prompt (8451 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:04:31 scheduler.py:895] Input prompt (7611 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:04:36 scheduler.py:895] Input prompt (5141 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:04:45 scheduler.py:895] Input prompt (9106 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:04:48 scheduler.py:895] Input prompt (7643 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:04:48 scheduler.py:895] Input prompt (7302 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:04:50 scheduler.py:895] Input prompt (5637 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:04:51 scheduler.py:895] Input prompt (10085 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:04:56 scheduler.py:895] Input prompt (8013 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:05:01 scheduler.py:895] Input prompt (7806 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:05:05 scheduler.py:895] Input prompt (7425 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:05:09 scheduler.py:895] Input prompt (4298 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:05:13 scheduler.py:895] Input prompt (4944 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:05:14 scheduler.py:895] Input prompt (6756 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:05:18 scheduler.py:895] Input prompt (8283 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:05:22 scheduler.py:895] Input prompt (8948 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:05:29 scheduler.py:895] Input prompt (4526 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:05:34 scheduler.py:895] Input prompt (6547 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:05:34 scheduler.py:895] Input prompt (9696 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:05:41 scheduler.py:895] Input prompt (7018 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:05:46 scheduler.py:895] Input prompt (4649 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:05:52 scheduler.py:895] Input prompt (4835 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:05:52 scheduler.py:895] Input prompt (4415 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:05:52 scheduler.py:895] Input prompt (8835 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:05:58 scheduler.py:895] Input prompt (9726 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:03 scheduler.py:895] Input prompt (9535 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:10 scheduler.py:895] Input prompt (4122 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:21 scheduler.py:895] Input prompt (5507 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:23 scheduler.py:895] Input prompt (4515 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:23 scheduler.py:895] Input prompt (7597 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:28 scheduler.py:895] Input prompt (8815 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:38 scheduler.py:895] Input prompt (4555 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:38 scheduler.py:895] Input prompt (4184 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:38 scheduler.py:895] Input prompt (9041 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:49 scheduler.py:895] Input prompt (4616 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:49 scheduler.py:895] Input prompt (4169 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:49 scheduler.py:895] Input prompt (4834 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:50 scheduler.py:895] Input prompt (8462 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:50 scheduler.py:895] Input prompt (5015 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:55 scheduler.py:895] Input prompt (8894 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:55 scheduler.py:895] Input prompt (7430 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:55 scheduler.py:895] Input prompt (5410 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:06:57 scheduler.py:895] Input prompt (8619 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:01 scheduler.py:895] Input prompt (4158 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:09 scheduler.py:895] Input prompt (8009 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:11 scheduler.py:895] Input prompt (5526 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:12 scheduler.py:895] Input prompt (5287 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:15 scheduler.py:895] Input prompt (9999 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:16 scheduler.py:895] Input prompt (9328 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:22 scheduler.py:895] Input prompt (6193 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:22 scheduler.py:895] Input prompt (9724 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:22 scheduler.py:895] Input prompt (6621 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:27 scheduler.py:895] Input prompt (9952 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:27 scheduler.py:895] Input prompt (6189 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:27 scheduler.py:895] Input prompt (7960 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:31 scheduler.py:895] Input prompt (8924 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:34 scheduler.py:895] Input prompt (6012 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:37 scheduler.py:895] Input prompt (5217 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:39 scheduler.py:895] Input prompt (10149 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:52 scheduler.py:895] Input prompt (4642 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:59 scheduler.py:895] Input prompt (7239 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:07:59 scheduler.py:895] Input prompt (4689 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:02 scheduler.py:895] Input prompt (5942 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:03 scheduler.py:895] Input prompt (4583 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:06 scheduler.py:895] Input prompt (8934 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:09 scheduler.py:895] Input prompt (7119 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:12 scheduler.py:895] Input prompt (6343 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:27 scheduler.py:895] Input prompt (5790 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:30 scheduler.py:895] Input prompt (4370 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:35 scheduler.py:895] Input prompt (6437 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:35 scheduler.py:895] Input prompt (8637 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:35 scheduler.py:895] Input prompt (4687 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:38 scheduler.py:895] Input prompt (6159 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:45 scheduler.py:895] Input prompt (8548 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:49 scheduler.py:895] Input prompt (8512 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:49 scheduler.py:895] Input prompt (6131 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:55 scheduler.py:895] Input prompt (4452 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:55 scheduler.py:895] Input prompt (7564 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:55 scheduler.py:895] Input prompt (7048 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:55 scheduler.py:895] Input prompt (4436 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:55 scheduler.py:895] Input prompt (7250 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:56 scheduler.py:895] Input prompt (7107 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:08:59 scheduler.py:895] Input prompt (4501 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:09:17 scheduler.py:895] Input prompt (4543 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:09:17 scheduler.py:895] Input prompt (8355 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:09:30 scheduler.py:895] Input prompt (8285 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:09:40 scheduler.py:895] Input prompt (8441 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:09:48 scheduler.py:895] Input prompt (9846 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:10:00 scheduler.py:895] Input prompt (5108 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:10:26 scheduler.py:895] Input prompt (4436 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:10:55 scheduler.py:895] Input prompt (4457 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:10:59 scheduler.py:895] Input prompt (6556 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:00 scheduler.py:895] Input prompt (9097 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:06 scheduler.py:895] Input prompt (4894 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:06 scheduler.py:895] Input prompt (5953 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:06 scheduler.py:895] Input prompt (7173 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:10 scheduler.py:895] Input prompt (5862 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:16 scheduler.py:895] Input prompt (4471 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:16 scheduler.py:895] Input prompt (6639 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:23 scheduler.py:895] Input prompt (4995 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:29 scheduler.py:895] Input prompt (4927 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:29 scheduler.py:895] Input prompt (7244 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:29 scheduler.py:895] Input prompt (7604 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:32 scheduler.py:895] Input prompt (9448 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:38 scheduler.py:895] Input prompt (8569 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:44 scheduler.py:895] Input prompt (6661 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:44 scheduler.py:895] Input prompt (9340 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:48 scheduler.py:895] Input prompt (8849 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:54 scheduler.py:895] Input prompt (5977 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:55 scheduler.py:895] Input prompt (9349 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:11:55 scheduler.py:895] Input prompt (5371 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:12:00 scheduler.py:895] Input prompt (10607 tokens) is too long and exceeds limit of 4096
WARNING 05-03 22:12:00 scheduler.py:895] Input prompt (5936 tokens) is too long and exceeds limit of 4096
overall result: 0.0
WARNING 05-03 22:12:16 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 22:12:29 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='Qwen/Qwen2.5-7B-Instruct', speculative_config=None, tokenizer='Qwen/Qwen2.5-7B-Instruct', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.bfloat16, max_seq_len=32768, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=Qwen/Qwen2.5-7B-Instruct, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 22:12:30 model_runner.py:1056] Starting to load model Qwen/Qwen2.5-7B-Instruct...
INFO 05-03 22:12:30 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 22:12:59 model_runner.py:1067] Loading model weights took 14.2487 GB
INFO 05-03 22:13:04 gpu_executor.py:122] # GPU blocks: 27715, # CPU blocks: 4681
INFO 05-03 22:13:04 gpu_executor.py:126] Maximum concurrency for 32768 tokens per request: 13.53x
INFO 05-03 22:13:08 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 22:13:08 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 22:13:17 model_runner.py:1523] Graph capturing finished in 10 secs.
overall result: 0.0
WARNING 05-03 22:36:05 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 22:36:17 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='Qwen/Qwen2.5-7B-Instruct', speculative_config=None, tokenizer='Qwen/Qwen2.5-7B-Instruct', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.bfloat16, max_seq_len=32768, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=Qwen/Qwen2.5-7B-Instruct, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 22:36:19 model_runner.py:1056] Starting to load model Qwen/Qwen2.5-7B-Instruct...
INFO 05-03 22:36:19 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 22:36:43 model_runner.py:1067] Loading model weights took 14.2487 GB
INFO 05-03 22:36:47 gpu_executor.py:122] # GPU blocks: 27715, # CPU blocks: 4681
INFO 05-03 22:36:47 gpu_executor.py:126] Maximum concurrency for 32768 tokens per request: 13.53x
INFO 05-03 22:36:52 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 22:36:52 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 22:37:01 model_runner.py:1523] Graph capturing finished in 10 secs.
overall result: 0.0
