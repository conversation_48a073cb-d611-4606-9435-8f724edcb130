#!/bin/bash
#SBATCH --job-name=witqa_orig_no_ret
#SBATCH --output=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/slurm_outputs/witqa_orig_no_ret_%j.out
#SBATCH --error=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/slurm_outputs/witqa_orig_no_ret_%j.err
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=48:00:00
#SBATCH --partition=ampere

source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

# Vanilla Llama2 7B
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
--model_name meta-llama/Llama-2-7b-hf \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_original_retrieve20_contrievermsm.json \
--max_new_tokens 100 \
--metric match \
--result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_llama2_7b_vanilla_no_retrieval.json \
--task qa \
--prompt_name "llama_chat_prompt" \
--download_dir /home/<USER>/.cache/huggingface

# Vanilla Llama2 13B
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
--model_name meta-llama/Llama-2-13b-hf \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_original_retrieve20_contrievermsm.json \
--max_new_tokens 100 \
--metric match \
--result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_llama2_13b_vanilla_no_retrieval.json \
--task qa \
--prompt_name "llama_chat_prompt" \
--download_dir /home/<USER>/.cache/huggingface

# Llama2 7B Chat/Instruct
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
--model_name meta-llama/Llama-2-7b-chat-hf \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_original_retrieve20_contrievermsm.json \
--max_new_tokens 100 \
--metric match \
--result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_llama2_7b_instruct_no_retrieval.json \
--task qa \
--prompt_name "llama_chat_prompt" \
--download_dir /home/<USER>/.cache/huggingface

# Llama2 13B Chat/Instruct
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
--model_name meta-llama/Llama-2-13b-chat-hf \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_original_retrieve20_contrievermsm.json \
--max_new_tokens 100 \
--metric match \
--result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_llama2_13b_instruct_no_retrieval.json \
--task qa \
--prompt_name "llama_chat_prompt" \
--download_dir /home/<USER>/.cache/huggingface

# Qwen 7B
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
--model_name Qwen/Qwen2.5-7B-Instruct \
--download_dir /home/<USER>/.cache/huggingface/hub \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_long_tail_original_retrieve20_contrievermsm.json \
--max_new_tokens 100 \
--metric match \
--result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/witqa/witqa_qwen2_7b_instruct_no_retrieval.jsonl \
--task qa \
--prompt_name "prompt_no_input"