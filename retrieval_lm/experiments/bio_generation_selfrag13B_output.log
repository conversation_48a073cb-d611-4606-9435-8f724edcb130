WARNING 10-28 16:41:57 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 10-28 16:41:57 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 10-28 16:42:57 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 10-28 16:43:01 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 10-28 16:43:05 model_runner.py:437] Graph capturing finished in 4 secs.
