from vllm import LLM, SamplingParams

model = LLM("selfrag/selfrag_llama2_7b", download_dir="/home/<USER>/model_cache", dtype="half")
sampling_params = SamplingParams(temperature=0.0, top_p=1.0, max_tokens=100, skip_special_tokens=False)

def format_prompt(input, paragraph=None):
    prompt = "### Instruction:\n{0}\n\n### Response:\n".format(input)
    if paragraph is not None:
        prompt += "[Retrieval]<paragraph>{0}</paragraph>".format(paragraph)
    return prompt

query_1 = """
Instruction: You are provided with a question. Your task is to extract the first alias listed under the "Aliases" section. Only return the first alias mentioned.

Question: What two ingredients make Devils on Horseback?

Alias1:
"""

query_2 = """
Instruction: You are provided with a question. Your task is to extract the first alias listed under the "Aliases" section. Only return the first alias mentioned.

Question: Which volcano buried the Italian city of Pompeii?

Alias1:
"""

query_3 = """
Instruction: You are provided with a question. Your task is to extract the first alias listed under the "Aliases" section. Only return the first alias mentioned.

Question: The characters of <PERSON> and <PERSON> appeared in what 1980s TV series?

Alias1:
"""

query_4 = """
Instruction: You are provided with a question. Your task is to extract the first alias listed under the "Aliases" section. Only return the first alias mentioned.

Question: What is the name of the world’s first test-tube baby, born on 25th July 1978 in Manchester, England?

Alias1:
"""

query_5 = """
Instruction: You are provided with a question. Your task is to extract the first alias listed under the "Aliases" section. Only return the first alias mentioned.

Question: Sugar melted to its hottest confectionery stage naturally produces?

Alias1:
"""

query_6 = """
Instruction: You are provided with a question. Your task is to extract the first alias listed under the "Aliases" section. Only return the first alias mentioned.

Question: “Dead Belgians Don’t Count” was the original working title of which 90s sitcom?

Alias1:
"""

query_7 = """
Instruction: You are provided with a question. Your task is to extract the first alias listed under the "Aliases" section. Only return the first alias mentioned.

Question: Senator Joe McCarthly represented which state?

Alias1:
"""

query_8 = """
Instruction: You are provided with a question. Your task is to extract the first alias listed under the "Aliases" section. Only return the first alias mentioned.

Question: Named after Mrs Gamp, a character in a Dickens' novel, what is a Gamp?

Alias1:
"""

query_9 = """
Instruction: You are provided with a question. Your task is to extract the first alias listed under the "Aliases" section. Only return the first alias mentioned.

Question: What religion takes its name from the Greek for 'universal', from the root words meaning 'in respect of [the] whole'?

Alias1:
"""

query_10 = """
Instruction: You are provided with a question. Your task is to extract the first alias listed under the "Aliases" section. Only return the first alias mentioned.

Question: In certain species of birds, what is the name of the pouch used for temporarily storing food?

Alias1:
"""

queries = [query_1, query_2, query_3, query_4, query_5, query_6, query_7, query_8, query_9, query_10]

# for a query that doesn't require retrieval
preds = model.generate([format_prompt(query) for query in queries], sampling_params)
for pred in preds:
    print("Model prediction: {0}".format(pred.outputs[0].text))
