from vllm import LLM, SamplingParams
import pandas as pd
import ast

# Define paths and model parameters
model_name = "selfrag/selfrag_llama2_13b"
download_dir = "/home/<USER>/model_cache"
input_file = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/long_tail_granola.csv"
output_file = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/granola_eq_predictions_bfloat16.csv"

# Load the model
model = LLM(model_name, download_dir=download_dir)
sampling_params = SamplingParams(temperature=0.0, top_p=1.0, max_tokens=100, skip_special_tokens=True)

# Function to format the input for the model
def format_prompt(input, paragraph=None):
    prompt = "### Instruction:\n{0}\n\n### Response:\n".format(input)
    if paragraph is not None:
        prompt += "[Retrieval]<paragraph>{0}</paragraph>".format(paragraph)
    return prompt

# Function to check correctness based on substring matching


import ast
import math

def check_correctness(ground_truth, prediction):
    # If ground_truth is a string that represents a list, convert it to an actual list
    if isinstance(ground_truth, str):
        try:
            ground_truth = ast.literal_eval(ground_truth)
        except (SyntaxError, ValueError):
            ground_truth = [ground_truth]

    # Process each ground truth answer, ignoring 'nan' values and converting to string
    processed_ground_truth = []
    for answer in ground_truth:
        if isinstance(answer, str) or not (isinstance(answer, float) and math.isnan(answer)):  # Ignore nan values
            processed_ground_truth.append(str(answer).lower().strip())

    # Convert the prediction to lowercase and strip it
    prediction = prediction.lower().strip()

    # Check if any of the ground truth answers are substrings of the prediction
    return any(answer in prediction for answer in processed_ground_truth if answer)



# Load the filtered GRANOLA-EQ dataset
df = pd.read_csv(input_file)

# Prepare to store results
results = []

# Iterate over the dataset and make predictions
for index, row in df.iterrows():
    question = row['question']
    ground_truth = row[[col for col in df.columns if col.startswith('granola_answer')]].tolist()
    formatted_prompt = format_prompt(question)
    
    # Generate predictions
    preds = model.generate([formatted_prompt], sampling_params)
    
    # Extract the prediction
    prediction = preds[0].outputs[0].text
    
    # Check correctness
    correctness = check_correctness(ground_truth, prediction)
    
    # Store the result along with the original data
    results.append({
        "question": question,
        "ground_truth": ground_truth,
        "prediction": prediction,
        "correctness": correctness
    })

# Convert results to a DataFrame for easier analysis
results_df = pd.DataFrame(results)

# Save the results to a CSV file
results_df.to_csv(output_file, index=False)
