#!/bin/bash
 
#SBATCH --job-name=pubhealth
#SBATCH --nodes=1
#SBATCH --gpus=1
#SBATCH --output=pubhealth_selfrag13B_output.log  
#SBATCH --error=pubhealth_selfrag13B_error.log   
#SBATCH --partition=ampere  

source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

python run_short_form.py \
  --model_name selfrag/selfrag_llama2_13b \
  --input_file health_claims_processed.jsonl \
  --max_new_tokens 50 \
  --threshold 0.2 --output_file pubhealth_selfrag13B_results.jsonl \
  --metric match --ndocs 5 \
  --use_groundness --use_utility --use_seqscore \
  --task fever