#!/bin/bash
 
#SBATCH --job-name=bio_generation
#SBATCH --nodes=1
#SBATCH --gpus=1
#SBATCH --output=bio_generation_selfrag13B_output.log  
#SBATCH --error=bio_generation_selfrag13B_error.log   
#SBATCH --partition=ampere  

source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

python run_long_form_static.py \
  --model_name selfrag/selfrag_llama2_13b \
  --ndocs 5 --max_new_tokens 300 --threshold 0.2 \
  --use_grounding --use_utility --use_seqscore \
  --task factscore --input_file factscore_unlabeled_alpaca_13b_retrieval.jsonl \
  --output_file bio_generation_selfrag13B_results.jsonl --max_depth 7 \