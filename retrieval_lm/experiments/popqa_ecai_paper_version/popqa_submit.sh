#!/bin/bash
#SBATCH --job-name=popqa_SR7B_SR13B_orig
#SBATCH --output=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/slurm_outputs/popqa_SR7B_SR13B_orig_%j.out
#SBATCH --error=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/slurm_outputs/popqa_SR7B_SR13B_orig_%j.err
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=24:00:00
#SBATCH --partition=ampere  

# Activate conda environment
conda activate selfrag

# SelfRAG-7B with different ndocs
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name selfrag/selfrag_llama2_7b \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve5_contrievermsm_selfrag7b_results.jsonl \
    --metric match --ndocs 5 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name selfrag/selfrag_llama2_7b \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve10_contrievermsm_selfrag7b_results.jsonl \
    --metric match --ndocs 10 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name selfrag/selfrag_llama2_7b \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve15_contrievermsm_selfrag7b_results.jsonl \
    --metric match --ndocs 15 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name selfrag/selfrag_llama2_7b \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve20_contrievermsm_selfrag7b_results.jsonl \
    --metric match --ndocs 20 --use_groundness --use_utility --use_seqscore \
    --dtype half

# SelfRAG-13B with different ndocs
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name selfrag/selfrag_llama2_13b \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve5_contrievermsm_selfrag13b_results.jsonl \
    --metric match --ndocs 5 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name selfrag/selfrag_llama2_13b \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve10_contrievermsm_selfrag13b_results.jsonl \
    --metric match --ndocs 10 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name selfrag/selfrag_llama2_13b \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve15_contrievermsm_selfrag13b_results.jsonl \
    --metric match --ndocs 15 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name selfrag/selfrag_llama2_13b \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve20_contrievermsm_selfrag13b_results.jsonl \
    --metric match --ndocs 20 --use_groundness --use_utility --use_seqscore \
    --dtype half