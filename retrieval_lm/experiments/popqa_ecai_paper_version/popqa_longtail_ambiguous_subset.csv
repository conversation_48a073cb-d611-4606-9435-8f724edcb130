id,subj,prop,obj,subj_id,prop_id,obj_id,s_aliases,o_aliases,s_uri,o_uri,s_wiki_title,o_wiki_title,s_pop,o_pop,question,possible_answers,exact_match_count,relevant_entity_count,all_possible_answers,wikidata_description,augmented_question,augmented_with,filtered_question
4711830,<PERSON>,occupation,politician,2073748,22,2834605,[],"[""political leader"",""political figure"",""polit."",""pol""]",http://www.wikidata.org/entity/Q6233155,http://www.wikidata.org/entity/Q82955,<PERSON> (Canadian politician),Politician,48,25692,What is <PERSON>'s occupation?,"[""politician"", ""political leader"", ""political figure"", ""polit."", ""pol""]",19,8,"['politician', 'political leader', 'political figure', 'polit.', 'pol', 'poet', 'entomologist', 'association football player', 'military officer', 'botanist', 'barrister', 'art theorist', 'explorer', 'judge', 'art historian']",Canadian politician,What is <PERSON>'s occupation as a Canadian public figure?,True,"What is the occupation of <PERSON> <PERSON><PERSON>, the Canadian public figure?"
5120341,<PERSON> <PERSON>,occupation,dentist,2263666,22,920325,[],"[""dentists"",""dental surgeon""]",http://www.wikidata.org/entity/Q6790954,http://www.wikidata.org/entity/Q27349,Matthew <PERSON> (politician),Dentist,63,16672,What is Matthew <PERSON>'s occupation?,"[""dentist"", ""dentists"", ""dental surgeon""]",4,3,"['dentist', 'dentists', 'dental surgeon', 'American football player', 'politician', 'psychotherapist']",Canadian politician,What is Matthew McKay's occupation?,False,What is Matthew McKay's profession?
5651252,Russell Stokes,occupation,politician,2518393,22,2834605,"[""Russell Newton Stokes""]","[""political leader"",""political figure"",""polit."",""pol""]",http://www.wikidata.org/entity/Q7381796,http://www.wikidata.org/entity/Q82955,Russell Stokes,Politician,84,25692,What is Russell Stokes's occupation?,"[""politician"", ""political leader"", ""political figure"", ""polit."", ""pol""]",2,2,"['politician', 'political leader', 'political figure', 'polit.', 'pol', 'board member', 'businessperson']",Australian politician,What is Russell Stokes's occupation as an Australian?,True,What is Russell Stokes's occupation?
4674890,Jim Brown,occupation,radio personality,2059616,22,916690,[],"[""radio presenter"",""radio hostess"",""host"",""hostess"",""radio host"",""radio jockey"",""radio program boss"",""RJ""]",http://www.wikidata.org/entity/Q6193880,http://www.wikidata.org/entity/Q2722764,Jim Brown (radio host),Radio personality,98,5737,What is Jim Brown's occupation?,"[""radio personality"", ""radio presenter"", ""radio hostess"", ""host"", ""hostess"", ""radio host"", ""radio jockey"", ""radio program boss"", ""RJ""]",40,34,"['radio personality', 'radio presenter', 'radio hostess', 'host', 'hostess', 'radio host', 'radio jockey', 'radio program boss', 'RJ', 'ice hockey player', 'writer', 'television director', 'basketball player', 'film producer', 'film actor', 'television actor', 'association football manager', 'association football player', 'lacrosse player', 'drummer', 'American football player', 'songwriter', 'darts player', 'cinematographer', 'designer', 'sport cyclist', 'adviser', 'journalist', 'baseball player', 'actor', 'diplomat', 'translator', 'computer scientist', 'politician', 'film director', 'sprinter', 'military personnel', 'Australian rules football player', 'banker']",Canadian journalist,What is Jim Brown's occupation as a Canadian journalist?,True,What is Jim Brown's occupation as a Canadian?
3689415,Charles Harrison,occupation,politician,1602408,22,2834605,[],"[""political leader"",""political figure"",""polit."",""pol""]",http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q82955,Charles Harrison (Bewdley MP),Politician,51,25692,What is Charles Harrison's occupation?,"[""politician"", ""political leader"", ""political figure"", ""polit."", ""pol""]",17,10,"['politician', 'political leader', 'political figure', 'polit.', 'pol', 'music publisher', 'songwriter', 'lyricist', 'librettist', 'actor', 'basketball player', 'cartographer', 'business executive', 'engineer', 'military officer', 'composer', 'industrial designer', 'illustrator', 'cartoonist', 'conductor', 'drawer']",British politician (1830-1888),What was Charles Harrison's (1830–1888) occupation?,True,What was Charles Harrison's (1830–1888) profession?
5574760,Richard Harris,occupation,composer,2484673,22,1189033,"[""Richard Frank Keith Harris""]",[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q36834,Richard Harris (composer),Composer,80,28389,What is Richard Harris's occupation?,"[""composer""]",29,21,"['composer', 'rugby union player', 'barrister', 'explorer', 'recording artist', 'rugby league player', 'baseball player', 'basketball player', 'cave diver', 'actor', 'screenwriter', 'researcher', 'filmmaker', 'governor', 'writer', 'politician', 'American football player', 'orator', 'association football player', 'sculptor', 'geographer', 'singer', 'singer-songwriter', 'musician', 'film director', 'stage actor', 'film producer', 'director', 'philosopher', 'anesthesiologist', 'film actor']",British composer and pianist,What is Richard Harris's occupation as a British pianist?,True,What is Richard Harris's occupation as a British?
896602,Michael Hutchings,occupation,chef,380296,22,1144698,[],"[""chef de cuisine""]",http://www.wikidata.org/entity/Q16136337,http://www.wikidata.org/entity/Q3499072,Michael Hutchings (chef),Chef,60,24351,What is Michael Hutchings's occupation?,"[""chef"", ""chef de cuisine""]",3,2,"['chef', 'chef de cuisine', 'topologist', 'mathematician']",American chef,What is Michael Hutchings's occupation as an American?,True,What is Michael Hutchings's occupation?
4700304,John Barnes,occupation,monk,2069200,22,2491733,[],"[""monks""]",http://www.wikidata.org/entity/Q6220774,http://www.wikidata.org/entity/Q733786,John Barnes (monk),Monk,69,33483,What is John Barnes's occupation?,"[""monk"", ""monks""]",46,35,"['monk', 'monks', 'university teacher', ""children's writer"", 'novelist', 'journalist', 'basketball player', 'sports commentator', 'cricketer', 'science fiction writer', 'diplomat', 'artist', 'film director', 'film producer', 'mayor', 'historian', 'computer scientist', 'politician', 'mathematician', 'engineer', 'military officer', 'baseball manager', 'radio personality', 'naval officer', 'researcher', 'director', 'baseball player', 'writer', 'saxophonist', 'Australian rules football player', 'jazz musician', 'trade unionist', 'athlete', 'actor', 'producer', 'visual artist', 'screenwriter', 'lawyer', 'judge']",English Benedictine monk (17th-century),What was John Barnes's occupation as an English Benedictine monk from the 17th century?,True,"What was the occupation of John Barnes, the English Benedictine monk from the 17th century?"
262900,Christopher Butson,occupation,priest,106064,22,1291475,"[""Christopher Henry Gould Butson""]","[""reverend"",""priestess""]",http://www.wikidata.org/entity/Q11805289,http://www.wikidata.org/entity/Q42603,Christopher Butson (priest),Priest,56,20534,What is Christopher Butson's occupation?,"[""priest"", ""reverend"", ""priestess""]",2,2,"['priest', 'reverend', 'priestess']",Irish Dean,What is Christopher Butson's occupation as an Irish Dean?,True,What is Christopher Butson's occupation as an Irishman?
610662,Fritz Goos,occupation,astronomer,249949,22,64448,"[""Hermann Fritz Gustav Goos""]",[],http://www.wikidata.org/entity/Q1466410,http://www.wikidata.org/entity/Q11063,Fritz Goos,Astronomer,50,12368,What is Fritz Goos's occupation?,"[""astronomer"", ""physicist""]",2,2,"['astronomer', 'physicist']",German physicist (1883-1968),What was Fritz Goos's (1883–1968) occupation?,True,What was Fritz Goos's (1883–1968) profession?
4736716,John Strange,occupation,diplomat,2083602,22,665281,"[""Sir John Strange"",""Esq. Late John Strange"",""Strange""]",[],http://www.wikidata.org/entity/Q6259378,http://www.wikidata.org/entity/Q193391,John Strange (diplomat),Diplomat,72,14886,What is John Strange's occupation?,"[""diplomat""]",11,10,"['diplomat', 'designer', 'cricketer', 'politician', 'pornographic actor', 'theologian', 'judge', 'businessperson', 'Hebrew Bible scholar', 'rugby league player', 'biblical archaeologist']",British diplomat,What is John Strange's occupation as a British national?,True,"What is the occupation of John Strange, the British national?"
4895544,Kyaw Swe,occupation,film director,2157263,22,854535,[],"[""movie director"",""director"",""motion picture director""]",http://www.wikidata.org/entity/Q6450829,http://www.wikidata.org/entity/Q2526255,Kyaw Swe (actor),Film director,97,45772,What is Kyaw Swe's occupation?,"[""film director"", ""movie director"", ""director"", ""motion picture director"", ""actor"", ""actress"", ""actors"", ""actresses""]",5,4,"['film director', 'movie director', 'director', 'motion picture director', 'actor', 'actress', 'actors', 'actresses', 'politician']",Burmese actor and film director,"What is the occupation of Kyaw Swe, the Burmese artist?",True,"What is the occupation of Kyaw Swe, the Burmese?"
1136027,"John Blake, Jr.",occupation,politician,500340,22,2834605,[],"[""political leader"",""political figure"",""polit."",""pol""]",http://www.wikidata.org/entity/Q1699352,http://www.wikidata.org/entity/Q82955,John Blake Jr. (politician),Politician,95,25692,"What is John Blake, Jr.'s occupation?","[""politician"", ""political leader"", ""political figure"", ""polit."", ""pol""]",2,2,"['politician', 'political leader', 'political figure', 'polit.', 'pol', 'judge', 'musician', 'university teacher', 'jazz musician']",American politician (1762-1826),"What was John Blake, Jr.'s (1762–1826) occupation?",True,"What was the occupation of John Blake, Jr. (1762–1826)?"
3073609,Andreas Rüdiger,occupation,philosopher,1312723,22,1543144,"[""Andreas Johannes Ridiger""]",[],http://www.wikidata.org/entity/Q4402242,http://www.wikidata.org/entity/Q4964182,Andreas Rüdiger,Philosopher,66,25747,What is Andreas Rüdiger's occupation?,"[""philosopher""]",5,3,"['philosopher', 'stage actor', 'film actor', 'publisher', 'bookseller', 'physicist']",German philosopher and physicist (1673-1731),"What was Andreas Rüdiger's occupation, the German physicist (1673–1731)?",True,"What was the occupation of Andreas Rüdiger, the German (1673–1731)?"
5604342,Robert Lewis,occupation,politician,2497580,22,2834605,[],"[""political leader"",""political figure"",""polit."",""pol""]",http://www.wikidata.org/entity/Q7346877,http://www.wikidata.org/entity/Q82955,Robert Lewis (MP),Politician,64,25692,What is Robert Lewis's occupation?,"[""politician"", ""political leader"", ""political figure"", ""polit."", ""pol""]",30,16,"['politician', 'political leader', 'political figure', 'polit.', 'pol', 'actor', 'writer', 'screenwriter', 'rugby union player', 'rugby league player', 'drafter', 'researcher', 'author', 'television producer', 'university teacher', 'film director', 'stage actor', 'film producer', 'director', 'theatrical director', 'film actor', 'association football player', 'journalist', 'opera director', 'painter', 'basketball player', 'printmaker']",English politician,What is Robert Lewis's occupation?,False,What is Robert Lewis's profession?
2573242,Pierre Abraham,occupation,journalist,1110137,22,663400,[],"[""journo"",""journalists""]",http://www.wikidata.org/entity/Q3383636,http://www.wikidata.org/entity/Q1930187,Pierre Abraham,Journalist,76,24952,What is Pierre Abraham's occupation?,"[""journalist"", ""journo"", ""journalists""]",2,2,"['journalist', 'journo', 'journalists', 'biographer', 'aircraft pilot', 'literary critic', 'encyclopedist', 'researcher', 'French resistance fighter', 'writer']","French journalist, encyclopedist and military figure in the French Air Force","What is Pierre Abraham's occupation, who was a French encyclopedist and military figure in the French Air Force?",True,"What is Pierre Abraham's occupation, who was a French encyclopedist and military figure?"
5978662,Thomas Gifford,occupation,politician,2684206,22,2834605,[],"[""political leader"",""political figure"",""polit."",""pol""]",http://www.wikidata.org/entity/Q7790024,http://www.wikidata.org/entity/Q82955,Thomas Gifford (politician),Politician,55,25692,What is Thomas Gifford's occupation?,"[""politician"", ""political leader"", ""political figure"", ""polit."", ""pol""]",6,2,"['politician', 'political leader', 'political figure', 'polit.', 'pol', 'writer', 'novelist']",Canadian politician,What is Thomas Gifford's occupation as a Canadian citizen?,True,What is Thomas Gifford's occupation as a Canadian?
5577511,Richard Myers,occupation,songwriter,2485785,22,2577376,[],"[""song writer""]",http://www.wikidata.org/entity/Q7327974,http://www.wikidata.org/entity/Q753110,Richard Myers (songwriter),Songwriter,85,25550,What is Richard Myers's occupation?,"[""songwriter"", ""song writer""]",5,2,"['songwriter', 'song writer', 'film editor', 'screenwriter', 'composer', 'cinematographer', 'film director']",American songwriter (1901-1977),What was Richard Myers's (1901–1977) occupation?,True,What was Richard Myers's (1901–1977) profession?
836769,Jean Gabriel Marie,occupation,composer,356988,22,1189033,[],[],http://www.wikidata.org/entity/Q16006794,http://www.wikidata.org/entity/Q36834,Jean Gabriel Marie (1907–1970),Composer,72,28389,What is Jean Gabriel Marie's occupation?,"[""composer""]",3,2,"['composer', 'conductor']",French composer,"What is the occupation of Jean Gabriel Marie, the French artist?",True,"What is the occupation of Jean Gabriel Marie, the French?"
4218380,George Lewis,occupation,politician,1848887,22,2834605,[],"[""political leader"",""political figure"",""polit."",""pol""]",http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q82955,George Lewis (politician),Politician,68,25692,What is George Lewis's occupation?,"[""politician"", ""political leader"", ""political figure"", ""polit."", ""pol""]",31,25,"['politician', 'political leader', 'political figure', 'polit.', 'pol', 'writer', 'military personnel', 'poet', 'conductor', 'bandleader', 'clarinetist', 'association football player', 'war correspondent', 'bookseller', 'journalist', 'musician', 'university teacher', 'saxophonist', 'jazz musician', 'composer', 'rugby union player', 'rugby league player', 'badminton player', 'ship-owner', 'author', 'Christian minister', 'cleric', 'recording artist', 'painter', 'cobbler', 'entomologist', 'theologian', 'sprinter', 'priest']",American politician,What is George Lewis's occupation?,False,What is George Lewis's profession?
5985932,Thomas Widdrington,occupation,politician,2687244,22,2834605,[],"[""political leader"",""political figure"",""polit."",""pol""]",http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q82955,Thomas Widdrington (died 1660),Politician,45,25692,What is Thomas Widdrington's occupation?,"[""politician"", ""political leader"", ""political figure"", ""polit."", ""pol""]",4,2,"['politician', 'political leader', 'political figure', 'polit.', 'pol', 'judge', 'barrister']",English politician,What is Thomas Widdrington's occupation?,False,What is the occupation of Thomas Widdrington?
1158868,Jorge Traverso,occupation,journalist,512908,22,663400,[],"[""journo"",""journalists""]",http://www.wikidata.org/entity/Q1703940,http://www.wikidata.org/entity/Q1930187,Jorge Traverso,Journalist,43,24952,What is Jorge Traverso's occupation?,"[""journalist"", ""journo"", ""journalists""]",3,2,"['journalist', 'journo', 'journalists', 'writer', 'association football player', 'television presenter']",Uruguayan journalist,"What is Jorge Traverso's occupation, who is Uruguayan?",True,"What is the occupation of Jorge Traverso, who is Uruguayan?"
5378331,Paolo Pellizzari,occupation,photographer,2391567,22,1092260,[],"[""photog"",""photographers""]",http://www.wikidata.org/entity/Q7132229,http://www.wikidata.org/entity/Q33231,Paolo Pellizzari,Photographer,74,10855,What is Paolo Pellizzari's occupation?,"[""photographer"", ""photog"", ""photographers""]",3,2,"['photographer', 'photog', 'photographers', 'researcher']",Italian photographer,What is Paolo Pellizzari's occupation as an Italian professional?,True,What is Paolo Pellizzari's occupation as an Italian?
3036567,Aleksandr Nekrasov,place of birth,Moscow,1299441,218,2169018,"[""Aleksandr Ivanovich Nekrasov""]","[""Moskva"",""Moscow, Russia"",""Moskva Federal City, Russia"",""Moscow, USSR"",""Moskva, Russia"",""City of Moscow"",""Moscow, Russian Federation"",""Moscow, Soviet Union"",""Moscow, Russian SFSR"",""Muscovite"",""Moscovite""]",http://www.wikidata.org/entity/Q4316559,http://www.wikidata.org/entity/Q649,Aleksandr Nekrasov,Moscow,80,149374,In what city was Aleksandr Nekrasov born?,"[""Moscow"", ""Moskva"", ""Moscow, Russia"", ""Moskva Federal City, Russia"", ""Moscow, USSR"", ""Moskva, Russia"", ""City of Moscow"", ""Moscow, Russian Federation"", ""Moscow, Soviet Union"", ""Moscow, Russian SFSR"", ""Muscovite"", ""Moscovite""]",4,3,"['Moscow', 'Moskva', 'Moscow, Russia', 'Moskva Federal City, Russia', 'Moscow, USSR', 'Moskva, Russia', 'City of Moscow', 'Moscow, Russian Federation', 'Moscow, Soviet Union', 'Moscow, Russian SFSR', 'Muscovite', 'Moscovite', 'Saint Petersburg', 'Kyruv', 'Komi-Zyryan Autonomous Oblast']",Russian mathematician,In what city was the Russian mathematician Aleksandr Nekrasov born?,True,"In what city was Aleksandr Nekrasov, the Russian mathematician, born?"
298892,Scott Fraser,place of birth,Edinburgh,120838,218,799769,[],"[""Edinburg"",""Edinburgh, Scotland"",""City of Edinburgh"",""Edina"",""Modern Athens""]",http://www.wikidata.org/entity/Q12051104,http://www.wikidata.org/entity/Q23436,Scott Fraser (orienteer),Edinburgh,32,114880,In what city was Scott Fraser born?,"[""Edinburgh"", ""Edinburg"", ""Edinburgh, Scotland"", ""City of Edinburgh"", ""Edina"", ""Modern Athens""]",9,7,"['Edinburgh', 'Edinburg', 'Edinburgh, Scotland', 'City of Edinburgh', 'Edina', 'Modern Athens', 'Shubenacadie', 'Moncton', 'Ottawa', 'Evanston', 'Dundee']",British orienteer,In what city was the British orienteer Scott Fraser born?,True,"In what city was Scott Fraser, the British orienteer, born?"
880910,Giuseppe Castelli,place of birth,Milan,373716,218,1508594,[],"[""Milano"",""Milan, Italy"",""Milano, Italy"",""Milano, Italia"",""Mailand"",""Milan Records""]",http://www.wikidata.org/entity/Q1608286,http://www.wikidata.org/entity/Q490,Giuseppe Castelli (footballer),Milan,40,110644,In what city was Giuseppe Castelli born?,"[""Milan"", ""Milano"", ""Milan, Italy"", ""Milano, Italy"", ""Milano, Italia"", ""Mailand"", ""Milan Records""]",9,7,"['Milan', 'Milano', 'Milan, Italy', 'Milano, Italy', 'Milano, Italia', 'Mailand', 'Milan Records', 'Pavia', 'Livorno Ferraris', 'Ripatransone', 'Frugarolo', 'San Gillio', 'Ascoli Piceno']",Italian footballer (1919-1971),In what city was the Italian footballer Giuseppe Castelli (1919–1971) born?,True,"In what city was Giuseppe Castelli (1919–1971), the Italian footballer, born?"
5440620,Phil Williams,place of birth,Birkenhead,2418885,218,2557618,[],[],http://www.wikidata.org/entity/Q7182553,http://www.wikidata.org/entity/Q746718,"Phil Williams (footballer, born 1958)",Birkenhead,23,12328,In what city was Phil Williams born?,"[""Birkenhead""]",16,5,"['Birkenhead', 'Tredegar', 'Fort Monmouth', 'Swansea', 'Grassy']",English footballer (born 1958),"In what city was Phil Williams, the English footballer born in 1958, born?",True,"In what city was Phil Williams, the English footballer born in 1958, born?"
5117704,Matt Moralee,place of birth,Newcastle upon Tyne,2262515,218,232446,"[""Matthew Whitfield Moralee""]","[""Newcastle"",""Newcastle-on-Tyne"",""Newcastle upon Tyne (parish)"",""Newcastle-upon-Tyne""]",http://www.wikidata.org/entity/Q6789088,http://www.wikidata.org/entity/Q1425428,Matt Moralee,Newcastle upon Tyne,55,110066,In what city was Matt Moralee born?,"[""Newcastle upon Tyne"", ""Newcastle"", ""Newcastle-on-Tyne"", ""Newcastle upon Tyne (parish)"", ""Newcastle-upon-Tyne""]",2,2,"['Newcastle upon Tyne', 'Newcastle', 'Newcastle-on-Tyne', 'Newcastle upon Tyne (parish)', 'Newcastle-upon-Tyne', 'Barnburgh']",English footballer (1878-1962),"In what city was Matt Moralee, the English footballer (1878–1962), born?",True,"In what city was Matt Moralee, the Englishman (1878–1962), born?"
5006548,Lloyd Ultan,place of birth,New York City,2210395,218,2005387,[],"[""NYC"",""New York"",""the five boroughs"",""Big Apple"",""City of New York"",""NY City"",""New York, New York"",""New York City, New York"",""New York, NY"",""New York City (NYC)""]",http://www.wikidata.org/entity/Q6662783,http://www.wikidata.org/entity/Q60,Lloyd Ultan (composer),New York City,47,718380,In what city was Lloyd Ultan born?,"[""New York City"", ""NYC"", ""New York"", ""the five boroughs"", ""Big Apple"", ""City of New York"", ""NY City"", ""New York, New York"", ""New York City, New York"", ""New York, NY"", ""New York City (NYC)""]",3,2,"['New York City', 'NYC', 'New York', 'the five boroughs', 'Big Apple', 'City of New York', 'NY City', 'New York, New York', 'New York City, New York', 'New York, NY', 'New York City (NYC)', 'The Bronx']",American composer (1929-1998),In what city was the American composer (1929–1998) Lloyd Ultan born?,True,"In what city was Lloyd Ultan, the American composer (1929–1998), born?"
4246734,Jim Baker,place of birth,Ilkeston,1860645,218,723177,"[""James William Baker""]",[],http://www.wikidata.org/entity/Q5564436,http://www.wikidata.org/entity/Q2061067,Jim Baker (footballer),Ilkeston,87,3491,In what city was Jim Baker born?,"[""Ilkeston""]",22,8,"['Ilkeston', 'Owensboro', 'Belfast', 'Buffalo', 'Chicago', 'Belleville', 'Marystown', 'United States of America']",English footballer (1891-1966),In what city was the English footballer Jim Baker (1891–1966) born?,True,"In what city was Jim Baker (1891–1966), the English footballer, born?"
4674280,Jim,place of birth,Arizona Territory,2059402,218,2222998,"[""Bow-os-loh""]",[],http://www.wikidata.org/entity/Q6193276,http://www.wikidata.org/entity/Q670002,Jim (Medal of Honor recipient),Arizona Territory,97,7679,In what city was Jim born?,"[""Arizona Territory""]",36,2,"['Arizona Territory', 'Niort']",Apache Indian scout,"In what city was Jim, an Apache Indian scout, born?",True,"""In what city was Jim, an Apache Indian scout, born?"""
2842700,James Brown,place of birth,Leith,1219416,218,7867,[],[],http://www.wikidata.org/entity/Q3806473,http://www.wikidata.org/entity/Q1018540,"James Brown (footballer, born 1907)",Leith,73,11495,In what city was James Brown born?,"[""Leith""]",144,47,"['Leith', 'Los Angeles', 'Augusta', 'Norwalk', 'Barnwell', 'Dalkeith', 'Portaferry', 'Cardross', 'England', 'Wolverhampton', 'Spott', 'Dunoon', 'Desdemona', 'Sandbank', 'Baltimore', 'Beaumont', 'Perth', 'Acton', 'Santa Clara', 'El Dorado', 'Dundee', 'Barnard Castle', 'Croydon', 'Glasgow', 'Ballymena', 'Birmingham', 'Cramlington', 'Leeds', 'Greenock', 'Fife', 'Rochester', 'London', 'New York City', 'Dalry', 'Scotland', 'Dover', 'Chicago', 'Philadelphia', 'Ohio', 'San Jose', 'Sydney', 'Paisley', 'Washington, D.C.', 'Staunton', 'Blackburn', 'Belfast']",Scottish footballer (born 1907),"In what city was James Brown, the Scottish footballer born in 1907, born?",True,"In what city was James Brown, the Scottish footballer born in 1907?"
3387429,Arthur Thomson,place of birth,Edinburgh,1456405,218,799769,"[""Arthur Campbell Thomson""]","[""Edinburg"",""Edinburgh, Scotland"",""City of Edinburgh"",""Edina"",""Modern Athens""]",http://www.wikidata.org/entity/Q4800459,http://www.wikidata.org/entity/Q23436,"Arthur Thomson (footballer, born 1948)",Edinburgh,35,114880,In what city was Arthur Thomson born?,"[""Edinburgh"", ""Edinburg"", ""Edinburgh, Scotland"", ""City of Edinburgh"", ""Edina"", ""Modern Athens""]",10,4,"['Edinburgh', 'Edinburg', 'Edinburgh, Scotland', 'City of Edinburgh', 'Edina', 'Modern Athens', 'Mönsterås', 'Durham', 'Glasgow']",Scottish footballer (1948-2002),"In what city was Arthur Thomson, the Scottish footballer (1948–2002), born?",True,"In what city was Arthur Thomson, the Scottish (1948–2002), born?"
714110,Giovanni Speranza,place of birth,Gießen,303327,218,1236153,[],"[""Giessen""]",http://www.wikidata.org/entity/Q1526380,http://www.wikidata.org/entity/Q3874,Giovanni Speranza,Giessen,68,3348,In what city was Giovanni Speranza born?,"[""Gießen"", ""Giessen""]",2,2,"['Gießen', 'Giessen', 'Vicenza']",Italian footballer,In what city was the Italian footballer Giovanni Speranza born?,True,"In what city was Giovanni Speranza, the Italian, born?"
5405575,Paul Walker,place of birth,Kilwinning,2403259,218,5828,[],"[""The Crossroads of Ayrshire""]",http://www.wikidata.org/entity/Q7154216,http://www.wikidata.org/entity/Q1013827,"Paul Walker (footballer, born 1977)",Kilwinning,74,2127,In what city was Paul Walker born?,"[""Kilwinning"", ""The Crossroads of Ayrshire""]",28,8,"['Kilwinning', 'The Crossroads of Ayrshire', 'Springfield', 'Bradford', 'England', 'Leicestershire', 'Glendale', 'Newton', 'Wood Green', 'Leicester']",Scottish footballer (born 1977),"In what city was Paul Walker, the Scottish footballer born in 1977, born?",True,"In what city was Paul Walker, the Scottish footballer born in 1977?"
1609681,Sebastian Hofmann,place of birth,Sinsheim,717275,218,278455,[],[],http://www.wikidata.org/entity/Q204163,http://www.wikidata.org/entity/Q14950,Sebastian Hofmann,Sinsheim,67,3584,In what city was Sebastian Hofmann born?,"[""Sinsheim""]",2,2,"['Sinsheim', 'Mexico City']",German footballer,In what city was the German footballer Sebastian Hofmann born?,True,In what city was the German Sebastian Hofmann born?
6450718,Ricardo Sánchez,place of birth,Guadalajara,2901573,218,2876737,"[""Ricardo Sanchez""]","[""Guadalajara, Jalisco"",""Guadalajara, Jalisco, Mexico"",""Guadalajara, Mexico"",""Guadalajara, M\u00e9xico""]",http://www.wikidata.org/entity/Q935978,http://www.wikidata.org/entity/Q9022,Ricardo Sánchez (footballer),Guadalajara,81,42420,In what city was Ricardo Sánchez born?,"[""Guadalajara"", ""Guadalajara, Jalisco"", ""Guadalajara, Jalisco, Mexico"", ""Guadalajara, Mexico"", ""Guadalajara, México""]",9,4,"['Guadalajara', 'Guadalajara, Jalisco', 'Guadalajara, Jalisco, Mexico', 'Guadalajara, Mexico', 'Guadalajara, México', 'Madrid', 'Roade, Sobrado', 'Montevideo']",Mexican footballer,In what city was the Mexican footballer Ricardo Sánchez born?,True,"In what city was Ricardo Sánchez, the Mexican footballer, born?"
4591465,Izumi Iimura,place of birth,Japan,2024915,218,502871,[],"[""State of Japan"",""Land of the Rising Sun"",""Nihon"",""Nippon"",""JP"",""Nippon-koku"",""Nihon-koku"",""JA"",""JPN"",""jp"",""JAP"",""Ja"",""Jap""]",http://www.wikidata.org/entity/Q6102286,http://www.wikidata.org/entity/Q17,Izumi Iimura,Japan,30,702414,In what city was Izumi Iimura born?,"[""Japan"", ""State of Japan"", ""Land of the Rising Sun"", ""Nihon"", ""Nippon"", ""JP"", ""Nippon-koku"", ""Nihon-koku"", ""JA"", ""JPN"", ""jp"", ""JAP"", ""Ja"", ""Jap""]",3,2,"['Japan', 'State of Japan', 'Land of the Rising Sun', 'Nihon', 'Nippon', 'JP', 'Nippon-koku', 'Nihon-koku', 'JA', 'JPN', 'jp', 'JAP', 'Ja', 'Jap', 'Saitama Prefecture']",Amateur international cricketer,In what city was the amateur international cricketer Izumi Iimura born?,True,"In what city was Izumi Iimura, the amateur international cricketer, born?"
3270447,Aleksandar Madžar,place of birth,Bar,1403583,218,89502,[],"[""Tivar"",""Tivari"",""Antivari"",""Antibari"",""Stari Bar""]",http://www.wikidata.org/entity/Q4714811,http://www.wikidata.org/entity/Q115276,Aleksandar Madžar (soccer),"Bar, Montenegro",58,4310,In what city was Aleksandar Madžar born?,"[""Bar"", ""Tivar"", ""Tivari"", ""Antivari"", ""Antibari"", ""Stari Bar""]",3,2,"['Bar', 'Tivar', 'Tivari', 'Antivari', 'Antibari', 'Stari Bar', 'Belgrade']",Montenegrin footballer,In what city was the Montenegrin footballer Aleksandar Madžar born?,True,"In what city was Aleksandar Madžar, the Montenegrin footballer, born?"
5117100,Matt Hamilton,place of birth,Hemel Hempstead,2262286,218,1155207,[],"[""Hemel""]",http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q3532,Matt Hamilton (racing driver),Hemel Hempstead,94,13923,In what city was Matt Hamilton born?,"[""Hemel Hempstead"", ""Hemel""]",4,2,"['Hemel Hempstead', 'Hemel', 'Madison']",British racing driver,In what city was the British racing driver Matt Hamilton born?,True,"In what city was Matt Hamilton, the British racing driver, born?"
4733160,John Robinson,place of birth,Mansfield,2082173,218,2936142,"[""John Sherman Robinson""]","[""Mansfield, Ohio""]",http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q983698,John Robinson (judge),"Mansfield, Ohio",45,7275,In what city was John Robinson born?,"[""Mansfield"", ""Mansfield, Ohio""]",122,28,"['Mansfield', 'Mansfield, Ohio', 'Chicago', 'Java', 'Northern Ireland', 'Cumberland County', 'Arnold', 'http://www.wikidata.org/.well-known/genid/5776cab9f78049d73c00d5a7bb728c87', 'Huntington', 'Canterbury', 'http://www.wikidata.org/.well-known/genid/9c0d711e1878f642aedd3fd5b2681956', 'Leeds', 'Kingston upon Hull', 'Middlesex County', 'http://www.wikidata.org/.well-known/genid/c2dbdf5ef7b90850f6cdde3acc4a60cc', 'Sturton le Steeple', 'St Kilda', 'Bulawayo', 'Bristol', 'Cleasby', 'Zumbro Township', 'Liverpool', 'Meath', 'Carrabelle', 'Portland', 'Cuba', 'London', 'Creston']","athlete and judge from Ohio, United States, born 1880","In what city was John Robinson, an athlete and judge from Ohio, United States, born in 1880?",True,"In what city was John Robinson, an Ohio native from the United States born in 1880?"
5829140,Steve McDonald,place of birth,Birmingham,2608970,218,776640,[],"[""Birmingham, England"",""Birmingham, West Midlands""]",http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q2256,Steve McDonald (cricketer),Birmingham,33,119797,In what city was Steve McDonald born?,"[""Birmingham"", ""Birmingham, England"", ""Birmingham, West Midlands""]",7,2,"['Birmingham', 'Birmingham, England', 'Birmingham, West Midlands', 'Glasgow']",English cricketer (born 1974),"In what city was Steve McDonald, the English cricketer born in 1974, born?",True,"In what city was Steve McDonald, the English cricketer born in 1974?"
1185324,Pierre Petit,place of birth,Paris,527937,218,2874868,[],"[""City of Light"",""Paris, France""]",http://www.wikidata.org/entity/Q171292,http://www.wikidata.org/entity/Q90,Pierre Petit (scholar),Paris,98,265592,In what city was Pierre Petit born?,"[""Paris"", ""City of Light"", ""Paris, France""]",26,10,"['Paris', 'City of Light', 'Paris, France', 'Aups', 'Guéret', 'Montluçon', 'Poitiers', 'Liège', 'Fontenay-Trésigny', 'Le Morne-Rouge', '11th arrondissement of Paris']","French scholar, physician and poet (1617–1687)","In what city was Pierre Petit, the French scholar, physician, and poet (1617–1687), born?",True,"In what city was Pierre Petit, the French scholar, physician, and poet (1617–1687), born?"
5613652,Robin Williams,place of birth,Christchurch,2501326,218,2782127,"[""Robert Martin Williams"",""Robert Martin \""Robin\"" Williams"",""Robert M. Williams"",""Robin M. Williams"",""Bob Williams""]","[""Christchurch, New Zealand""]",http://www.wikidata.org/entity/Q7352629,http://www.wikidata.org/entity/Q79990,Robin Williams (mathematician),Christchurch,82,33178,In what city was Robin Williams born?,"[""Christchurch"", ""Christchurch, New Zealand""]",17,8,"['Christchurch', 'Christchurch, New Zealand', 'London', 'Anglesey', 'Berkeley', 'Chicago', 'Pontypool', 'Rhoslan', 'Stellenbosch']","New Zealand mathematician, public servant, university administrator","In what city was Robin Williams, the New Zealand mathematician, public servant, and university administrator, born?",True,"In what city was Robin Williams, the New Zealand mathematician, public servant, and university administrator, born?"
251876,John Keating,place of birth,Hobart,101473,218,1261300,"[""John Henry Keating""]","[""Hobart Town"",""Hobarton"",""Hobart, Tasmania"",""Hobart, Tas.""]",http://www.wikidata.org/entity/Q11727915,http://www.wikidata.org/entity/Q40191,John Keating (Australian politician),Hobart,87,25121,In what city was John Keating born?,"[""Hobart"", ""Hobart Town"", ""Hobarton"", ""Hobart, Tasmania"", ""Hobart, Tas.""]",16,3,"['Hobart', 'Hobart Town', 'Hobarton', 'Hobart, Tasmania', 'Hobart, Tas.', 'Ireland', 'Madison Heights']",Australian  politician,In what city was the Australian politician John Keating born?,True,In what city was the Australian politician John Keating's birthplace?
181481,Jurgis Karnavičius,place of birth,Vilnius,73292,218,752152,[],"[""Vilna"",""Wilno"",""Vilne"",""Wilna"",""Vi\u013c\u0146a"",""Vilnia"",""Vilno"",""Vilnyus""]",http://www.wikidata.org/entity/Q11259521,http://www.wikidata.org/entity/Q216,Jurgis Karnavičius,Vilnius,69,42978,In what city was Jurgis Karnavičius born?,"[""Vilnius"", ""Vilna"", ""Wilno"", ""Vilne"", ""Wilna"", ""Viļņa"", ""Vilnia"", ""Vilno"", ""Vilnyus""]",3,3,"['Vilnius', 'Vilna', 'Wilno', 'Vilne', 'Wilna', 'Viļņa', 'Vilnia', 'Vilno', 'Vilnyus', 'Saint Petersburg', 'Kaunas']",Lithuanian musician,In what city was the Lithuanian musician Jurgis Karnavičius born?,True,In what city was the Lithuanian musician Jurgis Karnavičius born?
6445857,Xue Fei,place of birth,Beijing,2899617,218,2916491,[],"[""Peking"",""Beiping"",""Peiping"",""Yanjing"",""Zhongdu"",""Khanbaliq"",""BJ"",""Shun Tian Fu"",""Pekin"",""beijing""]",http://www.wikidata.org/entity/Q9339095,http://www.wikidata.org/entity/Q956,Xue Fei (footballer),Beijing,65,125585,In what city was Xue Fei born?,"[""Beijing"", ""Peking"", ""Beiping"", ""Peiping"", ""Yanjing"", ""Zhongdu"", ""Khanbaliq"", ""BJ"", ""Shun Tian Fu"", ""Pekin"", ""beijing""]",6,3,"['Beijing', 'Peking', 'Beiping', 'Peiping', 'Yanjing', 'Zhongdu', 'Khanbaliq', 'BJ', 'Shun Tian Fu', 'Pekin', 'beijing', 'Shandong']",footballer,"In what city was Xue Fei, the footballer, born?",True,"""In what city was Xue Fei born?"""
5708121,Scott Patterson,place of birth,Vancouver,2546647,218,835440,[],"[""City of Vancouver"",""Vancouver, BC"",""Vancouver, British Columbia""]",http://www.wikidata.org/entity/Q7437016,http://www.wikidata.org/entity/Q24639,Scott Patterson (Paralympian),Vancouver,42,145635,In what city was Scott Patterson born?,"[""Vancouver"", ""City of Vancouver"", ""Vancouver, BC"", ""Vancouver, British Columbia""]",10,7,"['Vancouver', 'City of Vancouver', 'Vancouver, BC', 'Vancouver, British Columbia', 'McCall', 'East Orange', 'Philadelphia', 'Oshawa', 'Oakdale', 'Crows Nest']","Canadian Paralympic athlete, skier and swimmer","In what city was the Canadian Paralympic athlete, skier, and swimmer Scott Patterson born?",True,In what city was the Canadian Paralympic athlete Scott Patterson born?
551266,Fernando García,place of birth,Santiago,224734,218,957791,"[""Fernando Garcia""]","[""Santiago de Chile"",""Santiago, Chile""]",http://www.wikidata.org/entity/Q1406681,http://www.wikidata.org/entity/Q2887,Fernando García (composer),Santiago,87,41610,In what city was Fernando García born?,"[""Santiago"", ""Santiago de Chile"", ""Santiago, Chile""]",11,3,"['Santiago', 'Santiago de Chile', 'Santiago, Chile', 'Pimentel', 'Madrid']",Chilean musician,In what city was the Chilean musician Fernando García born?,True,"In what city was Fernando García, the Chilean musician, born?"
178789,György Kárpáti,place of birth,Budapest,72245,218,573857,[],"[""Buda Pest"",""Buda-Pest"",""Budape\u0161\u0165"",""Budapesta"",""Budapeszt"",""Buda"",""Ofen"",""Bud\u00edn"",""Budim"",""Budon"",""Pest"",""Pe\u0161\u0165"",""Pe\u0161ta"",""\u00d3buda"",""Alt-Ofen"",""K\u0151b\u00e1nya""]",http://www.wikidata.org/entity/Q1123606,http://www.wikidata.org/entity/Q1781,György Kárpáti (film director),Budapest,72,134197,In what city was György Kárpáti born?,"[""Budapest"", ""Buda Pest"", ""Buda-Pest"", ""Budapešť"", ""Budapesta"", ""Budapeszt"", ""Buda"", ""Ofen"", ""Budín"", ""Budim"", ""Budon"", ""Pest"", ""Pešť"", ""Pešta"", ""Óbuda"", ""Alt-Ofen"", ""Kőbánya""]",3,3,"['Budapest', 'Buda Pest', 'Buda-Pest', 'Budapešť', 'Budapesta', 'Budapeszt', 'Buda', 'Ofen', 'Budín', 'Budim', 'Budon', 'Pest', 'Pešť', 'Pešta', 'Óbuda', 'Alt-Ofen', 'Kőbánya']",Hungarian film director (1933–2023),"In what city was György Kárpáti, the Hungarian film director (1933–2023), born?",True,"In what city was György Kárpáti, the Hungarian (1933–2023), born?"
2332141,Frederick Mackenzie,place of birth,Montreal,1017346,218,1114198,[],"[""Montr\u00e9al"",""City of Montreal"",""Montreal, Quebec"",""Ville de Montr\u00e9al"",""Ville de Montreal""]",http://www.wikidata.org/entity/Q3087132,http://www.wikidata.org/entity/Q340,Frederick Mackenzie (Quebec politician),Montreal,78,131670,In what city was Frederick Mackenzie born?,"[""Montreal"", ""Montréal"", ""City of Montreal"", ""Montreal, Quebec"", ""Ville de Montréal"", ""Ville de Montreal""]",5,3,"['Montreal', 'Montréal', 'City of Montreal', 'Montreal, Quebec', 'Ville de Montréal', 'Ville de Montreal', 'Kensington', 'http://www.wikidata.org/.well-known/genid/c6289e3577237125b59b5ee2d1ea356f']",Canadian politician (1841-1889),"In what city was Frederick Mackenzie, the Canadian politician (1841-1889), born?",True,"In what city was Frederick Mackenzie, the Canadian (1841-1889), born?"
268085,Gerhard Ludwig,place of birth,Berlin,108323,218,2135299,[],"[""Berlin, Germany"",""Berlin (Germany)"",""DE-BE""]",http://www.wikidata.org/entity/Q118469,http://www.wikidata.org/entity/Q64,Gerhard Ludwig,Berlin,47,163000,In what city was Gerhard Ludwig born?,"[""Berlin"", ""Berlin, Germany"", ""Berlin (Germany)"", ""DE-BE""]",6,2,"['Berlin', 'Berlin, Germany', 'Berlin (Germany)', 'DE-BE', 'Berg']",German bookseller,"In what city was Gerhard Ludwig, the German bookseller, born?",True,"""In what city was Gerhard Ludwig, the German, born?"""
816703,Anthony Gale,place of birth,Toronto,348944,218,534176,[],"[""City of Toronto"",""The Six"",""T-O"",""The 416"",""Hogtown""]",http://www.wikidata.org/entity/Q15979653,http://www.wikidata.org/entity/Q172,Anthony Gale (sledge hockey),Toronto,69,208499,In what city was Anthony Gale born?,"[""Toronto"", ""City of Toronto"", ""The Six"", ""T-O"", ""The 416"", ""Hogtown""]",3,2,"['Toronto', 'City of Toronto', 'The Six', 'T-O', 'The 416', 'Hogtown', 'Ireland']",ice sledge hockey,"In what city was Anthony Gale, associated with ice sledge hockey, born?",True,"""In what city was Anthony Gale, known for his involvement in ice sledge hockey, born?"""
3048920,Javier Suárez,place of birth,Madrid,1303604,218,936498,"[""Javier Suarez""]","[""City of Madrid"",""Madrid, Spain""]",http://www.wikidata.org/entity/Q434845,http://www.wikidata.org/entity/Q2807,Javier Suárez (economist),Madrid,96,108266,In what city was Javier Suárez born?,"[""Madrid"", ""City of Madrid"", ""Madrid, Spain""]",6,2,"['Madrid', 'City of Madrid', 'Madrid, Spain', 'Antioquia Department']",Spanish economist,In what city was the Spanish economist Javier Suárez born?,True,"In what city was Javier Suárez, the Spanish economist, born?"
787981,Kazuhiro Suzuki,place of birth,Tokyo,336142,218,273933,[],"[""T\u014dky\u014d"",""T\u00f4ky\u00f4"",""Tokyo-to"",""Tokyo Metropolitan prefecture"",""T\u014dky\u014d-to"",""T\u00f4ky\u00f4-to"",""Tokyo Metropolis"",""Tokio"",""Tokyo Prefecture""]",http://www.wikidata.org/entity/Q1574414,http://www.wikidata.org/entity/Q1490,Kazuhiro Suzuki,Tokyo,78,205487,In what city was Kazuhiro Suzuki born?,"[""Tokyo"", ""Tōkyō"", ""Tôkyô"", ""Tokyo-to"", ""Tokyo Metropolitan prefecture"", ""Tōkyō-to"", ""Tôkyô-to"", ""Tokyo Metropolis"", ""Tokio"", ""Tokyo Prefecture""]",7,4,"['Tokyo', 'Tōkyō', 'Tôkyô', 'Tokyo-to', 'Tokyo Metropolitan prefecture', 'Tōkyō-to', 'Tôkyô-to', 'Tokyo Metropolis', 'Tokio', 'Tokyo Prefecture', 'Kawasaki', 'Hokkaido']",Japanese association football player,In what city was the Japanese association football player Kazuhiro Suzuki born?,True,"In what city was Kazuhiro Suzuki, the Japanese player, born?"
4306603,Gösta Eriksson,place of birth,Vaxholm,1889741,218,524,"[""G\u00f6sta Vilhelm Eriksson""]",[],http://www.wikidata.org/entity/Q5626679,http://www.wikidata.org/entity/Q1001109,Gösta Eriksson (rowing),Vaxholm,20,847,In what city was Gösta Eriksson born?,"[""Vaxholm""]",7,2,"['Vaxholm', 'Skee']",Swedish coxswain,In what city was the Swedish coxswain Gösta Eriksson born?,True,"In what city was Gösta Eriksson, the Swedish coxswain, born?"
5169150,Mikael Eriksson,place of birth,Karlskoga,2287144,218,1811026,[],[],http://www.wikidata.org/entity/Q6845600,http://www.wikidata.org/entity/Q54732,Mikael Eriksson (ice hockey),Karlskoga,50,1317,In what city was Mikael Eriksson born?,"[""Karlskoga""]",10,2,"['Karlskoga', 'Älvdalen']",Swedish ice hockey player,In what city was the Swedish ice hockey player Mikael Eriksson born?,True,In what city was the Swedish athlete Mikael Eriksson born?
1208142,Liam Carroll,place of birth,Kinnitty,537981,218,757258,[],[],http://www.wikidata.org/entity/Q17305760,http://www.wikidata.org/entity/Q2181821,Liam Carroll (hurler),Kinnitty,43,343,In what city was Liam Carroll born?,"[""Kinnitty""]",4,2,"['Kinnitty', 'Dundalk']",Irish hurler,"In what city was Liam Carroll, the Irish hurler, born?",True,"In what city was Liam Carroll, the Irish, born?"
4210200,George Chambers,place of birth,Kimberley,1846006,218,835762,"[""George Henry Chambers""]","[""Kimberley, Nottinghamshire""]",http://www.wikidata.org/entity/Q5537801,http://www.wikidata.org/entity/Q2464975,"George Chambers (cricketer, born 1884)","Kimberley, Nottinghamshire",25,574,In what city was George Chambers born?,"[""Kimberley"", ""Kimberley, Nottinghamshire""]",13,5,"['Kimberley', 'Kimberley, Nottinghamshire', 'Whitby', 'Port of Spain', 'Chambersburg', 'London']",English cricketer (1884-1947),"In what city was George Chambers, the English cricketer (1884–1947), born?",True,"In what city was George Chambers, the English (1884–1947), born?"
3955580,Drive On,genre,pop music,1727609,91,1193964,[],"[""pop"",""Pop""]",http://www.wikidata.org/entity/Q5307918,http://www.wikidata.org/entity/Q37073,Drive On (song),Pop music,90,286065,What genre is Drive On?,"[""pop music"", ""pop"", ""Pop""]",3,2,"['pop music', 'pop', 'Pop', 'rock and roll', 'hard rock', 'glam rock']",single by Brother Beyond,"What genre is Drive On, a single by Brother Beyond?",True,What genre is the single Drive On by Brother Beyond?
5215694,Mother,genre,rock music,2309573,91,82098,[],"[""rock and roll"",""rock"",""Rock""]",http://www.wikidata.org/entity/Q6917290,http://www.wikidata.org/entity/Q11399,Mother (Luna Sea song),Rock music,79,140450,What genre is Mother?,"[""rock music"", ""rock and roll"", ""rock"", ""Rock""]",141,53,"['rock music', 'rock and roll', 'rock', 'Rock', 'drama film', 'pop music', 'thriller film', 'silent film', 'portrait', 'rhythm and blues', 'mystery film', 'indie folk', 'Japanese role-playing video game', 'documentary film', 'hymn text', 'heavy metal', 'metalcore', 'drama television series', 'comedy film', 'hip hop music', 'musical film', 'jazz', 'propaganda film', 'progressive folk', 'new wave', 'J-pop', 'role-playing video game', 'rape and revenge film', 'public art', 'comedy drama', 'progressive metal', 'biographical film', 'hard rock']",1995 song by Luna Sea,"What genre is Mother, the 1995 song by Luna Sea?",True,What is the genre of the 1995 song Mother by Luna Sea?
1184585,Unknown,genre,fantasy,527528,91,171945,[],"[""fantasy fiction""]",http://www.wikidata.org/entity/Q17124808,http://www.wikidata.org/entity/Q132311,Unknown (1988 anthology),Fantasy,42,105540,What genre is Unknown?,"[""fantasy"", ""fantasy fiction""]",85,17,"['fantasy', 'fantasy fiction', 'drama film', 'landscape painting', 'mystery film', 'narration', 'thriller', 'progressive rock', 'crime film', 'J-pop', 'action film', 'film based on a novel', 'genre art', 'silent film', 'hip hop music', 'documentary film', 'drama television program']",1988 anthology,"What genre is Unknown, the 1988 anthology?",True,"What is the genre of Unknown, the 1988 anthology?"
5547174,Reach,genre,country music,2471846,91,2837284,[],"[""country and western"",""country & western"",""country"",""Nashville sound""]",http://www.wikidata.org/entity/Q7300164,http://www.wikidata.org/entity/Q83440,Reach (Meredith Edwards album),Country music,85,91093,What genre is Reach?,"[""country music"", ""country and western"", ""country & western"", ""country"", ""Nashville sound""]",26,9,"['country music', 'country and western', 'country & western', 'country', 'Nashville sound', 'post-hardcore', 'hard rock', 'pop music', 'public art', 'pop-punk']",Meredith Edwards album,"What genre is Reach, the Meredith Edwards album?",True,What genre is the Meredith Edwards album titled *Reach*?
5927764,The Gap,genre,comedy-drama,2658827,91,2852854,"[""Gap""]","[""dramedy"",""comedic drama"",""tragicomedy"",""seriocomedy"",""comedy drama"",""dramatic comedy"",""Comedy-drama, dramedy""]",http://www.wikidata.org/entity/Q7735785,http://www.wikidata.org/entity/Q859369,The Gap (film),Comedy-drama,80,36819,What genre is The Gap?,"[""comedy-drama"", ""dramedy"", ""comedic drama"", ""tragicomedy"", ""seriocomedy"", ""comedy drama"", ""dramatic comedy"", ""Comedy-drama, dramedy""]",49,5,"['comedy-drama', 'dramedy', 'comedic drama', 'tragicomedy', 'seriocomedy', 'comedy drama', 'dramatic comedy', 'Comedy-drama, dramedy', 'pop rock', 'indie rock', 'traditional folk music', 'rhythm and blues', 'gospel music', 'non-fiction']",2006 film,"What genre is The Gap, the 2006 film?",True,"What genre is the 2006 film, *The Gap*?"
3846541,Dark Matter,genre,science fiction,1678288,91,844831,[],"[""SF"",""scifi"",""sci Fi"",""sci-Fi"",""science-fiction"",""sci fi"",""sciencefiction""]",http://www.wikidata.org/entity/Q5223312,http://www.wikidata.org/entity/Q24925,Dark Matter (Reeves-Stevens novel),Science fiction,50,155784,What genre is Dark Matter?,"[""science fiction"", ""SF"", ""scifi"", ""sci Fi"", ""sci-Fi"", ""science-fiction"", ""sci fi"", ""sciencefiction""]",146,17,"['science fiction', 'SF', 'scifi', 'sci Fi', 'sci-Fi', 'science-fiction', 'sci fi', 'sciencefiction', 'grunge', 'hard rock', 'science fiction television program', 'post-hardcore', 'thriller television series', 'instrumental rock', 'speculative fiction', 'panel discussion', 'television series based on a novel', 'role-playing video game', 'alternative rock', 'drama television series', 'documentary film', 'drama film', 'thriller', 'space opera', 'rock music', 'science fiction video game', 'Metroidvania', 'neo-prog']",book by Garfield Reeves-Stevens,"What genre is Dark Matter, the book by Garfield Reeves-Stevens?",True,"What is the genre of Dark Matter, the book by Garfield Reeves-Stevens?"
3680895,Chaotic,genre,novella,1598983,91,279622,[],[],http://www.wikidata.org/entity/Q5072828,http://www.wikidata.org/entity/Q149537,Chaotic (novella),Novella,55,22512,What genre is Chaotic?,"[""novella""]",5,3,"['novella', 'science fiction television program', 'adventure television series', 'fantasy', 'pop music', 'fantasy television series']",book by Kelley Armstrong,"What genre is Chaotic, a book by Kelley Armstrong?",True,What genre does the book *Chaotic* by Kelley Armstrong belong to?
4115134,Flare,genre,J-pop,1802645,91,167395,[],"[""Japanese pop"",""jpop""]",http://www.wikidata.org/entity/Q5457465,http://www.wikidata.org/entity/Q131578,Flare (album),J-pop,76,31446,What genre is Flare?,"[""J-pop"", ""Japanese pop"", ""jpop""]",19,5,"['J-pop', 'Japanese pop', 'jpop', 'teen film', 'pop rock', 'science fiction novel']",album from Hitomi Shimatani,"What genre is Flare, an album by Hitomi Shimatani?",True,"What is the genre of Flare, an album by Hitomi Shimatani?"
3966961,The New World,genre,documentary film,1733194,91,2898005,"[""New World""]","[""documentary movie"",""doc"",""film documentary"",""motion picture documentary"",""documentary"",""factual film""]",http://www.wikidata.org/entity/Q5319149,http://www.wikidata.org/entity/Q93204,The New World (2011 film),Documentary film,89,42565,What genre is The New World?,"[""documentary film"", ""documentary movie"", ""doc"", ""film documentary"", ""motion picture documentary"", ""documentary"", ""factual film""]",23,8,"['documentary film', 'documentary movie', 'doc', 'film documentary', 'motion picture documentary', 'documentary', 'factual film', 'country music', 'drama film', 'film score', 'biographical film', 'romance film', 'fantasy', 'season premiere', 'soundtrack']",2011 film directed by Jaan Tootsen,"What genre is The New World, the 2011 film directed by Jaan Tootsen?",True,"What is the genre of The New World, the 2011 film directed by Jaan Tootsen?"
3955183,Drill,genre,industrial rock,1727393,91,2925770,[],[],http://www.wikidata.org/entity/Q5307536,http://www.wikidata.org/entity/Q968730,Drill (UK band),Industrial rock,47,11267,What genre is Drill?,"[""industrial rock""]",16,7,"['industrial rock', 'alternative rock', 'Russian hip hop', 'drill', 'industrial metal']",British band,"What genre is Drill, the British band?",True,"What is the genre of Drill, the British band?"
5724174,Settle,genre,indie rock,2554742,91,611592,[],"[""Independent rock"",""Indie Rock""]",http://www.wikidata.org/entity/Q7456907,http://www.wikidata.org/entity/Q183504,Settle (band),Indie rock,69,68145,What genre is Settle?,"[""indie rock"", ""Independent rock"", ""Indie Rock""]",8,2,"['indie rock', 'Independent rock', 'Indie Rock', 'house music']",American musical group,"What genre is Settle, the American musical group?",True,"What is the genre of Settle, the American musical group?"
2960647,Voyage,genre,synth-pop,1268094,91,159936,[],"[""synthesizer pop"",""techno-pop"",""synthpop""]",http://www.wikidata.org/entity/Q4052802,http://www.wikidata.org/entity/Q1298934,Voyage (The Sound of Arrows album),Synth-pop,94,67724,What genre is Voyage?,"[""synth-pop"", ""synthesizer pop"", ""techno-pop"", ""synthpop""]",27,16,"['synth-pop', 'synthesizer pop', 'techno-pop', 'synthpop', 'disco', 'video art', 'pop rock', 'thriller', 'hip hop music', 'rock and roll', 'jazz', 'folk rock', 'pop music', 'electro house', 'rap rock', 'trap music', 'science fiction novel', 'folk music', 'J-pop', 'rock music', 'LGBT-related film']",2011 debut studio album by The Sound of Arrows,"What genre is Voyage, the 2011 debut studio album by The Sound of Arrows?",True,"What genre is Voyage, the 2011 debut studio album by The Sound of Arrows?"
2319205,To Mother,genre,J-pop,1011681,91,167395,[],"[""Japanese pop"",""jpop""]",http://www.wikidata.org/entity/Q306981,http://www.wikidata.org/entity/Q131578,To Mother (song),J-pop,83,31446,What genre is To Mother?,"[""J-pop"", ""Japanese pop"", ""jpop""]",4,3,"['J-pop', 'Japanese pop', 'jpop', 'punk rock', 'alternative rock', 'rhythm and blues']",single,What genre is the single To Mother?,True,What is the genre of the single *To Mother*?
345859,Magic,genre,soap opera,139057,91,808453,[],"[""soap"",""soapie"",""soapopera""]",http://www.wikidata.org/entity/Q12496070,http://www.wikidata.org/entity/Q23739,Magic (TV series),Soap opera,99,50456,What genre is Magic?,"[""soap opera"", ""soap"", ""soapie"", ""soapopera""]",102,49,"['soap opera', 'soap', 'soapie', 'soapopera', 'pop music', 'J-pop', 'caricature', 'silent film', 'dance music', 'gangsta rap', 'pop rock', 'funk', 'romantic fiction', 'drama film', 'hard rock', 'heavy metal', 'heartland rock', 'horror fiction', 'hip hop music', 'soul', 'film based on literature', 'baroque pop', 'electronic music', 'disco', 'soft rock', 'jazz fusion', 'K-pop', 'hardcore hip hop', 'fantasy', 'rock music', 'drama television series', 'horror film', 'bossa nova', 'synth-pop', 'mystery film', 'alternative rock', 'rhythm and blues', 'Southern hip hop', 'cantopop', 'essay', 'dirty south', ""children's television series"", 'new wave', 'psychological horror', 'portrait', 'electropop', 'fantasy television series', 'contemporary R&B']",2013 Indonesian television series,"What genre is Magic, the 2013 Indonesian television series?",True,What genre is the 2013 Indonesian television series Magic?
5931235,The Harrowing,genre,horror literature,2660552,91,666582,[],[],http://www.wikidata.org/entity/Q7738944,http://www.wikidata.org/entity/Q193606,The Harrowing (novel),Horror literature,87,291,What genre is The Harrowing?,"[""horror literature""]",4,2,"['horror literature', 'performing arts podcast', 'arts podcast', 'horror fiction', 'fiction podcast', 'drama fiction podcast']",book by Alexandra Sokoloff,"What genre is The Harrowing, a book by Alexandra Sokoloff?",True,"What is the genre of The Harrowing, a book by Alexandra Sokoloff?"
4630284,Yellow,genre,J-pop,2040779,91,167395,[],"[""Japanese pop"",""jpop""]",http://www.wikidata.org/entity/Q6141829,http://www.wikidata.org/entity/Q131578,Yellow (Kaela Kimura song),J-pop,56,31446,What genre is Yellow?,"[""J-pop"", ""Japanese pop"", ""jpop""]",52,17,"['J-pop', 'Japanese pop', 'jpop', 'LGBT comic', 'pop rock', 'hip hop music', 'alternative rock', 'documentary film', 'drama film', 'yaoi', 'house music', 'K-pop', 'musical film', 'romance film', 'free improvisation', 'pop music', 'Japanese rock', 'science fiction film']",song by Kaela Kimura,"What genre is Yellow, the song by Kaela Kimura?",True,What is the genre of the song Yellow by Kaela Kimura?
4329994,Hara,genre,public art,1901946,91,1864184,[],[],http://www.wikidata.org/entity/Q5653496,http://www.wikidata.org/entity/Q557141,Hara (sculpture),Public art,87,5578,What genre is Hara?,"[""public art""]",42,2,"['public art', 'pop rock']",sculpture by Deborah Butterfield,"What genre is Hara, a sculpture by Deborah Butterfield?",True,"What is the genre of Hara, a sculpture by Deborah Butterfield?"
5917281,The Club,genre,reality television,2653478,91,606038,[],"[""reality TV"",""reality television program"",""reality TV program"",""reality television show"",""television reality program"",""television reality show"",""TV reality program"",""TV reality show""]",http://www.wikidata.org/entity/Q7723520,http://www.wikidata.org/entity/Q182415,The Club (2010 TV series),Reality television,64,40906,What genre is The Club?,"[""reality television"", ""reality TV"", ""reality television program"", ""reality TV program"", ""reality television show"", ""television reality program"", ""television reality show"", ""TV reality program"", ""TV reality show""]",34,13,"['reality television', 'reality TV', 'reality television program', 'reality TV program', 'reality television show', 'television reality program', 'television reality show', 'TV reality program', 'TV reality show', 'drama film', 'thrash metal', 'sport film', 'drama television series', 'third-person shooter', 'K-pop', 'massively multiplayer online role-playing game', 'comedy drama', 'soap opera']",2010 baseball reality TV show,"What genre is The Club, a 2010 baseball-related show?",True,"What genre is The Club, a 2010 show?"
1227346,Cut,genre,mandopop,545893,91,2928692,[],"[""Mandarin popular music"",""mandapop""]",http://www.wikidata.org/entity/Q17434126,http://www.wikidata.org/entity/Q973150,Cut (EP),Mandopop,57,7854,What genre is Cut?,"[""mandopop"", ""Mandarin popular music"", ""mandapop""]",35,13,"['mandopop', 'Mandarin popular music', 'mandapop', 'industrial music', 'mockumentary', 'hard rock', 'slasher film', 'comedy', 'experimental film', 'comedy film', 'classical music', 'rock music', 'yaoi', 'young adult literature', 'drama film', 'splatter film', 'punk rock', 'comedy horror', 'sitcom', 'office humor', 'Australian rock', 'American television sitcom', 'comedy drama', 'drama anime and manga']",2014 extended play by Aaron Yan,"What genre is Cut, the 2014 extended play by Aaron Yan?",True,What genre is the 2014 extended play by Aaron Yan?
4621279,Stories,genre,J-pop,2037184,91,167395,[],"[""Japanese pop"",""jpop""]",http://www.wikidata.org/entity/Q6133773,http://www.wikidata.org/entity/Q131578,Stories (Mayumi Iizuka album),J-pop,61,31446,What genre is Stories?,"[""J-pop"", ""Japanese pop"", ""jpop""]",26,15,"['J-pop', 'Japanese pop', 'jpop', 'disco', 'jazz fusion', 'pop music', 'electro house', 'post-hardcore', 'rock music', 'Christian rock', 'drama film', 'new jack swing', 'alternative rock', ""children's television series"", 'traditional folk music', 'progressive house', 'rhythm and blues']",album by Mayumi Iizuka,"What genre is Stories, an album by Mayumi Iizuka?",True,What genre is the album Stories by Mayumi Iizuka?
1178924,I Lost My Heart in Heidelberg,genre,musical film,524400,91,2842261,[],"[""musical movie""]",http://www.wikidata.org/entity/Q17101486,http://www.wikidata.org/entity/Q842256,I Lost My Heart in Heidelberg (1952 film),Musical film,98,22860,What genre is I Lost My Heart in Heidelberg?,"[""musical film"", ""musical movie""]",4,2,"['musical film', 'musical movie', 'romance film', 'silent film']",1952 film by Ernst Neubach,"What genre is I Lost My Heart in Heidelberg, the 1952 film by Ernst Neubach?",True,What genre is the 1952 film I Lost My Heart in Heidelberg by Ernst Neubach?
2084971,VS,genre,J-pop,913105,91,167395,[],"[""Japanese pop"",""jpop""]",http://www.wikidata.org/entity/Q2714279,http://www.wikidata.org/entity/Q131578,VS (song),J-pop,61,31446,What genre is VS?,"[""J-pop"", ""Japanese pop"", ""jpop""]",6,3,"['J-pop', 'Japanese pop', 'jpop', 'pop music', 'romance anime and manga']",single by Misono,"What genre is VS, the single by Misono?",True,What genre is the single VS by Misono?
5724676,Seven Veils,genre,ambient music,2555004,91,664254,[],"[""ambient"",""Ambient""]",http://www.wikidata.org/entity/Q7457496,http://www.wikidata.org/entity/Q193207,Seven Veils (Robert Rich album),Ambient music,65,26089,What genre is Seven Veils?,"[""ambient music"", ""ambient"", ""Ambient""]",2,2,"['ambient music', 'ambient', 'Ambient', 'drama film']",album by Robert Rich,"What genre is Seven Veils, an album by Robert Rich?",True,"What is the genre of Seven Veils, an album by Robert Rich?"
6275770,Bridge,genre,J-pop,2832709,91,167395,[],"[""Japanese pop"",""jpop""]",http://www.wikidata.org/entity/Q82626,http://www.wikidata.org/entity/Q131578,Bridge (Speed album),J-pop,66,31446,What genre is Bridge?,"[""J-pop"", ""Japanese pop"", ""jpop""]",146,22,"['J-pop', 'Japanese pop', 'jpop', 'romance film', 'cityscape', 'figurative art', 'landscape painting', 'science fiction', 'neo-acoustic', 'fiction', 'silent film', 'rock music', 'public art', 'pencil drawing', 'drama film', 'musical film', 'acoustic music', 'fantasy film']",album by Speed,"What genre is Bridge, the album by Speed?",True,"What is the genre of Bridge, the album by Speed?"
3682187,Chariot Race,genre,racing video game,1599692,91,2853660,[],"[""racing game"",""racing video games"",""racer""]",http://www.wikidata.org/entity/Q5074370,http://www.wikidata.org/entity/Q860750,Chariot Race,Racing game,91,14617,What genre is Chariot Race?,"[""racing video game"", ""racing game"", ""racing video games"", ""racer""]",2,2,"['racing video game', 'racing game', 'racing video games', 'racer', 'genre art']",1983 video game,"What genre is Chariot Race, the 1983 video game?",True,"What is the genre of Chariot Race, the 1983 video game?"
5492591,Progression,genre,trance,2444709,91,513739,[],"[""trance music"",""Trance""]",http://www.wikidata.org/entity/Q7248611,http://www.wikidata.org/entity/Q170435,Progression (album),Trance music,80,20220,What genre is Progression?,"[""trance"", ""trance music"", ""Trance""]",7,2,"['trance', 'trance music', 'Trance', 'J-pop']",album by Markus Schulz,"What genre is Progression, the album by Markus Schulz?",True,What genre is the album Progression by Markus Schulz?
5958809,The Take,genre,punk rock,2674783,91,1011970,[],"[""punk"",""punk music""]",http://www.wikidata.org/entity/Q7767922,http://www.wikidata.org/entity/Q3071,The Take (Welsh band),Punk rock,69,83910,What genre is The Take?,"[""punk rock"", ""punk"", ""punk music""]",11,6,"['punk rock', 'punk', 'punk music', 'crime film', 'documentary film', 'action film', 'drama television series', 'television series based on a novel', 'pop music', 'drama film']",UK musical group,"What genre is The Take, the UK musical group?",True,"What is the genre of The Take, the UK musical group?"
975023,Conversations,genre,contemporary Christian music,416205,91,212294,[],"[""inspirational music"",""CCM"",""Contemporary Christian music""]",http://www.wikidata.org/entity/Q16246667,http://www.wikidata.org/entity/Q1379958,Conversations (Sara Groves album),Contemporary Christian music,99,15249,What genre is Conversations?,"[""contemporary Christian music"", ""inspirational music"", ""CCM"", ""Contemporary Christian music""]",22,13,"['contemporary Christian music', 'inspirational music', 'CCM', 'Contemporary Christian music', 'society and culture podcast', 'indie pop', 'alternative rock', 'Christian adult contemporary', 'jazz', 'acoustic music', 'traditional folk music', 'public art', 'video art', 'Percent for Art', 'emo rap', 'progressive metal', 'reggae']",album by Sara Groves,"What genre is Conversations, the album by Sara Groves?",True,"What is the genre of Conversations, the album by Sara Groves?"
3024339,Mars,genre,science fiction film,1294701,91,1406377,[],"[""sci-fi film"",""science fiction movie"",""sci-fi movie"",""scifi film"",""scifi movie"",""sci fi film"",""sci fi movie"",""scifi-film"",""scifi""]",http://www.wikidata.org/entity/Q4282423,http://www.wikidata.org/entity/Q471839,Mars (1968 film),Science fiction film,78,41996,What genre is Mars?,"[""science fiction film"", ""sci-fi film"", ""science fiction movie"", ""sci-fi movie"", ""scifi film"", ""scifi movie"", ""sci fi film"", ""sci fi movie"", ""scifi-film"", ""scifi""]",139,28,"['science fiction film', 'sci-fi film', 'science fiction movie', 'sci-fi movie', 'scifi film', 'scifi movie', 'sci fi film', 'sci fi movie', 'scifi-film', 'scifi', 'J-pop', 'portrait', 'action film', 'experimental rock', 'pop rock', 'romance manga', 'punk rock', 'horrorcore', 'mythological painting', 'popular science film', 'science fiction comics', 'tragicomedy', 'rap', 'docudrama', 'school anime and manga', 'romance film', 'progressive rock', 'speculative fiction film', 'drama fiction', 'noise music', 'drama film', 'anime', 'alternative rock', 'panel discussion', 'electronica', 'comedy film', 'science fiction television program', 'biography']",1968 film by Pavel Klushantsev,"What genre is Mars, the 1968 film by Pavel Klushantsev?",True,"What is the genre of Mars, the 1968 film by Pavel Klushantsev?"
783144,Dimensions,genre,jazz,333989,91,2837065,[],"[""jazz music"",""jass"",""jas"",""jaz"",""Jazz""]",http://www.wikidata.org/entity/Q1572104,http://www.wikidata.org/entity/Q8341,Dimensions (Maynard Ferguson album),Jazz,97,100802,What genre is Dimensions?,"[""jazz"", ""jazz music"", ""jass"", ""jas"", ""jaz"", ""Jazz""]",23,9,"['jazz', 'jazz music', 'jass', 'jas', 'jaz', 'Jazz', 'educational film', 'thrash metal', 'J-pop', 'hard rock', 'science fiction film', 'power metal', 'animated film']",1955 studio album by Maynard Ferguson,"What genre is Dimensions, the 1955 studio album by Maynard Ferguson?",True,"What genre is Dimensions, the 1955 studio album by Maynard Ferguson?"
3398480,Astro,genre,reggae,1462035,91,2932736,[],"[""reggae music""]",http://www.wikidata.org/entity/Q4811478,http://www.wikidata.org/entity/Q9794,Astro (English musician),Reggae,46,47326,What genre is Astro?,"[""reggae"", ""reggae music""]",22,7,"['reggae', 'reggae music', 'hip hop music', 'K-pop', 'pop music', 'noise music', 'indie rock', 'lovers rock', 'indie pop', 'graffiti', 'reggae fusion']","British musician, rapper, toaster (1957–2021)","What genre is Astro, the British musician, rapper, and toaster (1957–2021)?",True,"What genre is associated with Astro, the British musician (1957–2021)?"
5908270,The Angel,genre,horror film,2648853,91,703275,"[""Angel""]","[""horror movie""]",http://www.wikidata.org/entity/Q7713741,http://www.wikidata.org/entity/Q200092,The Angel (2007 film),Horror film,75,90942,What genre is The Angel?,"[""horror film"", ""horror movie""]",39,9,"['horror film', 'horror movie', 'crime film', 'biographical film', 'drama film', 'fairy tale', 'LGBT-related film', 'public art', 'history book', 'black comedy film', 'silent film', 'art film', 'spy film', 'thriller film', 'rock music', 'biography', 'film based on a novel', ""children's literature"", 'poetry']",2007 short film directed by Paul Hough,"What genre is The Angel, the 2007 short film directed by Paul Hough?",True,"What genre is The Angel, the 2007 short film directed by Paul Hough?"
1136901,I Will Be There,genre,country music,500838,91,2837284,[],"[""country and western"",""country & western"",""country"",""Nashville sound""]",http://www.wikidata.org/entity/Q16994621,http://www.wikidata.org/entity/Q83440,I Will Be There (Dan Seals song),Country music,88,91093,What genre is I Will Be There?,"[""country music"", ""country and western"", ""country & western"", ""country"", ""Nashville sound""]",7,4,"['country music', 'country and western', 'country & western', 'country', 'Nashville sound', 'zombie video game', 'jazz', 'rhythm and blues', 'role-playing video game', 'first-person shooter']",1987 single by Dan Seals,"What genre is I Will Be There, the 1987 single by Dan Seals?",True,What genre is the 1987 single I Will Be There by Dan Seals?
1214913,Drama,genre,mandopop,540799,91,2928692,[],"[""Mandarin popular music"",""mandapop""]",http://www.wikidata.org/entity/Q17370574,http://www.wikidata.org/entity/Q973150,Drama (Aaron Yan EP),Mandopop,81,7854,What genre is Drama?,"[""mandopop"", ""Mandarin popular music"", ""mandapop""]",47,20,"['mandopop', 'Mandarin popular music', 'mandapop', 'progressive rock', 'pop rock', 'blues rock', 'pop music', 'comedy', 'Hi-NRG', 'hip hop music', 'trip hop', 'romantic comedy', 'comedy film', 'K-pop', 'dance-pop', 'Eurodance', 'trot', 'indie pop', 'música popular brasileira', 'German hip hop', 'drama film', 'new wave']",EP by Aaron Yan,"What genre is Drama, an EP by Aaron Yan?",True,What genre is the EP by Aaron Yan titled *Drama*?
1941957,Gone,genre,gothic metal,853785,91,574132,[],"[""goth metal""]",http://www.wikidata.org/entity/Q2523001,http://www.wikidata.org/entity/Q178145,Gone (Entwine album),Gothic metal,9,11369,What genre is Gone?,"[""gothic metal"", ""goth metal""]",77,36,"['gothic metal', 'goth metal', 'post-hardcore', 'indie pop', 'pop rock', 'mystery fiction', 'dystopian novel', 'instrumental rock', 'drama film', 'documentary film', 'alternative rock', ""children's and youth literature"", 'pop music', 'rhythm and blues', 'mystery film', 'hard rock', 'horror film', 'performing arts podcast', 'rape and revenge film', 'electronic dance music', 'country music', 'hip hop music', 'arts podcast', 'synth-pop', 'science fiction', 'funk', 'progressive pop', 'alternative metal', 'Eurodance', 'electropop', 'soul', 'crime novel', 'thriller film', 'drama television series']",2001 album by Entwine,"What genre is Gone, the 2001 album by Entwine?",True,What genre is the 2001 album Gone by Entwine?
3774995,Compass,genre,public art,1643112,91,1864184,[],[],http://www.wikidata.org/entity/Q5156065,http://www.wikidata.org/entity/Q557141,Compass (Simpson),Public art,79,5578,What genre is Compass?,"[""public art""]",36,14,"['public art', 'factual television program', 'country folk', 'rock music', 'J-pop', 'pop music', 'blues', 'soul', 'television documentary', 'classical music', 'pop rock', 'jazz', 'traditional folk music']",artwork by Gail Simpson,"What genre is Compass, an artwork by Gail Simpson?",True,What type of genre does the artwork Compass by Gail Simpson belong to?
3362656,Apollo,genre,rhythm and blues,1444976,91,1350191,[],"[""R&B"",""RnB"",""RNB"",""R and B"",""R & B""]",http://www.wikidata.org/entity/Q4780273,http://www.wikidata.org/entity/Q45981,Apollo (quintet),Rhythm and blues,13,60774,What genre is Apollo?,"[""rhythm and blues"", ""R&B"", ""RnB"", ""RNB"", ""R and B"", ""R & B""]",142,25,"['rhythm and blues', 'R&B', 'RnB', 'RNB', 'R and B', 'R & B', 'stoner rock', 'mythological painting', 'public art', 'nude', 'statue', 'punk rock', 'mythological sculpture', 'progressive house', 'rap', 'animated film', 'J-pop', 'neoclassical ballet', 'big room house']",American R&B/disco group,"What genre is Apollo, the American music group?",True,"What is the genre of Apollo, the American music group?"
5913758,The Box,genre,experimental music,2651679,91,428760,[],"[""Experimental""]",http://www.wikidata.org/entity/Q7719568,http://www.wikidata.org/entity/Q1640319,The Box (King Missile song),Experimental music,62,18371,What genre is The Box?,"[""experimental music"", ""Experimental""]",62,16,"['experimental music', 'Experimental', 'drama film', 'crime film', 'alternative rock', 'road movie', 'electronica', 'musical film', 'non-fiction', 'reality television', 'thriller film', 'science fiction film', 'psychological thriller', 'country music', 'rock and roll', 'soap opera', 'film based on literature', 'new wave']",King Missile song,"What genre is The Box, a song by King Missile?",True,What genre is the song The Box by King Missile?
4552087,In Deep,genre,short story,2007126,91,1513338,[],"[""tale"",""pripovijetka""]",http://www.wikidata.org/entity/Q6009297,http://www.wikidata.org/entity/Q49084,In Deep (book),Short story,71,60371,What genre is In Deep?,"[""science fiction"", ""SF"", ""scifi"", ""sci Fi"", ""sci-Fi"", ""science-fiction"", ""sci fi"", ""sciencefiction"", ""short story"", ""tale"", ""pripovijetka""]",9,3,"['science fiction', 'SF', 'scifi', 'sci Fi', 'sci-Fi', 'science-fiction', 'sci fi', 'sciencefiction', 'short story', 'tale', 'pripovijetka', 'progressive rock', 'pop rock', 'hard rock']",book by Damon Knight,"What genre is In Deep, a book by Damon Knight?",True,What genre is the book In Deep by Damon Knight?
4091852,Fantasy,genre,rock music,1790710,91,82098,[],"[""rock and roll"",""rock"",""Rock""]",http://www.wikidata.org/entity/Q5434260,http://www.wikidata.org/entity/Q11399,Fantasy (Alice Nine song),Rock music,58,140450,What genre is Fantasy?,"[""rock music"", ""rock and roll"", ""rock"", ""Rock""]",93,32,"['rock music', 'rock and roll', 'rock', 'Rock', 'Eurodance', 'alternative rock', 'pop music', 'electropop', 'schlager music', 'cantopop', 'Chinese rock', 'hip hop music', 'portrait', 'Celtic music', 'adventure video game', 'soft rock', 'dance music', 'electronica', 'electronic dance music', 'mandopop', 'dance-pop', 'jazz', 'traditional heavy metal', 'blue-eyed soul', 'music of the Philippines', 'pop rock', 'synth-pop', 'comedy', 'contemporary R&B', 'visual kei', 'rhythm and blues', 'hard rock', 'action game', 'drum and bass', 'action-adventure game', 'J-pop', 'book review', 'Afrobeats']",Single by Alice Nine,"What genre is Fantasy, the single by Alice Nine?",True,What genre is the single Fantasy by Alice Nine?
4783605,Just a Matter of Time,genre,novella,2103116,91,279622,[],[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q149537,Just a Matter of Time (novel),Novella,93,22512,What genre is Just a Matter of Time?,"[""novella""]",6,2,"['novella', 'jazz', 'country music']",novel by James Hadley Chase,"What genre is Just a Matter of Time, a work by James Hadley Chase?",True,"What genre is Just a Matter of Time, a work by James Hadley Chase?"
5558408,Reminiscences,genre,documentary film,2477158,91,2898005,[],"[""documentary movie"",""doc"",""film documentary"",""motion picture documentary"",""documentary"",""factual film""]",http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q93204,Reminiscences (2010 film),Documentary film,64,42565,What genre is Reminiscences?,"[""documentary film"", ""documentary movie"", ""doc"", ""film documentary"", ""motion picture documentary"", ""documentary"", ""factual film""]",25,2,"['documentary film', 'documentary movie', 'doc', 'film documentary', 'motion picture documentary', 'documentary', 'factual film', 'indie rock', 'experimental film', 'biographical film']",2010 Peruvian experimental documentary film,"What genre is Reminiscences, the 2010 Peruvian experimental film?",True,What genre is the 2010 Peruvian film Reminiscences?
5236034,My Way,genre,Cantopop,2320100,91,2849778,[],"[""Cantonese popular music"",""HK-pop"",""Hong Kong pop music""]",http://www.wikidata.org/entity/Q6946606,http://www.wikidata.org/entity/Q853873,My Way (Shirley Kwan album),Cantopop,53,9657,What genre is My Way?,"[""Cantopop"", ""Cantonese popular music"", ""HK-pop"", ""Hong Kong pop music""]",44,26,"['Cantopop', 'Cantonese popular music', 'HK-pop', 'Hong Kong pop music', 'bachata', 'traditional pop', 'rock and roll', 'popular music', 'drama film', 'dancehall', 'jazz', 'contemporary R&B', 'biographical film', 'country music', 'musical film', 'microhouse', 'soul', 'blues', 'reggae', 'Philadelphia soul', 'J-pop', 'alternative rock', 'pop music', 'cantopop', 'nu metal', 'tropical house']",ninth Cantonese studio album by Hong Kong solo artist Shirley Kwan,"What genre is My Way, the ninth Cantonese studio album by Hong Kong solo artist Shirley Kwan?",True,"What genre is My Way, the ninth Cantonese studio album by Hong Kong solo artist Shirley Kwan associated with?"
2161859,Our Time,genre,J-pop,945068,91,167395,[],"[""Japanese pop"",""jpop""]",http://www.wikidata.org/entity/Q283888,http://www.wikidata.org/entity/Q131578,Our Time (Dream song),J-pop,31,31446,What genre is Our Time?,"[""J-pop"", ""Japanese pop"", ""jpop""]",9,6,"['J-pop', 'Japanese pop', 'jpop', 'drama film', 'mandopop', 'synth-pop', 'traditional folk music']",single by Dream,"What genre is the single ""Our Time"" by Dream?",True,"What is the genre of the single ""Our Time"" by Dream?"
5450342,Piel,genre,telenovela,2423736,91,808588,[],"[""Spanish soap opera""]",http://www.wikidata.org/entity/Q7191634,http://www.wikidata.org/entity/Q23745,Piel (TV series),Telenovela,68,22256,What genre is Piel?,"[""telenovela"", ""Spanish soap opera""]",5,2,"['telenovela', 'Spanish soap opera', 'electronic music']",telenovela,What genre is Piel?,False,What type of creative work is Piel associated with?
672700,Thin Ice,genre,documentary film,284636,91,2898005,[],"[""documentary movie"",""doc"",""film documentary"",""motion picture documentary"",""documentary"",""factual film""]",http://www.wikidata.org/entity/Q15052355,http://www.wikidata.org/entity/Q93204,Thin Ice (2013 film),Documentary film,52,42565,What genre is Thin Ice?,"[""documentary film"", ""documentary movie"", ""doc"", ""film documentary"", ""motion picture documentary"", ""documentary"", ""factual film""]",27,6,"['documentary film', 'documentary movie', 'doc', 'film documentary', 'motion picture documentary', 'documentary', 'factual film', 'drama film', 'crime film', 'sitcom', 'musical film', 'romantic comedy', 'comedy film', 'puzzle video game', 'maze video game']",2013 film,"What genre is Thin Ice, the 2013 film?",True,"What is the genre of Thin Ice, the 2013 film?"
5745769,Shine,genre,rock music,2565092,91,82098,[],"[""rock and roll"",""rock"",""Rock""]",http://www.wikidata.org/entity/Q7497340,http://www.wikidata.org/entity/Q11399,Shine (Luna Sea song),Rock music,56,140450,What genre is Shine?,"[""rock music"", ""rock and roll"", ""rock"", ""Rock""]",121,77,"['rock music', 'rock and roll', 'rock', 'Rock', 'jazz', 'monologue', 'Afrobeats', 'J-pop', 'music of the Philippines', 'drama film', 'K-pop', 'musical film', 'pop music', 'synth-pop', 'pop rock', 'heavy metal', 'bubblegum dance', 'grunge', 'stage and screen', 'traditional heavy metal', 'biographical film', 'reggae', 'literary work', 'contemporary R&B', 'romance film', 'Latin pop', 'alternative rock', 'rhythm and blues', 'young adult novel', 'post-punk', 'cantopop', 'folk rock', 'alternative metal', 'soul', 'death metal', 'crossover', 'progressive rock', 'soft rock', 'Europop', 'Celtic music', 'smooth jazz', 'LGBT-related literature', 'visual kei', 'country music']",song by Luna Sea,"What genre is Shine, the song by Luna Sea?",True,"What genre is the song ""Shine"" by Luna Sea?"
6245759,Zones,genre,young adult literature,2819699,91,133436,[],"[""juvenile fiction"",""YA"",""youth literature"",""juvenile literature"",""young adult fiction"",""YA literature"",""YA fiction""]",http://www.wikidata.org/entity/Q8073948,http://www.wikidata.org/entity/Q1233720,Zones (novel),Young adult fiction,75,34659,What genre is Zones?,"[""young adult literature"", ""juvenile fiction"", ""YA"", ""youth literature"", ""juvenile literature"", ""young adult fiction"", ""YA literature"", ""YA fiction"", ""science fiction"", ""SF"", ""scifi"", ""sci Fi"", ""sci-Fi"", ""science-fiction"", ""sci fi"", ""sciencefiction""]",3,3,"['young adult literature', 'juvenile fiction', 'YA', 'youth literature', 'juvenile literature', 'young adult fiction', 'YA literature', 'YA fiction', 'science fiction', 'SF', 'scifi', 'sci Fi', 'sci-Fi', 'science-fiction', 'sci fi', 'sciencefiction', 'punk rock', 'space rock']",book by Damien Broderick,"What genre is Zones, the book by Damien Broderick?",True,"What is the genre of Zones, the book by Damien Broderick?"
5928360,The Gift,genre,country music,2659146,91,2837284,[],"[""country and western"",""country & western"",""country"",""Nashville sound""]",http://www.wikidata.org/entity/Q7736402,http://www.wikidata.org/entity/Q83440,The Gift (The McCarters song),Country music,50,91093,What genre is The Gift?,"[""country music"", ""country and western"", ""country & western"", ""country"", ""Nashville sound""]",111,32,"['country music', 'country and western', 'country & western', 'country', 'Nashville sound', 'documentary film', 'gangsta rap', 'non-fiction', 'drama film', 'alternative rock', 'crossover', 'horrorcore', 'rap', 'Christmas music', 'essay', 'hip hop music', 'drama television series', 'fantasy', 'post-grunge', 'television series based on a novel', 'new wave', 'horror film', 'operatic pop', 'romantic fiction', 'LGBT-related film', 'fantasy television series', 'synth-pop', 'adult contemporary music', 'mystery film', 'thriller film', 'exotica', 'pornographic film', 'country pop', 'portrait', 'easy listening', 'mystery television series', 'thriller', 'Philippine television drama', 'comedy film', 'pop music', 'Midwest hip hop', 'spoken word']",song by The McCarters,"What genre is The Gift, a song by The McCarters?",True,"What genre is the song ""The Gift"" by The McCarters?"
4200166,Gene,genre,thriller,1842006,91,601063,[],"[""suspense"",""thriller television program"",""thriller TV program"",""thriller television show"",""thriller TV show"",""suspense television program"",""suspense TV program"",""suspense television show"",""suspense TV show""]",http://www.wikidata.org/entity/Q5531066,http://www.wikidata.org/entity/Q182015,Gene (novel),Thriller (genre),98,144161,What genre is Gene?,"[""thriller"", ""suspense"", ""thriller television program"", ""thriller TV program"", ""thriller television show"", ""thriller TV show"", ""suspense television program"", ""suspense TV program"", ""suspense television show"", ""suspense TV show""]",14,2,"['thriller', 'suspense', 'thriller television program', 'thriller TV program', 'thriller television show', 'thriller TV show', 'suspense television program', 'suspense TV program', 'suspense television show', 'suspense TV show', 'indie rock']",novel by Stel Pavlou,"What genre is Gene, the novel by Stel Pavlou?",True,What genre is the novel Gene by Stel Pavlou?
4078079,Evil,genre,alternative rock,1783722,91,79782,[],"[""alternative music"",""alt-rock"",""alternative"",""alt rock"",""pop\/rock"",""Alternative Rock""]",http://www.wikidata.org/entity/Q5418390,http://www.wikidata.org/entity/Q11366,Evil (Grinderman song),Alternative rock,52,138822,What genre is Evil?,"[""alternative rock"", ""alternative music"", ""alt-rock"", ""alternative"", ""alt rock"", ""pop/rock"", ""Alternative Rock""]",22,14,"['alternative rock', 'alternative music', 'alt-rock', 'alternative', 'alt rock', 'pop/rock', 'Alternative Rock', 'thriller television series', 'thriller', 'rhythm and blues', 'action film', 'comedy horror', 'horror film', 'fantasy film', 'LGBT-related film', 'thrash metal', 'film based on literature', 'punk rock', 'prison film', 'panel discussion', 'drama film', 'Chicago blues', 'zombie film', 'autobiographical fiction', 'drama television series', 'heavy metal', 'electropop']",song by Grinderman,"What genre is Evil, the song by Grinderman?",True,"What is the genre of the song ""Evil"" by Grinderman?"
5266791,Neighbours,genre,synth-pop,2336601,91,159936,[],"[""synthesizer pop"",""techno-pop"",""synthpop""]",http://www.wikidata.org/entity/Q6988196,http://www.wikidata.org/entity/Q1298934,Neighbours (Camouflage song),Synth-pop,57,67724,What genre is Neighbours?,"[""synth-pop"", ""synthesizer pop"", ""techno-pop"", ""synthpop""]",24,15,"['synth-pop', 'synthesizer pop', 'techno-pop', 'synthpop', 'comedy film', 'educational television', 'silent film', 'racing video game', 'public art', 'science fiction film', 'rock music', 'drama film', 'horror film', 'documentary film', ""children's television series"", 'genre art', 'soap opera']",1988 Camouflage song,"What genre is Neighbours, the 1988 song by Camouflage?",True,What genre is the 1988 song Neighbours by Camouflage?
4552784,In Silence,genre,rock music,2007450,91,82098,[],"[""rock and roll"",""rock"",""Rock""]",http://www.wikidata.org/entity/Q6010827,http://www.wikidata.org/entity/Q11399,In Silence (song),Rock music,92,140450,What genre is In Silence?,"[""rock music"", ""rock and roll"", ""rock"", ""Rock""]",6,3,"['rock music', 'rock and roll', 'rock', 'Rock', 'action game', 'survival horror', 'biographical film', 'drama film']",1996 single by Luna Sea,"What genre is In Silence, the 1996 single by Luna Sea?",True,What genre is the 1996 single In Silence by Luna Sea?
3425747,Back to Back,genre,country music,1476423,91,2837284,[],"[""country and western"",""country & western"",""country"",""Nashville sound""]",http://www.wikidata.org/entity/Q4839379,http://www.wikidata.org/entity/Q83440,Back to Back (Jeanne Pruett song),Country music,58,91093,What genre is Back to Back?,"[""country music"", ""country and western"", ""country & western"", ""country"", ""Nashville sound""]",17,9,"['country music', 'country and western', 'country & western', 'country', 'Nashville sound', 'pop rock', 'hip hop music', 'Southern soul', 'drama film', 'action film', 'jazz fusion', 'rock music', 'hard rock']",single by Jeanne Pruett,"What genre is Back to Back, a single by Jeanne Pruett?",True,"What is the genre of Back to Back, a single by Jeanne Pruett?"
5839630,Strength,genre,hardcore punk,2614186,91,56865,[],"[""hXc"",""Punk\/HC"",""hardcore"",""Hardcore Punk""]",http://www.wikidata.org/entity/Q7623222,http://www.wikidata.org/entity/Q10922,Strength (Japanese band),Hardcore punk,73,34061,What genre is Strength?,"[""hardcore punk"", ""hXc"", ""Punk/HC"", ""hardcore"", ""Hardcore Punk""]",17,6,"['hardcore punk', 'hXc', 'Punk/HC', 'hardcore', 'Hardcore Punk', 'glam metal', 'J-pop', 'rock music', 'silent film', 'hard rock']",Japanese punk band,"What genre is Strength, the Japanese punk band?",True,What genre of music does the band Strength from Japan perform?
1185931,Let It Go,genre,indie pop,528218,91,2842318,[],"[""indie pop music"",""indie-pop"",""indiepop""]",http://www.wikidata.org/entity/Q17135078,http://www.wikidata.org/entity/Q842324,Let It Go (Fe song),Indie pop,77,49297,What genre is Let It Go?,"[""indie pop"", ""indie pop music"", ""indie-pop"", ""indiepop""]",52,34,"['indie pop', 'indie pop music', 'indie-pop', 'indiepop', 'contemporary R&B', 'blues rock', 'K-pop', 'stage and screen', 'J-pop', 'dance-pop', 'country music', 'synth-pop', 'jazz', 'hard rock', 'pop music', 'rock music', 'electropop', 'heavy metal', 'rhythm and blues', 'house music', 'progressive rock', 'soul', 'soft rock', 'pop rap', 'pop rock', 'UK rap', 'hip hop music', 'Eurodance', 'show tune', 'indie rock']",song by Fe,"What genre is Let It Go, the song by Fe?",True,What genre is the song Let It Go by Fe?
3489407,Betrayal,genre,crime film,1507416,91,2919224,[],"[""crime movie""]",http://www.wikidata.org/entity/Q4898193,http://www.wikidata.org/entity/Q959790,Betrayal (1932 film),Crime film,72,28275,What genre is Betrayal?,"[""crime film"", ""crime movie""]",58,21,"['crime film', 'crime movie', 'documentary film', 'drama television series', 'hardcore punk', 'silent film', 'drama film', 'fantasy', 'romantic fiction', 'strategy video game', 'spoken drama', 'political history', 'action film', 'thrash metal', 'Star Trek novel', 'science fiction']",1932 British crime film directed by Reginald Fogwell,"What genre is Betrayal, the 1932 British film directed by Reginald Fogwell?",True,"What genre is the 1932 British film directed by Reginald Fogwell, titled Betrayal?"
5705598,Scorpio,genre,rock music,2545623,91,82098,[],"[""rock and roll"",""rock"",""Rock""]",http://www.wikidata.org/entity/Q7435378,http://www.wikidata.org/entity/Q11399,Scorpio (Trax song),Rock music,95,140450,What genre is Scorpio?,"[""rock music"", ""rock and roll"", ""rock"", ""Rock""]",33,5,"['rock music', 'rock and roll', 'rock', 'Rock', 'spy film', 'thriller film', 'action film', 'pop music', 'rhythm and blues', 'drama film', 'instrumental music', 'electro']",2004 single by TRAX,"What genre is Scorpio, the 2004 single by TRAX?",True,What genre is the 2004 single Scorpio by TRAX?
1195400,Right There,genre,romantic comedy,532872,91,2853587,[],"[""romedy"",""romcom""]",http://www.wikidata.org/entity/Q17183629,http://www.wikidata.org/entity/Q860626,Right There (film),Romantic comedy,99,64579,What genre is Right There?,"[""romantic comedy"", ""romedy"", ""romcom""]",4,3,"['romantic comedy', 'romedy', 'romcom', 'pop music']",2013 short film directed by Nathan Suher,"What genre is Right There, the 2013 short film directed by Nathan Suher?",True,"What is the genre of Right There, the 2013 short film directed by Nathan Suher?"
4109853,Fire,genre,horror literature,1799623,91,666582,[],[],http://www.wikidata.org/entity/Q5451304,http://www.wikidata.org/entity/Q193606,Fire (Rodgers novel),Horror literature,62,291,What genre is Fire?,"[""horror literature""]",154,51,"['horror literature', 'hip hop music', 'Italo disco', 'pop music', 'Afrobeats', 'Afro', 'rock music', 'pop rock', 'Korean hip hop', 'allegory', 'rhythm and blues', 'Afrobeat', 'psychedelic rock', 'Eurodance', 'melodrama', 'country pop', 'Computer art scene', 'adventure film', 'drama film', 'East Coast hip hop', 'hard rock', 'romance film', 'K-pop', 'disaster film', 'science fiction', 'gospel music', 'mythological painting', 'experimental music', 'abstract art', 'rap', 'drama television series', 'jazz', 'soul', 'fantasy', 'gothic metal', 'alternative rock', 'Christian rock', 'erotic film', 'LGBT-related film', 'jazz fusion', 'indie rock', 'urban fantasy', 'funk', 'southern rock', 'trance', 'apocalyptic fiction', 'horror fiction', 'public art']",1990 novel by Alan Rodgers,"What genre is Fire, the 1990 novel by Alan Rodgers?",True,"What is the genre of Fire, the 1990 novel by Alan Rodgers?"
804961,The Moment,genre,mandopop,343599,91,2928692,[],"[""Mandarin popular music"",""mandapop""]",http://www.wikidata.org/entity/Q15903033,http://www.wikidata.org/entity/Q973150,The Moment (Aaron Yan album),Mandopop,97,7854,What genre is The Moment?,"[""mandopop"", ""Mandarin popular music"", ""mandapop""]",27,17,"['mandopop', 'Mandarin popular music', 'mandapop', 'documentary film', 'drama film', 'smooth jazz', 'post-hardcore', 'jazz', 'mod revival', 'cantopop', 'pop rock', 'reality television', 'Christian rock', 'traditional folk music', 'mystery film']",2012 LP from Aaron Yan,"What genre is The Moment, the 2012 LP from Aaron Yan?",True,"What is the genre of The Moment, the 2012 LP from Aaron Yan?"
5838429,Strangers,genre,science fiction,2613486,91,844831,[],"[""SF"",""scifi"",""sci Fi"",""sci-Fi"",""science-fiction"",""sci fi"",""sciencefiction""]",http://www.wikidata.org/entity/Q7621583,http://www.wikidata.org/entity/Q24925,Strangers (Dozois novel),Science fiction,32,155784,What genre is Strangers?,"[""science fiction"", ""SF"", ""scifi"", ""sci Fi"", ""sci-Fi"", ""science-fiction"", ""sci fi"", ""sciencefiction""]",54,21,"['science fiction', 'SF', 'scifi', 'sci Fi', 'sci-Fi', 'science-fiction', 'sci fi', 'sciencefiction', 'country music', 'LGBT-related television series', 'society and culture podcast', 'independent film', 'drama film', 'crime film', 'synth-pop', 'thriller television series', 'pop music', 'adventure film', 'science fiction novel', 'horror fiction', 'British rock', 'alternative rock', 'hip hop music', 'rock music']",Gardner Dozois novel,"What genre is Strangers, the novel by Gardner Dozois?",True,What genre is the novel *Strangers* by Gardner Dozois?
5966775,Theatre,genre,mathcore,2678865,91,2049959,[],"[""math metal"",""noisecore""]",http://www.wikidata.org/entity/Q7777337,http://www.wikidata.org/entity/Q616679,Theatre (band),Mathcore,92,11277,What genre is Theatre?,"[""mathcore"", ""math metal"", ""noisecore""]",19,6,"['mathcore', 'math metal', 'noisecore', 'drama film', 'romance film', 'theatre magazine', 'silent film', 'jazz']",South African mathcore band,"What genre is Theatre, the South African band?",True,"What is the genre of Theatre, the South African band?"
3426056,Background,genre,documentary film,1476569,91,2898005,[],"[""documentary movie"",""doc"",""film documentary"",""motion picture documentary"",""documentary"",""factual film""]",http://www.wikidata.org/entity/Q4839636,http://www.wikidata.org/entity/Q93204,Background (1973 film),Documentary film,93,42565,What genre is Background?,"[""documentary film"", ""documentary movie"", ""doc"", ""film documentary"", ""motion picture documentary"", ""documentary"", ""factual film""]",25,6,"['documentary film', 'documentary movie', 'doc', 'film documentary', 'motion picture documentary', 'documentary', 'factual film', 'Christian hip hop', 'hardcore punk', 'drama film', 'video game magazine', 'hip hop music']",1973 film by Carmen D'Avino,"What genre is Background, the 1973 film by Carmen D'Avino?",True,What genre is the 1973 film Background by Carmen D'Avino?
321798,Node,genre,death metal,129833,91,1473195,[],"[""Death Metal""]",http://www.wikidata.org/entity/Q1225562,http://www.wikidata.org/entity/Q483251,Node (band),Death metal,95,35455,What genre is Node?,"[""death metal"", ""Death Metal""]",9,4,"['death metal', 'Death Metal', 'metalcore', 'electronic music', 'Berlin School of electronic music', 'hip hop music']",Italian musical group,"What genre is Node, the Italian musical group?",True,"What is the genre of Node, the Italian musical group?"
4552086,In Deep,genre,science fiction,2007126,91,844831,[],"[""SF"",""scifi"",""sci Fi"",""sci-Fi"",""science-fiction"",""sci fi"",""sciencefiction""]",http://www.wikidata.org/entity/Q6009297,http://www.wikidata.org/entity/Q24925,In Deep (book),Science fiction,71,155784,What genre is In Deep?,"[""science fiction"", ""SF"", ""scifi"", ""sci Fi"", ""sci-Fi"", ""science-fiction"", ""sci fi"", ""sciencefiction"", ""short story"", ""tale"", ""pripovijetka""]",9,3,"['science fiction', 'SF', 'scifi', 'sci Fi', 'sci-Fi', 'science-fiction', 'sci fi', 'sciencefiction', 'short story', 'tale', 'pripovijetka', 'progressive rock', 'pop rock', 'hard rock']",book by Damon Knight,"What genre is In Deep, the book by Damon Knight?",True,What genre is the book *In Deep* by Damon Knight?
4543053,If I Ever,genre,synth-pop,2003033,91,159936,[],"[""synthesizer pop"",""techno-pop"",""synthpop""]",http://www.wikidata.org/entity/Q5990428,http://www.wikidata.org/entity/Q1298934,If I Ever (song),Synth-pop,63,67724,What genre is If I Ever?,"[""synth-pop"", ""synthesizer pop"", ""techno-pop"", ""synthpop""]",3,2,"['synth-pop', 'synthesizer pop', 'techno-pop', 'synthpop', 'folk rock']",Red Flag song,What genre is the song If I Ever by Red Flag?,True,What genre is the song *If I Ever* by Red Flag?
5210885,More Love,genre,pop music,2307198,91,1193964,[],"[""pop"",""Pop""]",http://www.wikidata.org/entity/Q6910992,http://www.wikidata.org/entity/Q37073,More Love (Feargal Sharkey song),Pop music,75,286065,What genre is More Love?,"[""pop music"", ""pop"", ""Pop""]",7,5,"['pop music', 'pop', 'Pop', 'hip hop music', 'country music', 'soul']",1988 single by Feargal Sharkey,"What genre is More Love, the 1988 single by Feargal Sharkey?",True,"What is the genre of More Love, the 1988 single by Feargal Sharkey?"
6157089,West,genre,rock music,2775542,91,82098,[],"[""rock and roll"",""rock"",""Rock""]",http://www.wikidata.org/entity/Q7984207,http://www.wikidata.org/entity/Q11399,West (song),Rock music,49,140450,What genre is West?,"[""rock music"", ""rock and roll"", ""rock"", ""Rock""]",44,7,"['rock music', 'rock and roll', 'rock', 'Rock', 'film based on literature', 'drama film', 'Americana', 'television documentary', 'documentary film', 'coming-of-age fiction']",song,What genre is the song West?,True,What is the genre of the song West?
2317033,The Other Man,genre,comedy film,1010814,91,336148,"[""Other Man""]","[""comedy movie""]",http://www.wikidata.org/entity/Q3067291,http://www.wikidata.org/entity/Q157443,The Other Man (1916 film),Comedy film,90,36816,What genre is The Other Man?,"[""comedy film"", ""comedy movie""]",12,6,"['comedy film', 'comedy movie', 'drama film', 'romance film', 'mystery film', 'alternative rock', 'silent film']",1916 film,"What genre is The Other Man, a 1916 film?",True,"What genre is The Other Man, a 1916 production?"
129320,Wake Up,genre,J-pop,51590,91,167395,[],"[""Japanese pop"",""jpop""]",http://www.wikidata.org/entity/Q10853267,http://www.wikidata.org/entity/Q131578,Wake Up (ClariS song),J-pop,91,31446,What genre is Wake Up?,"[""J-pop"", ""Japanese pop"", ""jpop""]",51,25,"['J-pop', 'Japanese pop', 'jpop', 'rock music', 'pop music', 'protest song', 'indie rock', 'Christian rock', 'deathcore', 'pop rock', 'instrumental rock', 'mandopop', 'indie pop', 'new jack swing', 'UK rap', 'contemporary R&B', 'experimental film', 'dubstep', 'rhythm and blues', 'hard rock', 'disco', 'country music']",2012 single by ClariS,"What genre is Wake Up, the 2012 single by ClariS?",True,What genre is the 2012 single Wake Up by ClariS?
282946,The Copper,genre,crime film,114401,91,2919224,"[""Copper""]","[""crime movie""]",http://www.wikidata.org/entity/Q1194004,http://www.wikidata.org/entity/Q959790,The Copper (1958 film),Crime film,94,28275,What genre is The Copper?,"[""crime film"", ""crime movie""]",2,2,"['crime film', 'crime movie']",1958 film by Eugen York,"What genre is The Copper, the 1958 film directed by Eugen York?",True,"What genre is The Copper, the 1958 film directed by Eugen York?"
4357528,Heaven,genre,trance,1914437,91,513739,[],"[""trance music"",""Trance""]",http://www.wikidata.org/entity/Q5694574,http://www.wikidata.org/entity/Q170435,Heaven (Cosmic Baby album),Trance music,96,20220,What genre is Heaven?,"[""trance"", ""trance music"", ""Trance"", ""techno"", ""techno music""]",104,57,"['trance', 'trance music', 'Trance', 'techno', 'techno music', 'hymn text', 'crime film', 'heavy metal', 'pop music', 'rock music', 'hip hop music', 'drama film', 'J-pop', 'soul', 'panel discussion', 'contemporary R&B', 'romance film', 'sophisti-pop', 'house music', 'progressive house', 'jazz rock', 'disco', 'science fiction', 'gospel music', 'thriller film', 'Italo dance', 'Afrobeats', 'K-pop', 'alternative rock', 'jazz', 'new wave', 'synth-pop', 'glam metal', 'documentary film', 'comedy drama', 'future house', 'drum and bass', 'Gothic fiction', 'indie rock', 'dance-pop', 'noise rock', 'urban contemporary gospel']",1999 album by Cosmic Baby,"What genre is Heaven, the 1999 album by Cosmic Baby?",True,What genre is the 1999 album by Cosmic Baby?
983453,Heist,genre,action game,420527,91,911137,[],"[""action video game""]",http://www.wikidata.org/entity/Q16259630,http://www.wikidata.org/entity/Q270948,Heist (2000 video game),Action game,10,28982,What genre is Heist?,"[""action game"", ""action video game""]",18,5,"['action game', 'action video game', 'heist film']",2000 video game,"What genre is Heist, the 2000 video game?",True,What genre is the 2000 video game Heist?
3528437,Blue Peter,father,War Admiral,1524877,257,228968,[],[],http://www.wikidata.org/entity/Q4929614,http://www.wikidata.org/entity/Q1417640,Blue Peter (American horse),War Admiral,96,4161,Who is the father of Blue Peter?,"[""War Admiral""]",8,2,"['War Admiral', 'Fairway']",American-bred Thoroughbred racehorse,"Who is the father of Blue Peter, the American-bred Thoroughbred racehorse?",True,"Who is the father of Blue Peter, the American-bred Thoroughbred?"
208738,Ma Xiu,father,Ma Teng,84295,257,1875937,[],[],http://www.wikidata.org/entity/Q1143130,http://www.wikidata.org/entity/Q559722,Ma Xiu,Ma Teng,30,1364,Who is the father of Ma Xiu?,"[""Ma Teng""]",8,4,"['Ma Teng', 'Ma Rong', 'Ma Cheng']",Chinese warlord,"Who is the father of Ma Xiu, the Chinese warlord?",True,"Who is the father of Ma Xiu, the Chinese?"
2344983,George,father,David VII of Georgia,1022247,257,2867920,[],[],http://www.wikidata.org/entity/Q3102235,http://www.wikidata.org/entity/Q887093,George (son of David VII of Georgia),David VII of Georgia,68,1337,Who is the father of George?,"[""David VII of Georgia""]",84,3,"['David VII of Georgia', 'Andrew I of Hungary', 'Constantine I of Georgia']",son of David VII of Georgia (1250-1268),"Who is the father of George, the son of David VII of Georgia (1250–1268)?",True,"""Who is the father of George, the son of David VII of Georgia?"""
2344890,George,father,Constantine I of Georgia,1022219,257,1299670,"[""George, son of Constantine I of Georgia""]","[""Constantine I Bagrationi, King of Georgia""]",http://www.wikidata.org/entity/Q3102147,http://www.wikidata.org/entity/Q431850,George (son of Constantine I of Georgia),Constantine I of Georgia,58,619,Who is the father of George?,"[""Constantine I of Georgia"", ""Constantine I Bagrationi, King of Georgia""]",84,3,"['Constantine I of Georgia', 'Constantine I Bagrationi, King of Georgia', 'Andrew I of Hungary', 'David VII of Georgia']",co-king of Georgia (died between 1435 and 1446),"Who is the father of George, the co-king of Georgia who died between 1435 and 1446?",True,"Who is the father of George, the co-king of Georgia who lived during the 15th century?"
468395,Louis,father,"Rorgon I, Count of Maine",191004,257,1743664,[],[],http://www.wikidata.org/entity/Q1351496,http://www.wikidata.org/entity/Q533927,Louis (abbot of Saint-Denis),"Rorgon I, Count of Maine",72,519,Who is the father of Louis?,"[""Rorgon I, Count of Maine""]",32,3,"['Rorgon I, Count of Maine', 'John I, Count of La Marche', 'Louis III the Younger']",abbot of Saint-Denis (9th c.),"Who is the father of Louis, the abbot of Saint-Denis (9th century)?",True,"Who is the father of Louis, the abbot of Saint-Denis in the 9th century?"
5998755,Tina,country,United States of America,2693695,182,988513,"[""Tina, West Virginia""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q7807854,http://www.wikidata.org/entity/Q30,"Tina, West Virginia",United States,50,1629691,In what country is Tina?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",58,17,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'Italy', 'Russia', 'Sweden', 'Romania', 'Indonesia', 'Iran', 'United Arab Emirates', 'United Kingdom', 'Sudan', 'Poland']","Ghost town in Summers County, West Virginia","In what country is Tina, a ghost town in Summers County, West Virginia?",True,"In what country is Tina, a ghost town in Summers County?"
3890850,Dell,country,United States of America,1697901,182,988513,"[""Dell, Missouri""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q5254099,http://www.wikidata.org/entity/Q30,"Dell, Missouri",United States,38,1629691,In what country is Dell?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",11,5,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'Canada']","township in Benton County, Missouri","In what country is Dell, a township in Benton County, Missouri?",True,"In what country is Dell, a township in Benton County?"
3240290,Ago,country,Japan,1390174,182,502871,[],"[""State of Japan"",""Land of the Rising Sun"",""Nihon"",""Nippon"",""JP"",""Nippon-koku"",""Nihon-koku"",""JA"",""JPN"",""jp"",""JAP"",""Ja"",""Jap""]",http://www.wikidata.org/entity/Q4693315,http://www.wikidata.org/entity/Q17,"Ago, Mie",Japan,61,702414,In what country is Ago?,"[""Japan"", ""State of Japan"", ""Land of the Rising Sun"", ""Nihon"", ""Nippon"", ""JP"", ""Nippon-koku"", ""Nihon-koku"", ""JA"", ""JPN"", ""jp"", ""JAP"", ""Ja"", ""Jap""]",6,3,"['Japan', 'State of Japan', 'Land of the Rising Sun', 'Nihon', 'Nippon', 'JP', 'Nippon-koku', 'Nihon-koku', 'JA', 'JPN', 'jp', 'JAP', 'Ja', 'Jap', 'Estonia', 'Nigeria']","dissolved municipality in Shima district, Mie prefecture, Japan","In what country is Ago, a dissolved municipality in Shima district, Mie prefecture?",True,"In what country is Ago, a dissolved municipality in Shima district?"
4166304,Freedom,country,United States of America,1825665,182,988513,"[""Freedom, Utah""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q5500430,http://www.wikidata.org/entity/Q30,"Freedom, Utah",United States,96,1629691,In what country is Freedom?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",149,29,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'United Kingdom', 'Italy', 'Netherlands', 'Germany', 'Canada', 'Armenia']","unincorporated community in Freedom, Utah United States","In what country is Freedom, an unincorporated community in Utah, located?",True,"In what country is Freedom, an unincorporated community in Utah?"
3525079,Blenheim,country,United States of America,1523114,182,988513,"[""Blenheim, New Jersey""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q4926033,http://www.wikidata.org/entity/Q30,"Blenheim, New Jersey",United States,88,1629691,In what country is Blenheim?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",34,23,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'United Kingdom', 'New Zealand', 'Canada', 'Jamaica', 'Australia']","human settlement in Gloucester Township, New Jersey, United States of America","In what country is Blenheim, a human settlement in Gloucester Township, New Jersey?",True,"""In what country is Blenheim, a human settlement in Gloucester Township?"""
4760063,Mary,country,France,2092984,182,230035,[],"[""fr"",""FR"",""R\u00e9publique fran\u00e7aise"",""La France"",""Republic of France"",""French Republic"",""FRA"",""the Hexagon""]",http://www.wikidata.org/entity/Q628627,http://www.wikidata.org/entity/Q142,"Mary, Saône-et-Loire",France,37,486947,In what country is Mary?,"[""France"", ""fr"", ""FR"", ""République française"", ""La France"", ""Republic of France"", ""French Republic"", ""FRA"", ""the Hexagon""]",368,68,"['France', 'fr', 'FR', 'République française', 'La France', 'Republic of France', 'French Republic', 'FRA', 'the Hexagon', 'United States of America', 'Cuba', 'Kingdom of Great Britain', 'South Africa', 'United Kingdom', 'Turkmenistan', 'Australia', 'Hong Kong']","commune in Saône-et-Loire, France","In what country is Mary, a commune in Saône-et-Loire?",True,"In what country is Mary, a commune located in Saône-et-Loire?"
232266,Tsutsui Station,country,Japan,93973,182,502871,[],"[""State of Japan"",""Land of the Rising Sun"",""Nihon"",""Nippon"",""JP"",""Nippon-koku"",""Nihon-koku"",""JA"",""JPN"",""jp"",""JAP"",""Ja"",""Jap""]",http://www.wikidata.org/entity/Q11603024,http://www.wikidata.org/entity/Q17,Tsutsui Station (Aomori),Japan,64,702414,In what country is Tsutsui Station?,"[""Japan"", ""State of Japan"", ""Land of the Rising Sun"", ""Nihon"", ""Nippon"", ""JP"", ""Nippon-koku"", ""Nihon-koku"", ""JA"", ""JPN"", ""jp"", ""JAP"", ""Ja"", ""Jap""]",3,2,"['Japan', 'State of Japan', 'Land of the Rising Sun', 'Nihon', 'Nippon', 'JP', 'Nippon-koku', 'Nihon-koku', 'JA', 'JPN', 'jp', 'JAP', 'Ja', 'Jap']","railway station in Aomori, Aomori Prefecture, Japan","In what country is Tsutsui Station, a railway station located in Aomori, Aomori Prefecture?",True,"In what country is Tsutsui Station, a railway station located in Aomori Prefecture?"
3990933,Edmundston,country,Canada,1744170,182,355510,[],"[""Dominion of Canada"",""British North America"",""CAN"",""CA"",""ca"",""can"",""Can.""]",http://www.wikidata.org/entity/Q5339992,http://www.wikidata.org/entity/Q16,Edmundston (electoral district),Canada,69,816653,In what country is Edmundston?,"[""Canada"", ""Dominion of Canada"", ""British North America"", ""CAN"", ""CA"", ""ca"", ""can"", ""Can.""]",6,6,"['Canada', 'Dominion of Canada', 'British North America', 'CAN', 'CA', 'ca', 'can', 'Can.']","provincial electoral district in New Brunswick, Canada","In what country is Edmundston, a provincial electoral district in New Brunswick?",True,"In what country is Edmundston, a provincial electoral district located?"
5525700,Rahzan,country,Iran,2462258,182,2753763,[],"[""Islamic Republic of Iran"",""Persia"",""ir"",""Islamic Rep. Iran"",""\ud83c\uddee\ud83c\uddf7""]",http://www.wikidata.org/entity/Q7283648,http://www.wikidata.org/entity/Q794,Rahzan,Iran,72,317291,In what country is Rahzan?,"[""Iran"", ""Islamic Republic of Iran"", ""Persia"", ""ir"", ""Islamic Rep. Iran"", ""🇮🇷""]",3,2,"['Iran', 'Islamic Republic of Iran', 'Persia', 'ir', 'Islamic Rep. Iran', '🇮🇷', 'Pakistan']",village in Iran,In what country is the village of Rahzan located?,True,In what country is the village of Rahzan found?
1273302,Valdearcos de la Vega,country,Spain,567174,182,962574,[],"[""Espa\u00f1a"",""Kingdom of Spain"",""ES"",""ESP""]",http://www.wikidata.org/entity/Q1767866,http://www.wikidata.org/entity/Q29,Valdearcos de la Vega,Spain,39,377325,In what country is Valdearcos de la Vega?,"[""Spain"", ""España"", ""Kingdom of Spain"", ""ES"", ""ESP""]",2,2,"['Spain', 'España', 'Kingdom of Spain', 'ES', 'ESP']",municipality of Spain,In what country is Valdearcos de la Vega located?,True,In what country is Valdearcos de la Vega found?
4196474,Gaustadalléen,country,Norway,1840166,182,702887,"[""Gaustadalleen""]","[""Kingdom of Norway"",""Norge"",""Norv\u00e8ge\u200f"",""NO"",""NOR"",""no"",""Noreg"",""Norwegen\u200f"",""\ud83c\uddf3\ud83c\uddf4""]",http://www.wikidata.org/entity/Q5527860,http://www.wikidata.org/entity/Q20,Gaustadalléen tram stop,Norway,43,293715,In what country is Gaustadalléen?,"[""Norway"", ""Kingdom of Norway"", ""Norge"", ""Norvège‏"", ""NO"", ""NOR"", ""no"", ""Noreg"", ""Norwegen‏"", ""🇳🇴""]",2,2,"['Norway', 'Kingdom of Norway', 'Norge', 'Norvège\u200f', 'NO', 'NOR', 'no', 'Noreg', 'Norwegen\u200f', '🇳🇴']",tram stop,In what country is Gaustadalléen located?,True,In what country is Gaustadalléen found?
1972640,Wiesau,country,Germany,866470,182,607728,[],"[""FRG"",""BRD"",""Bundesrepublik Deutschland"",""Federal Republic of Germany"",""de"",""Deutschland"",""GER"",""BR Deutschland"",""DE""]",http://www.wikidata.org/entity/Q2569290,http://www.wikidata.org/entity/Q183,Wiesau (river),Germany,37,556493,In what country is Wiesau?,"[""Germany"", ""FRG"", ""BRD"", ""Bundesrepublik Deutschland"", ""Federal Republic of Germany"", ""de"", ""Deutschland"", ""GER"", ""BR Deutschland"", ""DE""]",3,2,"['Germany', 'FRG', 'BRD', 'Bundesrepublik Deutschland', 'Federal Republic of Germany', 'de', 'Deutschland', 'GER', 'BR Deutschland', 'DE']",river in Germany,"In what country is Wiesau, the location of a river?",True,"""In what country is Wiesau located?"""
4013695,Kamioka Station,country,Japan,1753994,182,502871,[],"[""State of Japan"",""Land of the Rising Sun"",""Nihon"",""Nippon"",""JP"",""Nippon-koku"",""Nihon-koku"",""JA"",""JPN"",""jp"",""JAP"",""Ja"",""Jap""]",http://www.wikidata.org/entity/Q5357786,http://www.wikidata.org/entity/Q17,Kamioka Station,Japan,53,702414,In what country is Kamioka Station?,"[""Japan"", ""State of Japan"", ""Land of the Rising Sun"", ""Nihon"", ""Nippon"", ""JP"", ""Nippon-koku"", ""Nihon-koku"", ""JA"", ""JPN"", ""jp"", ""JAP"", ""Ja"", ""Jap""]",2,2,"['Japan', 'State of Japan', 'Land of the Rising Sun', 'Nihon', 'Nippon', 'JP', 'Nippon-koku', 'Nihon-koku', 'JA', 'JPN', 'jp', 'JAP', 'Ja', 'Jap']","railway station in Saiki, Oita prefecture, Japan","In what country is Kamioka Station, a railway station located in Saiki, Oita prefecture?",True,"In what country is Kamioka Station, a railway station located in Saiki?"
1525,Fontenay,country,France,536,182,230035,[],"[""fr"",""FR"",""R\u00e9publique fran\u00e7aise"",""La France"",""Republic of France"",""French Republic"",""FRA"",""the Hexagon""]",http://www.wikidata.org/entity/Q1001153,http://www.wikidata.org/entity/Q142,"Fontenay, Manche",France,41,486947,In what country is Fontenay?,"[""France"", ""fr"", ""FR"", ""République française"", ""La France"", ""Republic of France"", ""French Republic"", ""FRA"", ""the Hexagon""]",11,7,"['France', 'fr', 'FR', 'République française', 'La France', 'Republic of France', 'French Republic', 'FRA', 'the Hexagon', 'Germany']","former commune in Manche, France","In what country is Fontenay, a former commune in Manche?",True,"""In what country is Fontenay, a former commune in Manche located?"""
6089822,Valea Seacă River,country,Romania,2739911,182,756886,[],"[""Roumania"",""Rumania"",""Rom\u00e2nia"",""ro"",""\ud83c\uddf7\ud83c\uddf4""]",http://www.wikidata.org/entity/Q7910319,http://www.wikidata.org/entity/Q218,Valea Seacă River (Mara),Romania,2,278539,In what country is Valea Seacă River?,"[""Romania"", ""Roumania"", ""Rumania"", ""România"", ""ro"", ""🇷🇴""]",37,36,"['Romania', 'Roumania', 'Rumania', 'România', 'ro', '🇷🇴']",tributary of the Valea Podului River,"In what country is the tributary of the Valea Podului River, Valea Seacă River?",True,In what country is the Valea Seacă River located?
3672517,Punghina,country,Romania,1594914,182,756886,[],"[""Roumania"",""Rumania"",""Rom\u00e2nia"",""ro"",""\ud83c\uddf7\ud83c\uddf4""]",http://www.wikidata.org/entity/Q5065667,http://www.wikidata.org/entity/Q218,Punghina,Romania,60,278539,In what country is Punghina?,"[""Romania"", ""Roumania"", ""Rumania"", ""România"", ""ro"", ""🇷🇴""]",3,2,"['Romania', 'Roumania', 'Rumania', 'România', 'ro', '🇷🇴']","commune in Mehedinți County, Romania","In what country is Punghina, a commune in Mehedinți County?",True,"In what country is Punghina, a commune located in Mehedinți County?"
6099166,Vera,country,United States of America,2744766,182,988513,"[""Vera, Virginia""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q7920730,http://www.wikidata.org/entity/Q30,"Vera, Virginia",United States,66,1629691,In what country is Vera?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",64,22,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'Norway', 'Spain', 'Czech Republic', 'Russia', 'Argentina', 'Canada', 'Tunisia', 'Brazil', 'Netherlands']","human settlement in Appomattox County, Virginia, United States","In what country is Vera, a human settlement in Appomattox County, Virginia?",True,"In what country is Vera, a human settlement in Appomattox County?"
5992288,Tigra,country,India,2690464,182,2215085,[],"[""Bharat"",""Hindustan"",""Bharatvarsh"",""in"",""IN"",""Republic of India"",""\ud83c\uddee\ud83c\uddf3"",""IND"",""Aryavratt""]",http://www.wikidata.org/entity/Q7801686,http://www.wikidata.org/entity/Q668,Tigra (gaon),India,78,1301086,In what country is Tigra?,"[""India"", ""Bharat"", ""Hindustan"", ""Bharatvarsh"", ""in"", ""IN"", ""Republic of India"", ""🇮🇳"", ""IND"", ""Aryavratt""]",7,4,"['India', 'Bharat', 'Hindustan', 'Bharatvarsh', 'in', 'IN', 'Republic of India', '🇮🇳', 'IND', 'Aryavratt', 'Switzerland', 'France', 'Angola']","village in Haryana, India","In what country is Tigra, a village located in Haryana?",True,"In what country is Tigra, a village in Haryana, located?"
4431678,Aminabad,country,Iran,1948661,182,2753763,[],"[""Islamic Republic of Iran"",""Persia"",""ir"",""Islamic Rep. Iran"",""\ud83c\uddee\ud83c\uddf7""]",http://www.wikidata.org/entity/Q5800534,http://www.wikidata.org/entity/Q794,"Aminabad, Kermanshah",Iran,24,317291,In what country is Aminabad?,"[""Iran"", ""Islamic Republic of Iran"", ""Persia"", ""ir"", ""Islamic Rep. Iran"", ""🇮🇷""]",18,17,"['Iran', 'Islamic Republic of Iran', 'Persia', 'ir', 'Islamic Rep. Iran', '🇮🇷', 'India']","village in Kermanshah, Iran","In what country is Aminabad, a village located in Kermanshah?",True,"In what country is Aminabad, a village in Kermanshah Province, located?"
3381798,Arthur,country,United States of America,1454433,182,988513,"[""Arthur, Nevada""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q4797729,http://www.wikidata.org/entity/Q30,"Arthur, Nevada",United States,72,1629691,In what country is Arthur?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",80,29,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'Belgium', 'Canada', 'Germany', 'Australia', 'United Kingdom', 'Ecuador', 'Sweden', 'France']","ghost town in Elko County, Nevada","In what country is Arthur, a ghost town in Elko County, Nevada?",True,"In what country is Arthur, a ghost town in Elko County?"
4304705,Kalu,country,Iran,1888757,182,2753763,[],"[""Islamic Republic of Iran"",""Persia"",""ir"",""Islamic Rep. Iran"",""\ud83c\uddee\ud83c\uddf7""]",http://www.wikidata.org/entity/Q5624952,http://www.wikidata.org/entity/Q794,"Kalu, Varzaqan",Iran,35,317291,In what country is Kalu?,"[""Iran"", ""Islamic Republic of Iran"", ""Persia"", ""ir"", ""Islamic Rep. Iran"", ""🇮🇷""]",13,7,"['Iran', 'Islamic Republic of Iran', 'Persia', 'ir', 'Islamic Rep. Iran', '🇮🇷', 'Kenya', 'Ethiopia', 'Papua New Guinea', 'Russia']","village in East Azerbaijan, Iran","In what country is Kalu, a village in East Azerbaijan?",True,"""In what country is Kalu, a village in East Azerbaijan located?"""
1445881,Pierce,country,United States of America,646469,182,988513,"[""Town of Pierce""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q1886636,http://www.wikidata.org/entity/Q30,"Pierce, Wisconsin",United States,80,1629691,In what country is Pierce?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",16,10,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica""]","civil town in Kewaunee County, Wisconsin","In what country is Pierce, a civil town in Kewaunee County, Wisconsin?",True,"In what country is Pierce, a civil town in Kewaunee County?"
1081675,Chalhuacocha,country,Peru,466102,182,1283964,[],"[""pe"",""Republic of Peru"",""Rep\u00fablica del Per\u00fa"",""\ud83c\uddf5\ud83c\uddea"",""Republica del Peru""]",http://www.wikidata.org/entity/Q16858901,http://www.wikidata.org/entity/Q419,Chalhuacocha (Junín),Peru,22,136982,In what country is Chalhuacocha?,"[""Peru"", ""pe"", ""Republic of Peru"", ""República del Perú"", ""🇵🇪"", ""Republica del Peru""]",4,4,"['Peru', 'pe', 'Republic of Peru', 'República del Perú', '🇵🇪', 'Republica del Peru']",body of water,In what country is Chalhuacocha?,False,In what country is Chalhuacocha located?
5353264,Oscar,country,United States of America,2379257,182,988513,[],"[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q7105834,http://www.wikidata.org/entity/Q30,"Oscar, West Virginia",United States,35,1629691,In what country is Oscar?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",59,20,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'Canada', 'United Kingdom', 'Belgium', 'Ukraine', 'Norway', 'France', 'Denmark', 'Sweden', 'Netherlands']","unincorporated community in Greenbrier County, West Virginia","In what country is Oscar, an unincorporated community in Greenbrier County, West Virginia?",True,"In what country is Oscar, an unincorporated community in Greenbrier County?"
3785558,Cora,country,United States of America,1648592,182,988513,"[""Cora, West Virginia""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q5169383,http://www.wikidata.org/entity/Q30,"Cora, West Virginia",United States,79,1629691,In what country is Cora?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",49,16,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'Spain', 'Italy', 'France', 'Mexico', 'Canada', 'United Kingdom', 'Mozambique', 'Chile']","unincorporated community in Logan County, West Virginia","In what country is Cora, an unincorporated community in Logan County, West Virginia?",True,"In what country is Cora, an unincorporated community in Logan County?"
1791858,Ježov,country,Czech Republic,791568,182,744365,[],"[""CZR"",""cz"",""\u010cesko"",""\u010cesk\u00e1 republika"",""\u010cR"",""cze"",""CZE"",""Czechia""]",http://www.wikidata.org/entity/Q2313180,http://www.wikidata.org/entity/Q213,Ježov (Hodonín District),Czech Republic,47,271047,In what country is Ježov?,"[""Czech Republic"", ""CZR"", ""cz"", ""Česko"", ""Česká republika"", ""ČR"", ""cze"", ""CZE"", ""Czechia""]",4,2,"['Czech Republic', 'CZR', 'cz', 'Česko', 'Česká republika', 'ČR', 'cze', 'CZE', 'Czechia']",village in Hodonín District of South Moravian region,"In what country is Ježov, a village in Hodonín District of the South Moravian region?",True,"In what country is Ježov, a village in Hodonín District of the South Moravian region located?"
3274434,Drăgăneasa River,country,Romania,1405201,182,756886,[],"[""Roumania"",""Rumania"",""Rom\u00e2nia"",""ro"",""\ud83c\uddf7\ud83c\uddf4""]",http://www.wikidata.org/entity/Q4716870,http://www.wikidata.org/entity/Q218,Drăgăneasa River (Provița),Romania,6,278539,In what country is Drăgăneasa River?,"[""Romania"", ""Roumania"", ""Rumania"", ""România"", ""ro"", ""🇷🇴""]",3,2,"['Romania', 'Roumania', 'Rumania', 'România', 'ro', '🇷🇴']",river in Romania,In what country is the Drăgăneasa River located?,True,In what country is the Drăgăneasa River found?
1004840,Sabiote,country,Spain,429408,182,962574,[],"[""Espa\u00f1a"",""Kingdom of Spain"",""ES"",""ESP""]",http://www.wikidata.org/entity/Q1641690,http://www.wikidata.org/entity/Q29,Sabiote,Spain,75,377325,In what country is Sabiote?,"[""Spain"", ""España"", ""Kingdom of Spain"", ""ES"", ""ESP""]",4,3,"['Spain', 'España', 'Kingdom of Spain', 'ES', 'ESP']",municipality of Spain,In what country is the municipality of Sabiote?,True,In what country is the municipality of Sabiote located?
4452323,Tad,country,Iran,1959056,182,2753763,"[""Tad, Falavarjan"",""Tad, Isfahan""]","[""Islamic Republic of Iran"",""Persia"",""ir"",""Islamic Rep. Iran"",""\ud83c\uddee\ud83c\uddf7""]",http://www.wikidata.org/entity/Q5827992,http://www.wikidata.org/entity/Q794,"Tad, Isfahan",Iran,53,317291,In what country is Tad?,"[""Iran"", ""Islamic Republic of Iran"", ""Persia"", ""ir"", ""Islamic Rep. Iran"", ""🇮🇷""]",6,2,"['Iran', 'Islamic Republic of Iran', 'Persia', 'ir', 'Islamic Rep. Iran', '🇮🇷', 'United States of America']","village in Falavarjan County, Iran","In what country is Tad, a village in Falavarjan County?",True,"In what country is Tad, a village in Falavarjan County, located?"
1246668,Villalcampo,country,Spain,555641,182,962574,[],"[""Espa\u00f1a"",""Kingdom of Spain"",""ES"",""ESP""]",http://www.wikidata.org/entity/Q1752356,http://www.wikidata.org/entity/Q29,Villalcampo,Spain,93,377325,In what country is Villalcampo?,"[""Spain"", ""España"", ""Kingdom of Spain"", ""ES"", ""ESP""]",3,2,"['Spain', 'España', 'Kingdom of Spain', 'ES', 'ESP']","municipality of Zamora Province, Spain","In what country is Villalcampo, a municipality of Zamora Province?",True,"In what country is Villalcampo, a municipality of Zamora Province, located?"
1742169,Scheidt,country,Germany,770225,182,607728,"[""Saarbr\u00fccken-Scheidt""]","[""FRG"",""BRD"",""Bundesrepublik Deutschland"",""Federal Republic of Germany"",""de"",""Deutschland"",""GER"",""BR Deutschland"",""DE""]",http://www.wikidata.org/entity/Q2232701,http://www.wikidata.org/entity/Q183,Scheidt (Saarbrücken),Germany,76,556493,In what country is Scheidt?,"[""Germany"", ""FRG"", ""BRD"", ""Bundesrepublik Deutschland"", ""Federal Republic of Germany"", ""de"", ""Deutschland"", ""GER"", ""BR Deutschland"", ""DE""]",9,7,"['Germany', 'FRG', 'BRD', 'Bundesrepublik Deutschland', 'Federal Republic of Germany', 'de', 'Deutschland', 'GER', 'BR Deutschland', 'DE']","stadtteil of Saarbrücken in Saarland, Germany","In what country is Scheidt, a stadtteil of Saarbrücken in Saarland?",True,"In what country is Scheidt, a stadtteil of Saarbrücken?"
4408940,Toronto Northwest,country,Canada,1937639,182,355510,[],"[""Dominion of Canada"",""British North America"",""CAN"",""CA"",""ca"",""can"",""Can.""]",http://www.wikidata.org/entity/Q576571,http://www.wikidata.org/entity/Q16,Toronto Northwest,Canada,86,816653,In what country is Toronto Northwest?,"[""Canada"", ""Dominion of Canada"", ""British North America"", ""CAN"", ""CA"", ""ca"", ""can"", ""Can.""]",2,2,"['Canada', 'Dominion of Canada', 'British North America', 'CAN', 'CA', 'ca', 'can', 'Can.']",federal electoral district of Canada,"In what country is Toronto Northwest, a federal electoral district?",True,"In what country is Toronto Northwest, a federal electoral district located?"
3416187,Awe,country,United States of America,1471592,182,988513,"[""Awe, Kentucky""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q4829997,http://www.wikidata.org/entity/Q30,"Awe, Kentucky",United States,91,1629691,In what country is Awe?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",14,9,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'Democratic Republic of the Congo', 'Nigeria', 'Indonesia']","township in Lewis County, Kentucky","In what country is Awe, a township in Lewis County, Kentucky?",True,"In what country is Awe, a township in Lewis County?"
1818407,Mrákotín,country,Czech Republic,801613,182,744365,"[""Mrakotin""]","[""CZR"",""cz"",""\u010cesko"",""\u010cesk\u00e1 republika"",""\u010cR"",""cze"",""CZE"",""Czechia""]",http://www.wikidata.org/entity/Q2349792,http://www.wikidata.org/entity/Q213,Mrákotín (Chrudim District),Czech Republic,45,271047,In what country is Mrákotín?,"[""Czech Republic"", ""CZR"", ""cz"", ""Česko"", ""Česká republika"", ""ČR"", ""cze"", ""CZE"", ""Czechia""]",3,2,"['Czech Republic', 'CZR', 'cz', 'Česko', 'Česká republika', 'ČR', 'cze', 'CZE', 'Czechia']",village in Chrudim District of Pardubice region,"In what country is Mrákotín, a village in Chrudim District of Pardubice region?",True,"In what country is Mrákotín, a village in Chrudim District of the Pardubice region located?"
4814263,Karimu,country,Iran,2119168,182,2753763,[],"[""Islamic Republic of Iran"",""Persia"",""ir"",""Islamic Rep. Iran"",""\ud83c\uddee\ud83c\uddf7""]",http://www.wikidata.org/entity/Q6370802,http://www.wikidata.org/entity/Q794,Karimu,Iran,77,317291,In what country is Karimu?,"[""Iran"", ""Islamic Republic of Iran"", ""Persia"", ""ir"", ""Islamic Rep. Iran"", ""🇮🇷""]",4,2,"['Iran', 'Islamic Republic of Iran', 'Persia', 'ir', 'Islamic Rep. Iran', '🇮🇷', 'Kenya']","village in South Khorasan, Iran","In what country is Karimu, a village in South Khorasan?",True,"In what country is Karimu, a village in South Khorasan located?"
3782263,Content,country,United States of America,1646785,182,988513,[],"[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q5165056,http://www.wikidata.org/entity/Q30,"Content (Centreville, Maryland)",United States,80,1629691,In what country is Content?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",172,10,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'British Hong Kong', 'Grenada', 'United Kingdom']","house near Centreville in Queen Anne's County, Maryland","In what country is Content, a house near Centreville in Queen Anne's County, Maryland?",True,"In what country is Content, a house near Centreville in Queen Anne's County?"
2996296,Zhukiv,country,Ukraine,1283098,182,741995,[],"[""UA"",""UKR"",""ua"",""Ukrainia"",""\ud83c\uddfa\ud83c\udde6"",""Ukr."",""Ukraina""]",http://www.wikidata.org/entity/Q4181416,http://www.wikidata.org/entity/Q212,Zhukiv,Ukraine,62,331690,In what country is Zhukiv?,"[""Ukraine"", ""UA"", ""UKR"", ""ua"", ""Ukrainia"", ""🇺🇦"", ""Ukr."", ""Ukraina""]",5,4,"['Ukraine', 'UA', 'UKR', 'ua', 'Ukrainia', '🇺🇦', 'Ukr.', 'Ukraina', 'Austria-Hungary', 'Austrian Empire']","village in Berezhany Raion, Ternopil Oblast, Ukraine","In what country is Zhukiv, a village located in Berezhany Raion, Ternopil Oblast?",True,"In what country is Zhukiv, a village located in Berezhany Raion?"
6152824,Weed,country,United States of America,2773353,182,988513,"[""Weed, Arkansas""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q7979321,http://www.wikidata.org/entity/Q30,"Weed, Arkansas",United States,38,1629691,In what country is Weed?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",12,4,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica""]","unincorporated community in Poinsett County, Arkansas","In what country is Weed, an unincorporated community in Poinsett County, Arkansas?",True,"In what country is Weed, an unincorporated community in Poinsett County?"
3268293,Alder,country,United States of America,1402643,182,988513,"[""Alder, Colorado""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q4713605,http://www.wikidata.org/entity/Q30,"Alder, Colorado",United States,72,1629691,In what country is Alder?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",17,11,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'Switzerland', 'Canada', 'Australia', 'United Kingdom']","human settlement in Saguache County, Colorado, United States of America","In what country is Alder, a human settlement in Saguache County, Colorado?",True,"In what country is Alder, a human settlement in Saguache County?"
1385147,Rogers,country,United States of America,619430,182,988513,"[""Rogers, Kansas""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q18390953,http://www.wikidata.org/entity/Q30,"Rogers, Kansas",United States,48,1629691,In what country is Rogers?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",26,21,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'Canada', 'Japan', 'Australia']",human settlement in United States of America,In what country is Rogers located?,True,"""In what country can Rogers be found?"""
125315,Cos,country,France,49816,182,230035,[],"[""fr"",""FR"",""R\u00e9publique fran\u00e7aise"",""La France"",""Republic of France"",""French Republic"",""FRA"",""the Hexagon""]",http://www.wikidata.org/entity/Q1082828,http://www.wikidata.org/entity/Q142,"Cos, Ariège",France,61,486947,In what country is Cos?,"[""France"", ""fr"", ""FR"", ""République française"", ""La France"", ""Republic of France"", ""French Republic"", ""FRA"", ""the Hexagon""]",10,6,"['France', 'fr', 'FR', 'République française', 'La France', 'Republic of France', 'French Republic', 'FRA', 'the Hexagon', 'Greece', 'Spain']","commune in Ariège, France","In what country is Cos, a commune in Ariège?",True,"In what country is Cos, a commune in Ariège located?"
4136000,Francis,country,Canada,1813852,182,355510,[],"[""Dominion of Canada"",""British North America"",""CAN"",""CA"",""ca"",""can"",""Can.""]",http://www.wikidata.org/entity/Q5479899,http://www.wikidata.org/entity/Q16,Francis (electoral district),Canada,74,816653,In what country is Francis?,"[""Canada"", ""Dominion of Canada"", ""British North America"", ""CAN"", ""CA"", ""ca"", ""can"", ""Can.""]",36,11,"['Canada', 'Dominion of Canada', 'British North America', 'CAN', 'CA', 'ca', 'can', 'Can.', 'United Kingdom', 'United States of America']",former Canadian electoral district,"In what country is Francis, a former electoral district?",True,"""In what country is Francis, a former district?"""
3668918,Centre,country,Belgium,1592997,182,1021279,[],"[""Kingdom of Belgium"",""BEL"",""be"",""\ud83c\udde7\ud83c\uddea"",""BE""]",http://www.wikidata.org/entity/Q5062220,http://www.wikidata.org/entity/Q31,"Centre, Wallonia",Belgium,44,249550,In what country is Centre?,"[""Belgium"", ""Kingdom of Belgium"", ""BEL"", ""be"", ""🇧🇪"", ""BE""]",17,15,"['Belgium', 'Kingdom of Belgium', 'BEL', 'be', '🇧🇪', 'BE', 'Burkina Faso', 'United States of America', 'Poland', 'Switzerland', 'Haiti', 'Norway', 'Cameroon', 'Netherlands', 'Thailand', 'Canada', 'Croatia', 'Spain', 'Sweden']",,In what country is Centre?,False,In what country is Centre located?
3391136,Asahi Station,country,Japan,1458025,182,502871,[],"[""State of Japan"",""Land of the Rising Sun"",""Nihon"",""Nippon"",""JP"",""Nippon-koku"",""Nihon-koku"",""JA"",""JPN"",""jp"",""JAP"",""Ja"",""Jap""]",http://www.wikidata.org/entity/Q4803372,http://www.wikidata.org/entity/Q17,Asahi Station (Kōchi),Japan,47,702414,In what country is Asahi Station?,"[""Japan"", ""State of Japan"", ""Land of the Rising Sun"", ""Nihon"", ""Nippon"", ""JP"", ""Nippon-koku"", ""Nihon-koku"", ""JA"", ""JPN"", ""jp"", ""JAP"", ""Ja"", ""Jap""]",7,6,"['Japan', 'State of Japan', 'Land of the Rising Sun', 'Nihon', 'Nippon', 'JP', 'Nippon-koku', 'Nihon-koku', 'JA', 'JPN', 'jp', 'JAP', 'Ja', 'Jap']","railway station in Kochi, Kochi prefecture, Japan","In what country is Asahi Station, a railway station located in Kochi, Kochi prefecture?",True,"In what country is Asahi Station, a railway station located in Kochi prefecture?"
3590116,Bud,country,United States of America,1553162,182,988513,[],"[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q4983752,http://www.wikidata.org/entity/Q30,"Bud, Wisconsin",United States,78,1629691,In what country is Bud?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",11,4,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'Norway']","unincorporated community in the Town of Jefferson, Vernon County, Wisconsin","In what country is Bud, an unincorporated community in the Town of Jefferson, Vernon County, Wisconsin?",True,"In what country is Bud, an unincorporated community in the Town of Jefferson, Vernon County?"
2567382,Perth,country,Canada,1107933,182,355510,[],"[""Dominion of Canada"",""British North America"",""CAN"",""CA"",""ca"",""can"",""Can.""]",http://www.wikidata.org/entity/Q3376164,http://www.wikidata.org/entity/Q16,Perth (electoral district),Canada,97,816653,In what country is Perth?,"[""Canada"", ""Dominion of Canada"", ""British North America"", ""CAN"", ""CA"", ""ca"", ""can"", ""Can.""]",39,27,"['Canada', 'Dominion of Canada', 'British North America', 'CAN', 'CA', 'ca', 'can', 'Can.', 'Australia', 'United Kingdom', 'Brazil', 'United States of America']",federal electoral district of Canada,"In what country is Perth, a federal electoral district?",True,"In what country is Perth, a federal electoral district located?"
4498593,Ara,country,India,1982478,182,2215085,[],"[""Bharat"",""Hindustan"",""Bharatvarsh"",""in"",""IN"",""Republic of India"",""\ud83c\uddee\ud83c\uddf3"",""IND"",""Aryavratt""]",http://www.wikidata.org/entity/Q590838,http://www.wikidata.org/entity/Q668,"Ara, Jharkhand",India,84,1301086,In what country is Ara?,"[""India"", ""Bharat"", ""Hindustan"", ""Bharatvarsh"", ""in"", ""IN"", ""Republic of India"", ""🇮🇳"", ""IND"", ""Aryavratt""]",46,21,"['India', 'Bharat', 'Hindustan', 'Bharatvarsh', 'in', 'IN', 'Republic of India', '🇮🇳', 'IND', 'Aryavratt', 'Italy', 'New Zealand', 'Russia', 'Norway', 'Iran', 'Guinea', 'Indonesia', 'Ireland', 'Central African Republic', 'Japan', 'Spain', 'Democratic Republic of the Congo']",human settlement,In what country is the human settlement Ara?,True,"""In what country is the settlement Ara located?"""
4185439,Riethnordhausen,country,Germany,1835317,182,607728,[],"[""FRG"",""BRD"",""Bundesrepublik Deutschland"",""Federal Republic of Germany"",""de"",""Deutschland"",""GER"",""BR Deutschland"",""DE""]",http://www.wikidata.org/entity/Q551988,http://www.wikidata.org/entity/Q183,"Riethnordhausen, Thuringia",Germany,61,556493,In what country is Riethnordhausen?,"[""Germany"", ""FRG"", ""BRD"", ""Bundesrepublik Deutschland"", ""Federal Republic of Germany"", ""de"", ""Deutschland"", ""GER"", ""BR Deutschland"", ""DE""]",3,2,"['Germany', 'FRG', 'BRD', 'Bundesrepublik Deutschland', 'Federal Republic of Germany', 'de', 'Deutschland', 'GER', 'BR Deutschland', 'DE']",municipality of Germany,In what country is Riethnordhausen located?,True,In what country is Riethnordhausen?
1596354,Alu,country,Estonia,711990,182,656083,[],"[""Republic of Estonia"",""Estland"",""Eesti"",""ee"",""EST"",""\ud83c\uddea\ud83c\uddea""]",http://www.wikidata.org/entity/Q2026558,http://www.wikidata.org/entity/Q191,"Alu, Pärnu County",Estonia,44,169786,In what country is Alu?,"[""Estonia"", ""Republic of Estonia"", ""Estland"", ""Eesti"", ""ee"", ""EST"", ""🇪🇪""]",16,6,"['Estonia', 'Republic of Estonia', 'Estland', 'Eesti', 'ee', 'EST', '🇪🇪', 'Democratic Republic of the Congo', 'Finland', 'Ethiopia', 'Uganda']","village in Pärnu, Pärnu County, Estonia","In what country is Alu, a village in Pärnu, Pärnu County?",True,"In what country is Alu, a village in Pärnu County?"
5962937,Chotýčany,country,Czech Republic,2676937,182,744365,[],"[""CZR"",""cz"",""\u010cesko"",""\u010cesk\u00e1 republika"",""\u010cR"",""cze"",""CZE"",""Czechia""]",http://www.wikidata.org/entity/Q777306,http://www.wikidata.org/entity/Q213,Chotýčany,Czech Republic,44,271047,In what country is Chotýčany?,"[""Czech Republic"", ""CZR"", ""cz"", ""Česko"", ""Česká republika"", ""ČR"", ""cze"", ""CZE"", ""Czechia""]",3,3,"['Czech Republic', 'CZR', 'cz', 'Česko', 'Česká republika', 'ČR', 'cze', 'CZE', 'Czechia']",village in České Budějovice District of South Bohemian region,"In what country is Chotýčany, a village in the České Budějovice District of the South Bohemian region?",True,"In what country is Chotýčany, a village in the South Bohemian region?"
3952467,Dragomirna River,country,Romania,1726018,182,756886,[],"[""Roumania"",""Rumania"",""Rom\u00e2nia"",""ro"",""\ud83c\uddf7\ud83c\uddf4""]",http://www.wikidata.org/entity/Q5305034,http://www.wikidata.org/entity/Q218,Dragomirna (Suceava),Romania,48,278539,In what country is Dragomirna River?,"[""Romania"", ""Roumania"", ""Rumania"", ""România"", ""ro"", ""🇷🇴""]",3,2,"['Romania', 'Roumania', 'Rumania', 'România', 'ro', '🇷🇴']","river in Suceava County, Romania","In what country is Dragomirna River, located in Suceava County?",True,In what country is Dragomirna River located?
1741307,Grant,country,Slovenia,769859,182,749148,[],"[""Slovenija"",""Republika Slovenija"",""si"",""\ud83c\uddf8\ud83c\uddee"",""svn"",""slo"",""Republic of Slovenia"",""SLO""]",http://www.wikidata.org/entity/Q2231457,http://www.wikidata.org/entity/Q215,"Grant, Tolmin",Slovenia,56,140196,In what country is Grant?,"[""Slovenia"", ""Slovenija"", ""Republika Slovenija"", ""si"", ""🇸🇮"", ""svn"", ""slo"", ""Republic of Slovenia"", ""SLO""]",50,36,"['Slovenia', 'Slovenija', 'Republika Slovenija', 'si', '🇸🇮', 'svn', 'slo', 'Republic of Slovenia', 'SLO', 'United States of America', 'Australia', 'Canada', 'Germany', 'New Zealand']","place in Slovenian Littoral, Slovenia","In what country is Grant, a place in the Slovenian Littoral?",True,"In what country is Grant, a place in the Littoral region?"
4966829,Lima,country,United States of America,2190805,182,988513,"[""Lima, Wisconsin""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q6548789,http://www.wikidata.org/entity/Q30,"Lima (community), Wisconsin",United States,29,1629691,In what country is Lima?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",64,47,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'United Kingdom', 'Paraguay', 'Oman', 'Italy', 'Sweden', 'Australia', 'Spain', 'Argentina', 'Peru', 'Brazil', 'Cuba', 'Iran', 'Angola', 'Republic of the Congo']",community in Wisconsin,"In what country is Lima, a community in Wisconsin?",True,"In what country is Lima, a community located in Wisconsin?"
2979928,Záblatí,country,Czech Republic,1275975,182,744365,"[""Zablati""]","[""CZR"",""cz"",""\u010cesko"",""\u010cesk\u00e1 republika"",""\u010cR"",""cze"",""CZE"",""Czechia""]",http://www.wikidata.org/entity/Q41192,http://www.wikidata.org/entity/Q213,Záblatí (Prachatice District),Czech Republic,53,271047,In what country is Záblatí?,"[""Czech Republic"", ""CZR"", ""cz"", ""Česko"", ""Česká republika"", ""ČR"", ""cze"", ""CZE"", ""Czechia""]",9,8,"['Czech Republic', 'CZR', 'cz', 'Česko', 'Česká republika', 'ČR', 'cze', 'CZE', 'Czechia']",village in Prachatice District of South Bohemian region,"In what country is Záblatí, a village in the Prachatice District of the South Bohemian region?",True,"In what country is Záblatí, a village in the Prachatice District of the South Bohemian region located?"
215156,Kawahigashi Station,country,Japan,86946,182,502871,[],"[""State of Japan"",""Land of the Rising Sun"",""Nihon"",""Nippon"",""JP"",""Nippon-koku"",""Nihon-koku"",""JA"",""JPN"",""jp"",""JAP"",""Ja"",""Jap""]",http://www.wikidata.org/entity/Q11478848,http://www.wikidata.org/entity/Q17,Kawahigashi Station (Fukushima),Japan,36,702414,In what country is Kawahigashi Station?,"[""Japan"", ""State of Japan"", ""Land of the Rising Sun"", ""Nihon"", ""Nippon"", ""JP"", ""Nippon-koku"", ""Nihon-koku"", ""JA"", ""JPN"", ""jp"", ""JAP"", ""Ja"", ""Jap""]",4,3,"['Japan', 'State of Japan', 'Land of the Rising Sun', 'Nihon', 'Nippon', 'JP', 'Nippon-koku', 'Nihon-koku', 'JA', 'JPN', 'jp', 'JAP', 'Ja', 'Jap']","railway station in Sukagawa, Fukushima prefecture, Japan","In what country is Kawahigashi Station, a railway station located in Sukagawa, Fukushima prefecture?",True,"In what country is Kawahigashi Station, located in Sukagawa, Fukushima prefecture?"
5189030,Contest,country,France,2296189,182,230035,[],"[""fr"",""FR"",""R\u00e9publique fran\u00e7aise"",""La France"",""Republic of France"",""French Republic"",""FRA"",""the Hexagon""]",http://www.wikidata.org/entity/Q687442,http://www.wikidata.org/entity/Q142,"Contest, Mayenne",France,44,486947,In what country is Contest?,"[""France"", ""fr"", ""FR"", ""République française"", ""La France"", ""Republic of France"", ""French Republic"", ""FRA"", ""the Hexagon""]",16,4,"['France', 'fr', 'FR', 'République française', 'La France', 'Republic of France', 'French Republic', 'FRA', 'the Hexagon', 'Australia', 'United Kingdom']","commune in Mayenne, France","In what country is Contest, a commune in Mayenne?",True,"In what country is Contest, a commune located?"
4202811,Genoa,country,United States of America,1843152,182,988513,"[""Genoa, Minnesota""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q5533418,http://www.wikidata.org/entity/Q30,"Genoa, Minnesota",United States,64,1629691,In what country is Genoa?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",33,22,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'Italy', 'Australia', 'United Kingdom', 'New Zealand', 'Canada']","unincorporated community in Olmsted County, Minnesota","In what country is Genoa, an unincorporated community in Olmsted County, Minnesota?",True,"In what country is Genoa, an unincorporated community in Olmsted County?"
1469534,Normania Township,country,United States of America,656272,182,988513,[],"[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q1910672,http://www.wikidata.org/entity/Q30,"Normania Township, Yellow Medicine County, Minnesota",United States,57,1629691,In what country is Normania Township?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",3,2,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica""]","township in Yellow Medicine County, Minnesota","In what country is Normania Township, located in Yellow Medicine County, Minnesota?",True,"In which country is Normania Township, located in Yellow Medicine County, Minnesota?"
3905343,Devanur,country,India,1704501,182,2215085,[],"[""Bharat"",""Hindustan"",""Bharatvarsh"",""in"",""IN"",""Republic of India"",""\ud83c\uddee\ud83c\uddf3"",""IND"",""Aryavratt""]",http://www.wikidata.org/entity/Q5266505,http://www.wikidata.org/entity/Q668,Devanur,India,89,1301086,In what country is Devanur?,"[""India"", ""Bharat"", ""Hindustan"", ""Bharatvarsh"", ""in"", ""IN"", ""Republic of India"", ""🇮🇳"", ""IND"", ""Aryavratt""]",3,3,"['India', 'Bharat', 'Hindustan', 'Bharatvarsh', 'in', 'IN', 'Republic of India', '🇮🇳', 'IND', 'Aryavratt']","village in Tamil Nadu, India","In what country is Devanur, a village in Tamil Nadu?",True,"In which country is Devanur, a village in Tamil Nadu, located?"
1949764,Tegher,country,Armenia,856894,182,1257196,[],"[""Republic of Armenia"",""\ud83c\udde6\ud83c\uddf2"",""ARM"",""AM""]",http://www.wikidata.org/entity/Q2535629,http://www.wikidata.org/entity/Q399,"Tegher, Armenia",Armenia,93,196468,In what country is Tegher?,"[""Armenia"", ""Republic of Armenia"", ""🇦🇲"", ""ARM"", ""AM""]",3,2,"['Armenia', 'Republic of Armenia', '🇦🇲', 'ARM', 'AM']",village in Aragatsotn Province of Armenia,"In what country is Tegher, a village located in Aragatsotn Province?",True,"In what country is Tegher, a village in Aragatsotn Province, located?"
4012395,New England,country,Australia,1753305,182,1270922,"[""Electoral district of New England"",""New England NSW"",""New England electoral district""]","[""Commonwealth of Australia"",""AU"",""AUS"",""au"",""British Colony of Australia"",""\ud83c\udde6\ud83c\uddfa"",""Straya"",""Aussieland""]",http://www.wikidata.org/entity/Q5355766,http://www.wikidata.org/entity/Q408,Electoral district of New England,Australia,85,713574,In what country is New England?,"[""Australia"", ""Commonwealth of Australia"", ""AU"", ""AUS"", ""au"", ""British Colony of Australia"", ""🇦🇺"", ""Straya"", ""Aussieland""]",25,14,"['Australia', 'Commonwealth of Australia', 'AU', 'AUS', 'au', 'British Colony of Australia', '🇦🇺', 'Straya', 'Aussieland', 'South Africa', 'United States of America', 'United Kingdom', 'Canada']","former state electoral district of New South Wales, Australia","In what country is New England, a former state electoral district of New South Wales?",True,"In what country is New England, a former state electoral district?"
6265961,La Couarde-sur-Mer,country,France,2828799,182,230035,[],"[""fr"",""FR"",""R\u00e9publique fran\u00e7aise"",""La France"",""Republic of France"",""French Republic"",""FRA"",""the Hexagon""]",http://www.wikidata.org/entity/Q818599,http://www.wikidata.org/entity/Q142,La Couarde-sur-Mer,France,87,486947,In what country is La Couarde-sur-Mer?,"[""France"", ""fr"", ""FR"", ""République française"", ""La France"", ""Republic of France"", ""French Republic"", ""FRA"", ""the Hexagon""]",2,2,"['France', 'fr', 'FR', 'République française', 'La France', 'Republic of France', 'French Republic', 'FRA', 'the Hexagon']","commune in Charente-Maritime, France","In what country is La Couarde-sur-Mer, a commune in Charente-Maritime?",True,"In what country is La Couarde-sur-Mer, a commune in Charente-Maritime located?"
6376093,Riechheimer Berg,country,Germany,2872386,182,607728,[],"[""FRG"",""BRD"",""Bundesrepublik Deutschland"",""Federal Republic of Germany"",""de"",""Deutschland"",""GER"",""BR Deutschland"",""DE""]",http://www.wikidata.org/entity/Q896050,http://www.wikidata.org/entity/Q183,Riechheimer Berg,Germany,39,556493,In what country is Riechheimer Berg?,"[""Germany"", ""FRG"", ""BRD"", ""Bundesrepublik Deutschland"", ""Federal Republic of Germany"", ""de"", ""Deutschland"", ""GER"", ""BR Deutschland"", ""DE""]",2,2,"['Germany', 'FRG', 'BRD', 'Bundesrepublik Deutschland', 'Federal Republic of Germany', 'de', 'Deutschland', 'GER', 'BR Deutschland', 'DE']","municipal association in Thuringia, Germany","In what country is Riechheimer Berg, a municipal association in Thuringia?",True,"""In what country is Riechheimer Berg, a municipal association in Thuringia located?"""
5308029,North Lake,country,Canada,2355667,182,355510,[],"[""Dominion of Canada"",""British North America"",""CAN"",""CA"",""ca"",""can"",""Can.""]",http://www.wikidata.org/entity/Q7055883,http://www.wikidata.org/entity/Q16,North Lake (Nova Scotia),Canada,57,816653,In what country is North Lake?,"[""Canada"", ""Dominion of Canada"", ""British North America"", ""CAN"", ""CA"", ""ca"", ""can"", ""Can.""]",81,79,"['Canada', 'Dominion of Canada', 'British North America', 'CAN', 'CA', 'ca', 'can', 'Can.', 'United States of America', 'Australia']","lake in Nova Scotia, Canada","In what country is North Lake, a lake located in Nova Scotia?",True,"""In what country is North Lake, a lake located in Nova Scotia, situated?"""
4591269,Bārta,country,Latvia,2024824,182,739213,"[""Bartuva""]","[""Republic of Latvia"",""Latvian Republic"",""lv"",""Latvija"",""\ud83c\uddf1\ud83c\uddfb"",""LAT"",""LVA""]",http://www.wikidata.org/entity/Q610187,http://www.wikidata.org/entity/Q211,Bārta,Latvia,78,151926,In what country is Bārta?,"[""Latvia"", ""Republic of Latvia"", ""Latvian Republic"", ""lv"", ""Latvija"", ""🇱🇻"", ""LAT"", ""LVA""]",2,2,"['Latvia', 'Republic of Latvia', 'Latvian Republic', 'lv', 'Latvija', '🇱🇻', 'LAT', 'LVA', 'Lithuania']",river in Latvia,In what country is the river Bārta located?,True,In what country is the river Bārta found?
1639019,Urge,country,Estonia,728450,182,656083,[],"[""Republic of Estonia"",""Estland"",""Eesti"",""ee"",""EST"",""\ud83c\uddea\ud83c\uddea""]",http://www.wikidata.org/entity/Q2075028,http://www.wikidata.org/entity/Q191,"Urge, Rapla County",Estonia,77,169786,In what country is Urge?,"[""Estonia"", ""Republic of Estonia"", ""Estland"", ""Eesti"", ""ee"", ""EST"", ""🇪🇪""]",10,2,"['Estonia', 'Republic of Estonia', 'Estland', 'Eesti', 'ee', 'EST', '🇪🇪']","village in Kohila Rural Municipality, Rapla County, Estonia","In what country is Urge, a village in Kohila Rural Municipality, Rapla County?",True,"""In what country is Urge, a village in Kohila Rural Municipality?"""
5835502,Domašov,country,Czech Republic,2611845,182,744365,[],"[""CZR"",""cz"",""\u010cesko"",""\u010cesk\u00e1 republika"",""\u010cR"",""cze"",""CZE"",""Czechia""]",http://www.wikidata.org/entity/Q761824,http://www.wikidata.org/entity/Q213,Domašov,Czech Republic,88,271047,In what country is Domašov?,"[""Czech Republic"", ""CZR"", ""cz"", ""Česko"", ""Česká republika"", ""ČR"", ""cze"", ""CZE"", ""Czechia""]",3,2,"['Czech Republic', 'CZR', 'cz', 'Česko', 'Česká republika', 'ČR', 'cze', 'CZE', 'Czechia']",village in Brno-venkov District of South Moravian region,"In what country is Domašov, a village in Brno-venkov District of the South Moravian region?",True,"In what country is Domašov, a village in the South Moravian region?"
1430021,Ločenice,country,Czech Republic,639609,182,744365,[],"[""CZR"",""cz"",""\u010cesko"",""\u010cesk\u00e1 republika"",""\u010cR"",""cze"",""CZE"",""Czechia""]",http://www.wikidata.org/entity/Q1872754,http://www.wikidata.org/entity/Q213,Ločenice,Czech Republic,46,271047,In what country is Ločenice?,"[""Czech Republic"", ""CZR"", ""cz"", ""Česko"", ""Česká republika"", ""ČR"", ""cze"", ""CZE"", ""Czechia""]",2,2,"['Czech Republic', 'CZR', 'cz', 'Česko', 'Česká republika', 'ČR', 'cze', 'CZE', 'Czechia']",village in České Budějovice District of South Bohemian region,"In what country is Ločenice, a village in the České Budějovice District of the South Bohemian region?",True,"In what country is Ločenice, a village in the South Bohemian region?"
3248819,Aki,country,Japan,1394282,182,502871,[],"[""State of Japan"",""Land of the Rising Sun"",""Nihon"",""Nippon"",""JP"",""Nippon-koku"",""Nihon-koku"",""JA"",""JPN"",""jp"",""JAP"",""Ja"",""Jap""]",http://www.wikidata.org/entity/Q4700842,http://www.wikidata.org/entity/Q17,"Aki, Ōita",Japan,60,702414,In what country is Aki?,"[""Japan"", ""State of Japan"", ""Land of the Rising Sun"", ""Nihon"", ""Nippon"", ""JP"", ""Nippon-koku"", ""Nihon-koku"", ""JA"", ""JPN"", ""jp"", ""JAP"", ""Ja"", ""Jap""]",33,7,"['Japan', 'State of Japan', 'Land of the Rising Sun', 'Nihon', 'Nippon', 'JP', 'Nippon-koku', 'Nihon-koku', 'JA', 'JPN', 'jp', 'JAP', 'Ja', 'Jap', 'Italy', 'Uganda']","dissolved municipality in Higashikunisaki district, Ōita prefecture, Japan","In what country is Aki, a dissolved municipality in Higashikunisaki district, Ōita prefecture?",True,"In what country is Aki, a dissolved municipality in Higashikunisaki district?"
1475631,Morales de Campos,country,Spain,658872,182,962574,[],"[""Espa\u00f1a"",""Kingdom of Spain"",""ES"",""ESP""]",http://www.wikidata.org/entity/Q1919453,http://www.wikidata.org/entity/Q29,Morales de Campos,Spain,56,377325,In what country is Morales de Campos?,"[""Spain"", ""España"", ""Kingdom of Spain"", ""ES"", ""ESP""]",2,2,"['Spain', 'España', 'Kingdom of Spain', 'ES', 'ESP']",municipality of Spain,In what country is Morales de Campos located?,True,In what country is Morales de Campos found?
3927118,Dobra River,country,Romania,1714891,182,756886,[],"[""Roumania"",""Rumania"",""Rom\u00e2nia"",""ro"",""\ud83c\uddf7\ud83c\uddf4""]",http://www.wikidata.org/entity/Q5286678,http://www.wikidata.org/entity/Q218,Dobra River (Lotru),Romania,11,278539,In what country is Dobra River?,"[""Romania"", ""Roumania"", ""Rumania"", ""România"", ""ro"", ""🇷🇴""]",9,8,"['Romania', 'Roumania', 'Rumania', 'România', 'ro', '🇷🇴']",,In what country is Dobra River?,False,"""In what country is the Dobra River located?"""
4811648,Karahasanlı,country,Turkey,2117928,182,1297158,"[""Karahasanl\u0131, Karaisal\u0131""]","[""Republic of Turkey"",""\ud83c\uddf9\ud83c\uddf7"",""TUR"",""TR""]",http://www.wikidata.org/entity/Q6367944,http://www.wikidata.org/entity/Q43,"Karahasanlı, Karaisalı",Turkey,27,454969,In what country is Karahasanlı?,"[""Turkey"", ""Republic of Turkey"", ""🇹🇷"", ""TUR"", ""TR""]",4,4,"['Turkey', 'Republic of Turkey', '🇹🇷', 'TUR', 'TR']","neighborhood in Karaisalı, Adana, Turkey","In what country is Karahasanlı, a neighborhood in Karaisalı, Adana?",True,"In what country is Karahasanlı, a neighborhood in Karaisalı?"
3375127,Arlington,country,United States of America,1451324,182,988513,[],"[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q4792252,http://www.wikidata.org/entity/Q30,"Arlington, Harrison County, West Virginia",United States,87,1629691,In what country is Arlington?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",64,55,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'Canada', 'United Kingdom', 'Australia', 'South Africa']","unincorporated community in Harrison County, West Virginia","In what country is Arlington, an unincorporated community in Harrison County, West Virginia?",True,"""In what country is Arlington, an unincorporated community in Harrison County?"""
1506440,Adams,country,United States of America,672162,182,988513,"[""Adams, New Jersey""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q19460101,http://www.wikidata.org/entity/Q30,"Adams, New Jersey",United States,99,1629691,In what country is Adams?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",49,34,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'Canada', 'United Kingdom', 'Philippines', 'Australia']","unincorporated community in New Jersey, United States","In what country is Adams, an unincorporated community in New Jersey?",True,"In what country is Adams, an unincorporated community located?"
344660,Pira,country,Spain,138582,182,962574,[],"[""Espa\u00f1a"",""Kingdom of Spain"",""ES"",""ESP""]",http://www.wikidata.org/entity/Q1248551,http://www.wikidata.org/entity/Q29,"Pira, Tarragona",Spain,82,377325,In what country is Pira?,"[""Spain"", ""España"", ""Kingdom of Spain"", ""ES"", ""ESP""]",15,9,"['Spain', 'España', 'Kingdom of Spain', 'ES', 'ESP', 'Australia', 'Peru', 'Nigeria', 'Italy', 'Benin', 'Russia', 'France']",municipality of Spain,In what country is the municipality of Pira?,True,In what country is the municipality located?
5264183,Neal,country,United States of America,2335244,182,988513,"[""Neal, Illinois""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q6984028,http://www.wikidata.org/entity/Q30,"Neal, Illinois",United States,40,1629691,In what country is Neal?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",9,5,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica""]",unincorporated community in Illinois,"In what country is Neal, an unincorporated community in Illinois?",True,"In what country is Neal, an unincorporated community?"
2347689,Kijevac,country,Serbia,1023282,182,1263285,[],"[""\ud83c\uddf7\ud83c\uddf8"",""Republic of Serbia"",""Republika Srbija"",""rs"",""Srbija"",""SRB"",""RS""]",http://www.wikidata.org/entity/Q3105187,http://www.wikidata.org/entity/Q403,Kijevac (Surdulica),Serbia,47,232782,In what country is Kijevac?,"[""Serbia"", ""🇷🇸"", ""Republic of Serbia"", ""Republika Srbija"", ""rs"", ""Srbija"", ""SRB"", ""RS""]",5,4,"['Serbia', '🇷🇸', 'Republic of Serbia', 'Republika Srbija', 'rs', 'Srbija', 'SRB', 'RS']","village in Pčinja District, Serbia","In what country is Kijevac, a village in the Pčinja District?",True,"In what country is Kijevac, a village located in the Pčinja District?"
5696,Saint-Antonin,country,France,2221,182,230035,[],"[""fr"",""FR"",""R\u00e9publique fran\u00e7aise"",""La France"",""Republic of France"",""French Republic"",""FRA"",""the Hexagon""]",http://www.wikidata.org/entity/Q1004795,http://www.wikidata.org/entity/Q142,"Saint-Antonin, Gers",France,57,486947,In what country is Saint-Antonin?,"[""France"", ""fr"", ""FR"", ""République française"", ""La France"", ""Republic of France"", ""French Republic"", ""FRA"", ""the Hexagon""]",7,6,"['France', 'fr', 'FR', 'République française', 'La France', 'Republic of France', 'French Republic', 'FRA', 'the Hexagon', 'Canada']","commune in Gers, France","In what country is Saint-Antonin, a commune in Gers?",True,"In what country is Saint-Antonin, a commune in Gers located?"
5435300,Peterson,country,United States of America,2416440,182,988513,"[""Peterson, Indiana""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q7178235,http://www.wikidata.org/entity/Q30,"Peterson, Indiana",United States,47,1629691,In what country is Peterson?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",19,12,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'Canada', 'New Zealand', 'Norway']","unincorporated community in Adams County, Indiana, United States","In what country is Peterson, an unincorporated community in Adams County, Indiana, located?",True,"In what country is Peterson, an unincorporated community in Adams County, located?"
612520,Joy,country,United States of America,250917,182,988513,[],"[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q14680988,http://www.wikidata.org/entity/Q30,"Joy, Arkansas",United States,63,1629691,In what country is Joy?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",107,17,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'Japan', 'Finland', 'Netherlands', 'South Africa', 'Germany', 'Poland']","unincorporated community in White County, Arkansas","In what country is Joy, an unincorporated community in White County, Arkansas?",True,"In what country is Joy, an unincorporated community in White County?"
6089701,Valea Pleșii River,country,Romania,2739797,182,756886,[],"[""Roumania"",""Rumania"",""Rom\u00e2nia"",""ro"",""\ud83c\uddf7\ud83c\uddf4""]",http://www.wikidata.org/entity/Q7910169,http://www.wikidata.org/entity/Q218,Valea Pleșii River (Bârsa),Romania,3,278539,In what country is Valea Pleșii River?,"[""Romania"", ""Roumania"", ""Rumania"", ""România"", ""ro"", ""🇷🇴""]",3,2,"['Romania', 'Roumania', 'Rumania', 'România', 'ro', '🇷🇴']",tributary of the Bârsa River in Romania,"In what country is Valea Pleșii River, a tributary of the Bârsa River?",True,In what country is Valea Pleșii River located?
1292989,Revigliasco d'Asti,country,Italy,575278,182,1218153,[],"[""Italia"",""Italian Republic"",""IT"",""\ud83c\uddee\ud83c\uddf9"",""ITA""]",http://www.wikidata.org/entity/Q17849,http://www.wikidata.org/entity/Q38,Revigliasco d'Asti,Italy,95,403188,In what country is Revigliasco d'Asti?,"[""Italy"", ""Italia"", ""Italian Republic"", ""IT"", ""🇮🇹"", ""ITA""]",3,2,"['Italy', 'Italia', 'Italian Republic', 'IT', '🇮🇹', 'ITA']",Italian comune,"In what country is Revigliasco d'Asti, an Italian comune?",True,In what country is Revigliasco d'Asti located?
1093104,Willow River,country,United States of America,473455,182,988513,[],"[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q1688723,http://www.wikidata.org/entity/Q30,Willow River (Mississippi River tributary),United States,62,1629691,In what country is Willow River?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",16,15,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'Canada']",river in United States of America,In what country is Willow River?,False,In what country is Willow River located?
3438406,Ban On,country,Thailand,1482850,182,2858195,[],"[""Kingdom of Thailand"",""th"",""Siam"",""\ud83c\uddf9\ud83c\udded"",""Land of Smiles"",""THA""]",http://www.wikidata.org/entity/Q4853659,http://www.wikidata.org/entity/Q869,Ban On,Thailand,42,249163,In what country is Ban On?,"[""Thailand"", ""Kingdom of Thailand"", ""th"", ""Siam"", ""🇹🇭"", ""Land of Smiles"", ""THA""]",2,2,"['Thailand', 'Kingdom of Thailand', 'th', 'Siam', '🇹🇭', 'Land of Smiles', 'THA', 'Vietnam']","subdistrict in Ngao district, Lampang province, Thailand","In what country is Ban On, a subdistrict in Ngao district, Lampang province?",True,"In what country is Ban On, a subdistrict in Ngao district?"
2634990,Kanaküla,country,Estonia,1135599,182,656083,[],"[""Republic of Estonia"",""Estland"",""Eesti"",""ee"",""EST"",""\ud83c\uddea\ud83c\uddea""]",http://www.wikidata.org/entity/Q3470316,http://www.wikidata.org/entity/Q191,Kanaküla,Estonia,59,169786,In what country is Kanaküla?,"[""Estonia"", ""Republic of Estonia"", ""Estland"", ""Eesti"", ""ee"", ""EST"", ""🇪🇪""]",2,2,"['Estonia', 'Republic of Estonia', 'Estland', 'Eesti', 'ee', 'EST', '🇪🇪']","village in Saarde Rural Municipality, Pärnu County, Estonia","In what country is Kanaküla, a village in Saarde Rural Municipality, Pärnu County?",True,"In what country is Kanaküla, a village in Saarde Rural Municipality?"
3373198,Breitenfelde,country,Germany,1450321,182,607728,[],"[""FRG"",""BRD"",""Bundesrepublik Deutschland"",""Federal Republic of Germany"",""de"",""Deutschland"",""GER"",""BR Deutschland"",""DE""]",http://www.wikidata.org/entity/Q479043,http://www.wikidata.org/entity/Q183,Breitenfelde (Amt),Germany,67,556493,In what country is Breitenfelde?,"[""Germany"", ""FRG"", ""BRD"", ""Bundesrepublik Deutschland"", ""Federal Republic of Germany"", ""de"", ""Deutschland"", ""GER"", ""BR Deutschland"", ""DE""]",4,3,"['Germany', 'FRG', 'BRD', 'Bundesrepublik Deutschland', 'Federal Republic of Germany', 'de', 'Deutschland', 'GER', 'BR Deutschland', 'DE']",amt in Germany,In what country is Breitenfelde?,False,Where is Breitenfelde located?
2096935,Konjsko Brdo,country,Croatia,918213,182,772386,[],"[""Republic of Croatia"",""HR"",""HRV"",""hr"",""\ud83c\udded\ud83c\uddf7"",""CRO""]",http://www.wikidata.org/entity/Q2727682,http://www.wikidata.org/entity/Q224,Konjsko Brdo,Croatia,90,209021,In what country is Konjsko Brdo?,"[""Croatia"", ""Republic of Croatia"", ""HR"", ""HRV"", ""hr"", ""🇭🇷"", ""CRO""]",3,3,"['Croatia', 'Republic of Croatia', 'HR', 'HRV', 'hr', '🇭🇷', 'CRO', 'Montenegro', 'Bosnia and Herzegovina']","settlement in the Municipality of Perušić, Lika-Senj County, Croatia","In what country is Konjsko Brdo, a settlement in the Municipality of Perušić, Lika-Senj County?",True,"In what country is Konjsko Brdo, a settlement in the Municipality of Perušić?"
14017,Le Moustoir,country,France,5904,182,230035,"[""Ar Vouster""]","[""fr"",""FR"",""R\u00e9publique fran\u00e7aise"",""La France"",""Republic of France"",""French Republic"",""FRA"",""the Hexagon""]",http://www.wikidata.org/entity/Q1014008,http://www.wikidata.org/entity/Q142,Le Moustoir,France,56,486947,In what country is Le Moustoir?,"[""France"", ""fr"", ""FR"", ""République française"", ""La France"", ""Republic of France"", ""French Republic"", ""FRA"", ""the Hexagon""]",3,3,"['France', 'fr', 'FR', 'République française', 'La France', 'Republic of France', 'French Republic', 'FRA', 'the Hexagon']","commune in Côtes-d'Armor, France","In what country is Le Moustoir, a commune in Côtes-d'Armor?",True,"In what country is Le Moustoir, a commune in Côtes-d'Armor located?"
5614172,Robinson,country,United States of America,2501557,182,988513,"[""Robinson, Minnesota""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q7352962,http://www.wikidata.org/entity/Q30,"Robinson, Minnesota",United States,79,1629691,In what country is Robinson?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",47,29,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'Canada', 'Australia', 'United Kingdom', 'South Africa', 'France', 'Czech Republic', 'Mexico']","unincorporated community in St. Louis County, Minnesota","In what country is Robinson, an unincorporated community in St. Louis County, Minnesota?",True,"In what country is Robinson, an unincorporated community in St. Louis County?"
3972968,Earl,country,United States of America,1736255,182,988513,"[""Earl, Wisconsin""]","[""the United States of America"",""America"",""U.S.A."",""USA"",""U.S."",""US"",""the US"",""the USA"",""US of A"",""the United States"",""U. S. A."",""U. S."",""the States"",""the U.S."",""'Merica"",""U.S"",""United States"",""'Murica""]",http://www.wikidata.org/entity/Q5325691,http://www.wikidata.org/entity/Q30,"Earl, Wisconsin",United States,83,1629691,In what country is Earl?,"[""United States of America"", ""the United States of America"", ""America"", ""U.S.A."", ""USA"", ""U.S."", ""US"", ""the US"", ""the USA"", ""US of A"", ""the United States"", ""U. S. A."", ""U. S."", ""the States"", ""the U.S."", ""'Merica"", ""U.S"", ""United States"", ""'Murica""]",18,11,"['United States of America', 'the United States of America', 'America', 'U.S.A.', 'USA', 'U.S.', 'US', 'the US', 'the USA', 'US of A', 'the United States', 'U. S. A.', 'U. S.', 'the States', 'the U.S.', ""'Merica"", 'U.S', 'United States', ""'Murica"", 'United Kingdom', 'Zimbabwe', 'Canada']","unincorporated community in Washburn County, Wisconsin","In what country is Earl, an unincorporated community in Washburn County, Wisconsin?",True,"In which country is Earl, an unincorporated community in Washburn County?"
2025404,Rozsochatec,country,Czech Republic,887722,182,744365,[],"[""CZR"",""cz"",""\u010cesko"",""\u010cesk\u00e1 republika"",""\u010cR"",""cze"",""CZE"",""Czechia""]",http://www.wikidata.org/entity/Q2635307,http://www.wikidata.org/entity/Q213,Rozsochatec,Czech Republic,53,271047,In what country is Rozsochatec?,"[""Czech Republic"", ""CZR"", ""cz"", ""Česko"", ""Česká republika"", ""ČR"", ""cze"", ""CZE"", ""Czechia""]",2,2,"['Czech Republic', 'CZR', 'cz', 'Česko', 'Česká republika', 'ČR', 'cze', 'CZE', 'Czechia']",village in Havlíčkův Brod District of Vysočina region,"In what country is Rozsochatec, a village in the Havlíčkův Brod District of the Vysočina region?",True,"In what country is Rozsochatec, a village in the Havlíčkův Brod District of the Vysočina region located?"
5933116,The Hunt,producer,Manoel de Oliveira,2661524,164,1300447,"[""Hunt""]","[""Manoel C\u00e2ndido Pinto de Oliveira""]",http://www.wikidata.org/entity/Q7741034,http://www.wikidata.org/entity/Q43264,The Hunt (1963 film),Manoel de Oliveira,87,3665,Who was the producer of The Hunt?,"[""Manoel de Oliveira"", ""Manoel Cândido Pinto de Oliveira""]",48,5,"['Manoel de Oliveira', 'Manoel Cândido Pinto de Oliveira', 'Damon Lindelof', 'Gianluca Curti', 'Sisse Graum Jørgensen', 'Thomas Vinterberg', 'Don Fleming', 'Morten Kaufmann', 'Santo Versace', 'Jason Blum']",1963 film by Manoel de Oliveira,"Who was the producer of the 1963 film, The Hunt?",True,"Who was responsible for producing the 1963 film, The Hunt?"
5907056,The Accused,producer,Mario Soffici,2648243,164,44376,"[""Los acusados"",""Accused""]",[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/*********,The Accused (1960 film),Mario Soffici,56,127,Who was the producer of The Accused?,"[""Mario Soffici""]",10,3,"['Mario Soffici', 'Hal B. Wallis', 'Sherry Lansing', 'Stanley R. Jaffe']",1960 film,"Who was the producer of the 1960 film, The Accused?",True,"Who was the producer of the 1960 film, *The Accused*?"
6002810,Today,producer,Carlos Santana,2695794,164,2829054,[],"[""Carlos Augusto Alves Santana""]",http://www.wikidata.org/entity/Q7812176,http://www.wikidata.org/entity/Q819016,Today (EP),Carlos Santana,75,79757,Who was the producer of Today?,"[""Carlos Santana"", ""Carlos Augusto Alves Santana""]",60,10,"['Carlos Santana', 'Carlos Augusto Alves Santana', 'Jerry Kennedy', 'Randy Sparks', 'Mark Wright', 'Everlast', 'Felton Jarvis', 'Oumar Sall', 'Gilles Sandoz', 'Seiji Kameda', 'Butch Vig', 'Mike Berniker', 'Reza Mirkarimi']",EP released by Everlast,"Who was the producer of Today, an EP released by Everlast?",True,"Who was the producer of Today, an EP by Everlast?"
5920930,The Deal,producer,Fernando Ayala,2655342,164,1011530,"[""Deal""]",[],http://www.wikidata.org/entity/Q7729377,http://www.wikidata.org/entity/Q3069435,The Deal (1983 film),Fernando Ayala,59,194,Who was the producer of The Deal?,"[""Fernando Ayala""]",27,2,"['Fernando Ayala', 'Michael Prupas']",1983 film by Fernando Ayala,"Who was the producer of the 1983 film, The Deal?",True,Who was the producer of the 1983 film?
2548111,On Tour,producer,Yann Tiersen,1100380,164,334219,[],"[""Yann Pierre Tiersen""]",http://www.wikidata.org/entity/Q3352164,http://www.wikidata.org/entity/Q157256,On Tour (Yann Tiersen album),Yann Tiersen,82,15203,Who was the producer of On Tour?,"[""Yann Tiersen"", ""Yann Pierre Tiersen""]",11,3,"['Yann Tiersen', 'Yann Pierre Tiersen', 'Laetitia Gonzalez', 'Yaël Fogiel', 'Vittorio Cecchi Gori', 'Gianni Minervini']",2006 live album by Yann Tiersen,"Who was the producer of the 2006 live album, On Tour?",True,Who was the producer of the 2006 live album?
2935301,The Trap,producer,Pat Powers,1257112,164,841564,"[""Trap""]",[],http://www.wikidata.org/entity/Q3989673,http://www.wikidata.org/entity/Q2483415,The Trap (1913 film),Pat Powers (businessman),94,2121,Who was the producer of The Trap?,"[""Pat Powers""]",56,6,"['Pat Powers', 'George H. Brown', 'Lene Bausager', 'Siegmund Lubin', 'Jalal Mehraban', 'Melvin Frank', 'Kirsty Bell', 'Jon Huertas']",1913 film,"Who was the producer of the 1913 film, The Trap?",True,Who was the producer of the 1913 film?
1398873,Ghost,producer,Hossein Shahabi,625858,164,368023,[],[],http://www.wikidata.org/entity/*********,http://www.wikidata.org/entity/*********,Ghost (1998 film),Hossein Shahabi,85,138,Who was the producer of Ghost?,"[""Hossein Shahabi""]",91,7,"['Hossein Shahabi', 'Noel Zancanella', 'Ryan Tedder', 'The Monsters and The Strangerz', 'Ariel Rechtshaid', 'Jon Bellion', 'Bharat Shah', 'Steven-Charles Jaffe', 'Howard W. Koch', 'Greg Kurstin', 'Bruce Joel Rubin', 'DJ Thomilla']",1997 film by Hossein Shahabi,"Who was the producer of the 1997 film, Ghost?",True,Who was the producer of the 1997 film?
4171110,From Now On,producer,Paulo Branco,1828117,164,873772,[],[],http://www.wikidata.org/entity/Q5505475,http://www.wikidata.org/entity/Q259593,From Now On (film),Paulo Branco,72,932,Who was the producer of From Now On?,"[""Paulo Branco""]",11,3,"['Paulo Branco', 'David Gamson', 'David Pack', 'Stephen Lipson']",2007 film by Catarina Ruivo,Who was the producer of the 2007 film From Now On by Catarina Ruivo?,True,Who was the producer of the 2007 film From Now On by Catarina Ruivo?
5937854,The Lie,producer,Independent Moving Pictures,2663894,164,2016625,"[""Lie""]","[""Independent Moving Pictures Company""]",http://www.wikidata.org/entity/Q7747104,http://www.wikidata.org/entity/Q60648,The Lie (1912 film),Independent Moving Pictures,95,1240,Who was the producer of The Lie?,"[""Independent Moving Pictures"", ""Independent Moving Pictures Company""]",29,5,"['Independent Moving Pictures', 'Independent Moving Pictures Company', 'Siegmund Lubin', 'Rolf Meyer', 'Jason Blum', 'Alix Madigan', 'Adolph Zukor']",1912 American film by King Baggot,"Who was the producer of the 1912 American film, The Lie, directed by King Baggot?",True,"Who was the producer of the 1912 American film, The Lie?"
3974408,Early Man,producer,Steve Roach,1736785,164,2397511,[],[],http://www.wikidata.org/entity/Q5326686,http://www.wikidata.org/entity/Q714470,Early Man (album),Steve Roach (musician),9,2118,Who was the producer of Early Man?,"[""Steve Roach""]",6,2,"['Steve Roach', 'Carla Shelley', 'Peter Lord', 'Nick Park', 'Richard Beek', 'David Sproxton']",album by Steve Roach,Who was the producer of the album Early Man?,True,Who was responsible for producing the album Early Man?
5386402,Party,producer,Gary Coleman,2395663,164,1033361,[],"[""Gary Wayne Coleman""]",http://www.wikidata.org/entity/Q7140881,http://www.wikidata.org/entity/Q313367,Party (1994 film),Gary Coleman,64,73501,Who was the producer of Party?,"[""Gary Coleman"", ""Gary Wayne Coleman""]",40,7,"['Gary Coleman', 'Gary Wayne Coleman', 'Tommy Boyce', 'Daikō Nagato', 'Paulo Branco', 'Johnnie J. Young', 'John Parish', 'T. Siva', 'Thom Panunzio', 'Jeff Bhasker']",1994 film by Eric Swelstad,"Who was the producer of Party, the 1994 film by Eric Swelstad?",True,Who was the producer of the 1994 film directed by Eric Swelstad?
1153600,Mother and Child,producer,Carl Froelich,510102,164,2847705,[],"[""Carl August Hugo Froelich""]",http://www.wikidata.org/entity/Q17026490,http://www.wikidata.org/entity/Q85038,Mother and Child (1924 film),Carl Froelich,63,373,Who was the producer of Mother and Child?,"[""Carl Froelich"", ""Carl August Hugo Froelich""]",526,3,"['Carl Froelich', 'Carl August Hugo Froelich', 'Alejandro González Iñárritu', 'Guido Bagier']",1924 German silent drama film directed by Carl Froelich,"Who was the producer of the 1924 German silent drama film, Mother and Child?",True,Who was the producer of the 1924 German silent drama film?
5563707,Revelations,producer,Tony Gayton,2479689,164,2702217,[],[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/********,Revelations (Hell on Wheels),Tony Gayton,86,374,Who was the producer of Revelations?,"[""Tony Gayton""]",51,9,"['Tony Gayton', 'Howie Tee', 'Piotr Wiwczarek', ""Brendan O'Brien"", 'Conny Plank', 'Gene', 'Hugh Jones', 'Lesli Linka Glatter', 'Tony Brown']",episode of Hell on Wheels (S1 E7),"Who was the producer of Revelations, an episode of Hell on Wheels (Season 1, Episode 7)?",True,"Who was the producer of the episode *Revelations* from *Hell on Wheels* (Season 1, Episode 7)?"
1074278,Home,producer,Carrie Akre,462009,164,1584234,[],[],http://www.wikidata.org/entity/Q16844319,http://www.wikidata.org/entity/Q5046201,Home (Carrie Akre album),Carrie Akre,67,644,Who was the producer of Home?,"[""Carrie Akre""]",281,37,"['Carrie Akre', 'Howard Benson', 'Chris Thomas', 'Drew Pearson', 'John Petrucci', 'The Chicks', 'Toby Wright', 'Teddy Riley', 'John Lydon', 'Mike Portnoy', 'Tucker Martine', 'Donald Ross Skinner', 'Bomb the Bass', 'Bob Montgomery', 'Denis Freyd', 'Donald ""Duck"" Dunn', 'Pdogg', 'Chris Jenkins', 'Tim Palmer', 'Ian Broudie', 'Nick Martinelli', 'Andy Wright', 'Clive Langer', 'Lloyd Maines', 'Takeshi Kobayashi', 'Owen Bradley', 'Luc Besson', 'Pavel Strnad', 'Sheryl Crow', 'Mitchell Froom', 'Brett Beavers', 'F.M. Einheit', 'Stewart Levine', 'Klaas Leyen', 'Topic', 'Gota Yashiki', 'Mick Hucknall', 'Sergey Selyanov', 'Jeannine Gagné', 'Motoki Matsuoka', 'Manfred Eicher']",Carrie Akre album,Who was the producer of the album Home?,True,Who was responsible for producing the album Home?
5959348,The Test,producer,Harry S. Webb,2675053,164,1907606,"[""Test""]",[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/********,The Test (1935 film),Harry S. Webb,88,110,Who was the producer of The Test?,"[""Harry S. Webb""]",52,4,"['Harry S. Webb', 'William Nicholas Selig', 'David Horsley']",1935 film by Bernard B. Ray,"Who was the producer of the 1935 film, The Test?",True,"Who was involved in the production of the 1935 film, The Test?"
5745791,Shine,producer,Brendan O'Brien,2565101,164,2363770,[],[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q707504,Shine (Trey Anastasio song),Brendan O'Brien (record producer),53,3756,Who was the producer of Shine?,"[""Brendan O'Brien""]",121,24,"[""Brendan O'Brien"", 'Dann Huff', 'Jerry Harrison', 'John Shanks', 'Joni Mitchell', 'Mark Ralph', 'Tony Cohen', 'Luna Sea', 'Eric Kwok', 'Daniel Lanois', 'Jane Scott', 'Nicholas Tse', 'Pharrell Williams', 'Kevin Moore', 'Larry Klein', 'Heiko Maile', 'Jeff Lorber', 'Cyndi Lauper', 'Steve Lillywhite', 'Lee Soo-man', 'Swizz Beatz', 'Years & Years', 'Tony Platt', 'D Adja productions']",song by Trey Anastasio,"Who was the producer of Shine, a song by Trey Anastasio?",True,Who was the producer of the song Shine by Trey Anastasio?
807577,In the Family,producer,Paulo Porto,344794,164,17374,[],[],http://www.wikidata.org/entity/Q1592219,http://www.wikidata.org/entity/Q10346753,In the Family (1971 film),Paulo Porto,67,74,Who was the producer of In the Family?,"[""Paulo Porto""]",9,2,"['Paulo Porto', 'Gordon Quinn', 'Roberto Farias']",1971 film by Paulo Porto,"Who was the producer of the 1971 film, In the Family?",True,"Who was involved in producing the 1971 film, *In the Family*?"
801960,The Easiest Way,producer,Clara Kimball Young,342347,164,959969,"[""Easiest Way""]","[""Clara Kimball"",""Edith Matilda Clara Kimball""]",http://www.wikidata.org/entity/Q15875980,http://www.wikidata.org/entity/Q289284,The Easiest Way (1917 film),Clara Kimball Young,78,1392,Who was the producer of The Easiest Way?,"[""Clara Kimball Young"", ""Clara Kimball"", ""Edith Matilda Clara Kimball""]",3,2,"['Clara Kimball Young', 'Clara Kimball', 'Edith Matilda Clara Kimball', 'Hunt Stromberg']",1917 film by Albert Capellani,"Who was the producer of the 1917 film, The Easiest Way?",True,"Who was involved in producing the 1917 film, *The Easiest Way*?"
5745770,Shine,producer,Luna Sea,2565092,164,839260,[],"[""Lunacy""]",http://www.wikidata.org/entity/Q7497340,http://www.wikidata.org/entity/Q24760,Shine (Luna Sea song),Luna Sea,56,2942,Who was the producer of Shine?,"[""Luna Sea"", ""Lunacy""]",121,24,"['Luna Sea', 'Lunacy', 'Dann Huff', 'Jerry Harrison', 'John Shanks', 'Joni Mitchell', 'Mark Ralph', 'Tony Cohen', 'Eric Kwok', 'Daniel Lanois', 'Jane Scott', 'Nicholas Tse', 'Pharrell Williams', ""Brendan O'Brien"", 'Kevin Moore', 'Larry Klein', 'Heiko Maile', 'Jeff Lorber', 'Cyndi Lauper', 'Steve Lillywhite', 'Lee Soo-man', 'Swizz Beatz', 'Years & Years', 'Tony Platt', 'D Adja productions']",song by Luna Sea,"Who was the producer of the song, Shine?",True,"Who was responsible for producing the song, Shine?"
5955665,The Sisters,director,Christy Cabanne,2673105,526,700769,"[""Sisters""]","[""William Christy Cabanne""]",http://www.wikidata.org/entity/Q7764530,http://www.wikidata.org/entity/Q1993804,The Sisters (1914 film),Christy Cabanne,93,891,Who was the director of The Sisters?,"[""Christy Cabanne"", ""William Christy Cabanne""]",130,7,"['Christy Cabanne', 'William Christy Cabanne', 'Anatole Litvak', 'Bojana Maljević', 'Grigori Roshal', 'Roberto Malenotti', 'Arthur Allan Seidelman', 'Irving Rapper', 'Christophe Cazenove']",1914 film by Christy Cabanne,"Who was the director of the 1914 film, The Sisters?",True,Who was the director of the 1914 film?
5987762,Those Who Love,director,Manning Haynes,2688121,526,2241098,[],"[""Horace Manning Haynes""]",http://www.wikidata.org/entity/Q7796877,http://www.wikidata.org/entity/Q6750864,Those Who Love (1929 film),Manning Haynes,72,97,Who was the director of Those Who Love?,"[""Manning Haynes"", ""Horace Manning Haynes""]",5,3,"['Manning Haynes', 'Horace Manning Haynes', 'P.J. Ramster', 'Isabel Coixet', 'Paulette McDonagh']",1929 film by Manning Haynes,"Who was the director of the 1929 film, Those Who Love?",True,"Who directed the 1929 film, *Those Who Love*?"
5931107,The Happy Family,director,Maclean Rogers,2660480,526,2230809,"[""Happy Family""]",[],http://www.wikidata.org/entity/Q7738809,http://www.wikidata.org/entity/Q6724633,The Happy Family (1936 film),Maclean Rogers,59,185,Who was the director of The Happy Family?,"[""Maclean Rogers""]",12,2,"['Maclean Rogers', 'Muriel Box']",1936 film directed by Maclean Rogers,"Who was the director of the 1936 film, The Happy Family?",True,"Who was the director of the 1936 film, The Happy Family?"
5927641,The Gamble,director,Tom Ricketts,2658768,526,1154752,"[""Gamble""]",[],http://www.wikidata.org/entity/Q7735669,http://www.wikidata.org/entity/Q3530860,The Gamble (1916 film),Tom Ricketts,61,979,Who was the director of The Gamble?,"[""Tom Ricketts""]",11,2,"['Tom Ricketts', 'Carlo Vanzina', 'Trong Ninh Luu']",1916 short film by Tom Ricketts,"Who was the director of the 1916 short film, The Gamble?",True,Who was the director of the 1916 short film?
5719217,Senior Year,director,Jerrold Tarog,2552352,526,2056409,[],"[""Jerrold Viacrucis Tarog""]",http://www.wikidata.org/entity/Q7450723,http://www.wikidata.org/entity/Q6183129,Senior Year (film),Jerrold Tarog,71,2638,Who was the director of Senior Year?,"[""Jerrold Tarog"", ""Jerrold Viacrucis Tarog""]",4,2,"['Jerrold Tarog', 'Jerrold Viacrucis Tarog', 'Alex Hardcastle']",2010 film by Jerrold Tarog,"Who was the director of the 2010 film, Senior Year?",True,Who was the director of the 2010 film?
3060498,Victory,director,Vsevolod Pudovkin,1308059,526,1835148,[],"[""Vsevolod Illarionovich Pudovkin"",""Wsewolod Illarionowitsch Pudowkin""]",http://www.wikidata.org/entity/Q4366190,http://www.wikidata.org/entity/Q55195,Victory (1938 film),Vsevolod Pudovkin,64,1976,Who was the director of Victory?,"[""Mikhail Doller"", ""Mikhail Ivanovich Doller"", ""Vsevolod Pudovkin"", ""Vsevolod Illarionovich Pudovkin"", ""Wsewolod Illarionowitsch Pudowkin""]",119,11,"['Mikhail Doller', 'Mikhail Ivanovich Doller', 'Vsevolod Pudovkin', 'Vsevolod Illarionovich Pudovkin', 'Wsewolod Illarionowitsch Pudowkin', 'M. A. Wetherell', 'Eliran Peled', 'Yevgeny Matveyev', 'John Cromwell', 'Ajit Pal Mangat', 'Alex Zakrzewski', 'Nanda Kishore', 'Maurice Tourneur', 'Mark Peploe', 'Park Beom-su']","1938 film by Vsevolod Pudovkin, Mikhail Doller","Who was the director of the 1938 film, Victory?",True,Who was the director of the 1938 film?
5453554,Pilot,director,Robby Benson,2425255,526,2676136,[],"[""Robin David Segal""]",http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q777128,Pilot (Sabrina the Teenage Witch),Robby Benson,16,27678,Who was the director of Pilot?,"[""Robby Benson"", ""Robin David Segal""]",475,226,"['Robby Benson', 'Robin David Segal', 'Ken Kwapis', 'David Wellington', 'Charles McDougall', 'Joe Russo', 'Aaron Augenblick', 'Albert Calleros', 'Julie Anne Robinson', 'Len Wiseman', 'Phillip Noyce', 'Jace Alexander', 'Anthony Russo', 'Jason Ensler', 'Michael Cuesta', 'Lucy A. Snyder', 'Michael Dante DiMartino', 'Richard Pearce', 'David Nutter', ""Peter O'Fallon"", 'Bryan Singer', 'Brett Ratner', 'Mark Mylod', 'Peter Levin', 'Robert Mandel', 'Marc Buckland', 'Todd Holland', 'Peter Horton', 'Michael Mann', 'David Slade', 'Peter Berg', 'Michael Fresco', 'Jon Turteltaub', 'Howard Deutch', 'Doug Liman', 'Greg Daniels', 'Kevin Bray', 'John Tracy', 'Bill L. Norton', 'Steve Shill', 'Michael Engler', 'Ron Hughart', 'Allen Coulter', 'Gene Reynolds', 'Michael Lembeck', 'Alan Ball', 'Yves Simoneau', 'Thomas Schlamme', 'Michael M. Robin', 'James Burrows', 'J. J. Abrams', 'Declan Lowney', 'Jason Winer', 'Lesli Linka Glatter', 'Mick Jackson', 'Jake Kasdan', 'David Semel', 'Wes Archer', 'Robert Schwentke', 'Ryan Murphy', 'Bill Lawrence', 'Bryan Konietzko', 'Adam Bernstein', 'Michael Dinner', 'David Lynch', 'Anthony Lioi', 'Pamela Fryman', 'Mark Piznarski', 'Alex Graves', 'Greg Yaitanes', 'Dwayne Carey-Hill', 'Danny Cannon', 'Richard Shepard', 'Andy Ackerman', 'Gregory Hoblit', 'Stephen Hopkins', 'Jay Sandrich', 'Bharat Nalluri', 'John Requa', 'Linda Day', 'Glenn Ficarra', 'Michael Lessac', 'Phil Lord', 'Michael Mayer', 'Joss Whedon', 'Paul McGuigan', 'Patty Jenkins', 'John Madden', 'Jamie Travis', 'Brad Anderson', 'Bryan Gordon', 'Seth Gordon', 'Virgil L. Fabian', 'Lee Daniels', 'Steve Hoefer', 'Alan Taylor', 'Glen Winter', 'Chris Koch', 'Scott Winant', 'John Gray', 'Rob Bowman', 'Francis Lawrence', 'Marc Webb', 'Seth Rogen', 'Vince Gilligan', 'Joe Carnahan', 'Jamal Shoorje', 'Marcos Siega', 'Bronwen Hughes', 'Michelle MacLaren', 'Clark Johnson', 'Nelson McCormick', 'Niels Arden Oplev', 'Jon Favreau', 'Justin Roiland', 'Chris Miller', 'Rich Moore', 'Louis C.K.', 'Scott Stewart', ""Gavin O'Connor"", 'Liz Friedlander', 'Lee Toland Krieger', 'Andy Cadiff', 'Darren Star', 'McG', 'Evan Goldberg', 'Lena Dunham', 'Neil Marshall', 'Michael Offer', 'Adam Davidson', 'Don Scardino', 'David Frankel', 'Amy Sherman-Palladino', 'Gary Fleder', 'Elizabeth Allen', 'Michael Showalter', 'Jesse Peretz', 'Bill Duke', 'Alan Poul', 'Jason Reitman', 'Tom Marshall', 'James Griffiths', 'Julie Plec', 'R. J. Cutler', 'Ruben Fleischer', 'Peter Atencio', 'Phil Traill', 'Craig Brewer', 'Neal Marlens', 'Robert King', 'Ted Wass', 'Dean Parisot', 'Vivienne Medrano', 'Bradley Buecker', 'Steve Zuckerman', 'Amy York Rubin', 'Larry Teng', 'Allan Arkush', 'Martin Scorsese', 'Morten Tyldum', 'James Frawley', 'Karyn Kusama', 'Mike White', 'Paul Krasny', 'Taika Waititi', 'Neil Burger', 'Randall Einhorn', 'Jeffrey Nachmanoff', 'Andrew D. Weyman', 'Rodrigo García Márquez', 'Michael W. Watkins', 'Paul Weitz', 'Kim Han Gyul', 'Rob Thomas']","pilot episode of Sabrina, the Teenage Witch directed by Robby Benson","Who was the director of the pilot episode of Sabrina, the Teenage Witch?",True,"Who was the director of the pilot episode of *Sabrina, the Teenage Witch*?"
1495476,Messiah,director,William Klein,667044,526,2440737,[],[],http://www.wikidata.org/entity/*********,http://www.wikidata.org/entity/Q723892,Messiah (1999 film),William Klein (photographer),98,2241,Who was the director of Messiah?,"[""William Klein""]",29,4,"['William Klein', 'James McTeigue', 'Udi Kagan', 'Shūsuke Kaneko']",1999 film by William Klein,"Who was the director of the 1999 film, Messiah?",True,Who was the director of the 1999 film?
4490656,Homecoming,director,Todd Holland,1978689,526,41436,[],[],http://www.wikidata.org/entity/Q5889367,http://www.wikidata.org/entity/Q1071497,Homecoming (Miss Guided),Todd Holland,68,1769,Who was the director of Homecoming?,"[""Todd Holland""]",132,35,"['Todd Holland', 'Kevin Hooks', 'Adriyanto Dewo', 'Suvi West', 'Lee Yong-min', 'James Frawley', 'Joe May', ""Trent O'Donnell"", 'Joshua Butler', 'Patrick Norris', 'Mervyn LeRoy', 'Robin Dunne', 'Ken Whittingham', 'Greg Beeman', 'Larry Teng', 'Jonathan Kaplan', 'David Greenwalt', 'James Roday Rodriguez', 'Eagle Egilsson', 'Anssi Kömi', 'Gail Mancuso', 'Joe Dante', 'Gilbert M. Shilton', 'Catherine Corsini', 'Helen Shaver', 'Mark Jean', 'Jeannot Szwarc', 'Alec Smight', 'Katsumi Nishikawa', 'Yim Ho', 'Mika Kaurismäki', 'Sam Esmail', 'Bradley Buecker', 'Leslie Libman', 'Morgan J. Freeman', 'Eriq La Salle', 'Martin Wood']",episode of Miss Guided,"Who was the director of Homecoming, an episode of Miss Guided?",True,"Who was the director of the episode ""Homecoming"" from Miss Guided?"
2769262,College,director,Franco Castellano,1188965,526,239896,[],[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/********,College (1984 film),Franco Castellano,90,173,Who was the director of College?,"[""Franco Castellano"", ""Giuseppe Moccia""]",44,6,"['Franco Castellano', 'Giuseppe Moccia', 'Buster Keaton', 'Allen Coulter', 'James W. Horne', 'Don Scardino', 'Deb Hagan', 'Federico Moccia']","1984 film by Giuseppe Moccia, Franco Castellano","Who was the director of the 1984 film, College?",True,Who was the director of the 1984 film?
5960645,The Tree,director,Todd Field,2675690,526,2224301,"[""Tree""]","[""William Todd Field""]",http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q670296,The Tree (1993 film),Todd Field,82,10431,Who was the director of The Tree?,"[""Todd Field"", ""William Todd Field""]",32,7,"['Todd Field', 'William Todd Field', 'Robert Guenette', 'Sonja Prosenc', 'Otar Andronikashvili', 'Daisy Chan', 'Julie Bertuccelli', 'Abby Berlin']",1993 film by Todd Field,"Who was the director of the 1993 film, The Tree?",True,Who was the director of the 1993 film?
1530395,Driven,director,Maurice Elvey,683777,526,1902360,[],[],http://www.wikidata.org/entity/*********,http://www.wikidata.org/entity/Q565510,Driven (1916 film),Maurice Elvey,76,526,Who was the director of Driven?,"[""Maurice Elvey""]",29,8,"['Maurice Elvey', 'Eagle Egilsson', 'John Polson', 'Renny Harlin', 'Nick Hamm', 'Dennis Smith', 'Alan Taylor', 'Charles Brabin']",1916 film by Maurice Elvey,"Who was the director of the 1916 film, Driven?",True,Who was the director of the 1916 film?
1051711,Freedom,director,Liz Friedlander,450341,526,1323743,[],[],http://www.wikidata.org/entity/*********,http://www.wikidata.org/entity/********,Freedom (The Following),Liz Friedlander,63,1622,Who was the director of Freedom?,"[""Liz Friedlander""]",149,14,"['Liz Friedlander', 'Peter Cousens', 'Tudor Giurgiu', 'Richard Mulindwa', 'Yoko Ono', 'Lisandro Alonso', 'Craig Ross, Jr.', 'Michael Schultz', 'Thampi Kannanthanam', 'Muriel Tramis', 'Scott Hicks', 'Jan Speckenbach', 'Šarūnas Bartas', 'Chico Ejiro']",episode of The Following (S2 E11),"Who was the director of Freedom, an episode of The Following (Season 2, Episode 11)?",True,"Who was the director of Freedom, an episode of The Following?"
2987359,Balance,director,Lyudmil Kirkov,1279313,526,2227006,[],[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/********,Balance (1983 film),Lyudmil Kirkov,75,147,Who was the director of Balance?,"[""Lyudmil Kirkov""]",55,2,"['Lyudmil Kirkov', 'Wolfgang Lauenstein', 'Christoph Lauenstein']",1983 Bulgarian drama film directed by Lyudmil Kirkov,"Who was the director of the 1983 Bulgarian drama film, Balance?",True,"Who directed the 1983 Bulgarian drama film, Balance?"
4088684,Faith,director,James Kirkwood,1789003,526,207922,"[""The Virtuous Outcast""]","[""James Kirkwood, Sr.""]",http://www.wikidata.org/entity/Q5431132,http://www.wikidata.org/entity/Q1371051,Faith (1916 film),James Kirkwood Sr.,79,666,Who was the director of Faith?,"[""James Kirkwood"", ""James Kirkwood, Sr.""]",149,16,"['James Kirkwood', 'James Kirkwood, Sr.', 'Allan Kroeker', 'Arvin Brown', 'Howard M. Mitchell', 'Charles Swickard', 'Fred Toye', 'Michael Nankin', 'Will Waring', 'Ted Mather', 'Mikaï Flageole', 'Kim Jong-hak', 'Donna Deitch', 'Jonathan Kaplan', 'John Strickland', 'Ken Olin', 'Rose Troche']",1916 film directed by James Kirkwood,"Who was the director of the 1916 film, Faith?",True,Who was the director of the 1916 film?
309956,On the Run,director,Ernest Morris,125573,526,1200811,[],[],http://www.wikidata.org/entity/Q12126739,http://www.wikidata.org/entity/Q3732130,On the Run (1958 film),Ernest Morris,75,108,Who was the director of On the Run?,"[""Ernest Morris""]",25,7,"['Ernest Morris', 'Pat Jackson', 'Alfred Cheung', 'Mende Brown', 'Claudia Gerini', 'Bruno de Almeida', 'Yana Gorskaya']",1958 film by Ernest Morris,"Who was the director of the 1958 film, On the Run?",True,Who was the director of the 1958 film?
6094861,Variety,director,Adrian Brunel,2742636,526,1199824,[],"[""Adrian Hope Brunel""]",http://www.wikidata.org/entity/Q7915873,http://www.wikidata.org/entity/Q372692,Variety (1935 film),Adrian Brunel,83,322,Who was the director of Variety?,"[""Adrian Brunel"", ""Adrian Hope Brunel""]",17,5,"['Adrian Brunel', 'Adrian Hope Brunel', 'Ewald André Dupont', 'Bette Gordon', 'Juan Antonio Bardem', 'Nicolas Farkas']",1935 British film directed by Adrian Brunel,"Who was the director of the 1935 British film, Variety?",True,Who was the director of the 1935 British film?
5944707,The Night Riders,director,Alexander Butler,2667437,526,242797,"[""Night Riders""]",[],http://www.wikidata.org/entity/Q7754102,http://www.wikidata.org/entity/Q1449546,The Night Riders (1920 film),Alexander Butler,89,123,Who was the director of The Night Riders?,"[""Alexander Butler""]",6,3,"['Alexander Butler', 'Jacques Jaccard', 'George Sherman']",1920 film by Alexander Butler,"Who was the director of the 1920 film, The Night Riders?",True,"Who was the director of the 1920 film, The Night Riders?"
1352311,You and I,director,Wolfgang Liebeneiner,602719,526,2243947,[],[],http://www.wikidata.org/entity/Q18208518,http://www.wikidata.org/entity/Q67568,You and I (1938 film),Wolfgang Liebeneiner,96,437,Who was the director of You and I?,"[""Wolfgang Liebeneiner""]",24,2,"['Wolfgang Liebeneiner', 'Roland Joffé']",1938 film directed by Wolfgang Liebeneiner,"Who was the director of the 1938 film, You and I?",True,Who was the director of the 1938 film?
5939352,The Love Nest,director,Thomas Bentley,2664654,526,2682785,"[""Love Nest""]",[],http://www.wikidata.org/entity/Q7748946,http://www.wikidata.org/entity/Q7787584,The Love Nest (1933 film),Thomas Bentley,92,222,Who was the director of The Love Nest?,"[""Thomas Bentley""]",6,4,"['Thomas Bentley', 'Helmy Rafla', 'Buster Keaton', 'Rudolf Walther-Fein', 'Edward F. Cline']",1933 film by Thomas Bentley,"Who was the director of the 1933 film, The Love Nest?",True,"Who directed the 1933 film, The Love Nest?"
5360069,Out,director,Lionel Rogosin,2382437,526,184875,[],[],http://www.wikidata.org/entity/Q7111421,http://www.wikidata.org/entity/Q1344057,Out (1957 film),Lionel Rogosin,68,496,Who was the director of Out?,"[""Lionel Rogosin""]",26,8,"['Lionel Rogosin', 'György Kristóf', 'Hideyuki Hirayama', 'Steven Clay Hunter', 'Sarah Pia Anderson', 'Roee Rosen', 'Paris Barclay', 'Daniel Dencik']",1957 short film directed by Lionel Rogosin,Who was the director of the 1957 short film Out?,True,Who was the director of the 1957 short film?
2934585,The Physician,director,Georg Jacoby,1256866,526,2389071,"[""Physician""]",[],http://www.wikidata.org/entity/Q3988718,http://www.wikidata.org/entity/Q71273,The Physician (1928 film),Georg Jacoby,73,361,Who was the director of The Physician?,"[""Georg Jacoby""]",13,2,"['Georg Jacoby', 'Philipp Stölzl', 'David Lean']","1928 film by Georg Jacoby, David Lean","Who was the director of the 1928 film, The Physician?",True,Who was the director of the 1928 film?
801964,The Easiest Way,director,Albert Capellani,342347,526,715932,"[""Easiest Way""]",[],http://www.wikidata.org/entity/Q15875980,http://www.wikidata.org/entity/Q2037315,The Easiest Way (1917 film),Albert Capellani,78,256,Who was the director of The Easiest Way?,"[""Albert Capellani""]",3,2,"['Albert Capellani', 'Jack Conway']",1917 film by Albert Capellani,"Who was the director of the 1917 film, The Easiest Way?",True,"Who was the director of the 1917 film, *The Easiest Way*?"
2043035,The Betrayed,director,Frans Weisz,894900,526,842293,"[""Betrayed""]",[],http://www.wikidata.org/entity/Q2658829,http://www.wikidata.org/entity/Q2485055,The Betrayed (1993 film),Frans Weisz,89,177,Who was the director of The Betrayed?,"[""Frans Weisz""]",3,2,"['Frans Weisz', 'Amanda Gusack']",1993 film by Frans Weisz,"Who was the director of the 1993 film, The Betrayed?",True,Who was the director of the 1993 film?
1425159,Sacrifice,director,Frank Reicher,637394,526,163321,[],[],http://www.wikidata.org/entity/Q18709064,http://www.wikidata.org/entity/Q1307857,Sacrifice (1917 film),Frank Reicher,82,1872,Who was the director of Sacrifice?,"[""Frank Reicher""]",99,23,"['Frank Reicher', 'Paul Holahan', 'Chen Kaige', 'John Hyams', 'Michael Patrick Jann', 'Adam Davidson', 'Damian Lee', 'David Straiton', 'Philip Sgriccia', 'Stefan Schwartz', 'Romain Gavras', 'Kevin Fair', 'Hardee Kirkland', 'Ken Girotti', 'Mark L. Lester', 'Nick Copus', 'Lesli Linka Glatter', 'David Barrett', 'George Konstantin', 'Randall Einhorn', 'Peter A. Dowling', 'Reynaldo Villalobos', 'Michael Cohn']",1917 film by Frank Reicher,"Who was the director of the 1917 film, Sacrifice?",True,"Who was responsible for directing the 1917 film, Sacrifice?"
4156313,Women Who Work,director,Manuel Romero,1821982,526,2242028,"[""Mujeres que trabajan""]","[""Manuel Romeo""]",http://www.wikidata.org/entity/Q5494934,http://www.wikidata.org/entity/Q6752853,Women Who Work (1938 film),Manuel Romero (director),94,204,Who was the director of Women Who Work?,"[""Manuel Romero"", ""Manuel Romeo""]",4,2,"['Manuel Romero', 'Manuel Romeo', 'Julio Bracho']",1938 film by Manuel Romero,"Who was the director of the 1938 film, Women Who Work?",True,"Who directed the 1938 film, Women Who Work?"
2858626,The Barrier,director,Lesley Selander,1225031,526,583993,"[""Barrier""]",[],http://www.wikidata.org/entity/Q3822600,http://www.wikidata.org/entity/Q1804597,The Barrier (1937 film),Lesley Selander,98,653,Who was the director of The Barrier?,"[""Lesley Selander""]",16,6,"['Lesley Selander', 'Edgar Lewis', 'George W. Hill', 'William J. Bauman', 'Bassam Al-Thawadi', 'Christo Christov']",1937 film by Lesley Selander,"Who was the director of the 1937 film, The Barrier?",True,Who was the director of the 1937 film?
4202549,Genius,director,Babar Ahmad,1843036,526,1475297,[],[],http://www.wikidata.org/entity/Q5533253,http://www.wikidata.org/entity/Q4837509,Genius (2003 film),Babar Ahmad,54,1245,Who was the director of Genius?,"[""Babar Ahmad""]",45,10,"['Babar Ahmad', 'Ohmkar', 'Greg Yaitanes', 'Ron Howard', 'Kevin Hooks', 'Anil Sharma', 'Michael Grandage', 'Kenneth Biller', 'Gregory Markopoulos', 'Rod Daniel', 'Suseenthiran', 'Babar Ahmed', 'Viktor Sergeev', 'Jace Alexander']",2003 film directed by Babar Ahmad,"Who was the director of the 2003 film, Genius?",True,Who was the director of the 2003 film?
5141436,Men and Women,director,William Churchill deMille,2274094,526,1330559,[],"[""William Churchill de Mille"",""William C. De Mille"",""William deMille""]",http://www.wikidata.org/entity/Q6816235,http://www.wikidata.org/entity/Q454464,Men and Women (1925 film),William C. deMille,87,2167,Who was the director of Men and Women?,"[""William Churchill deMille"", ""William Churchill de Mille"", ""William C. De Mille"", ""William deMille""]",8,4,"['William Churchill deMille', 'William Churchill de Mille', 'William C. De Mille', 'William deMille', 'James Kirkwood', 'Liu Bingjian', 'Walter Hugo Khouri', 'D. W. Griffith']",1925 film by William C. deMille,"Who was the director of the 1925 film, Men and Women?",True,"Who directed the 1925 film, Men and Women?"
2925643,Sold,director,Hugh Ford,1253249,526,2928727,[],[],http://www.wikidata.org/entity/Q3964221,http://www.wikidata.org/entity/Q973200,Sold (1915 film),Hugh Ford (director),97,125,Who was the director of Sold?,"[""Hugh Ford""]",11,2,"['Hugh Ford', 'Jeffrey D. Brown', 'Edwin Stanton Porter']",1915 American silent drama film,"Who was the director of the 1915 American silent drama film, Sold?",True,Who was the director of the 1915 American silent drama film?
5953033,The Saint,director,Abdur Rashid Kardar,2671796,526,1045904,"[""Saint""]","[""A. R. Kardar""]",http://www.wikidata.org/entity/Q7762049,http://www.wikidata.org/entity/Q317425,The Saint (1941 film),Abdur Rashid Kardar,70,962,Who was the director of The Saint?,"[""Abdur Rashid Kardar"", ""A. R. Kardar""]",22,5,"['Abdur Rashid Kardar', 'A. R. Kardar', 'Phillip Noyce', 'Simon West', 'Peter Yates', 'Ernie Barbarash', 'Andrius Blaževičius']",1941 Bollywood film directed by Abdur Rashid Kardar,"Who was the director of the 1941 Bollywood film, The Saint?",True,Who was the director of the 1941 Bollywood film?
5948000,The Pioneers,director,Franklyn Barrett,2669187,526,1820638,"[""Pioneers""]","[""Walter Franklyn Barrett""]",http://www.wikidata.org/entity/Q7757231,http://www.wikidata.org/entity/Q5492176,The Pioneers (1916 film),Franklyn Barrett,83,113,Who was the director of The Pioneers?,"[""Franklyn Barrett"", ""Walter Franklyn Barrett""]",18,5,"['Franklyn Barrett', 'Walter Franklyn Barrett', 'Tim Burstall', 'Raymond Longford', 'Wallace McCutcheon, Sr.', 'Albert Herman']",1916 film by Franklyn Barrett,"Who was the director of the 1916 film, The Pioneers?",True,Who was the director of the 1916 film?
5937083,The Last Word,director,Binka Zhelyazkova,2663513,526,964381,"[""Last Word""]",[],http://www.wikidata.org/entity/Q7746198,http://www.wikidata.org/entity/Q2903953,The Last Word (1973 film),Binka Zhelyazkova,86,263,Who was the director of The Last Word?,"[""Binka Zhelyazkova""]",30,9,"['Binka Zhelyazkova', 'Geoffrey Haley', 'Wasfi Darwish', 'Michael M. Robin', 'Mark Pellington', 'Hossein Shahabi', 'Roy Boulting', 'Tony Spiridakis', 'Robert van Ackeren']",1973 film by Binka Zhelyazkova,"Who was the director of the 1973 film, The Last Word?",True,Who was the director of the 1973 film?
1385900,Escape,director,Wyllis Cooper,619903,526,2803831,[],"[""Willis Cooper""]",http://www.wikidata.org/entity/Q18391958,http://www.wikidata.org/entity/Q8039957,Escape (1950 TV series),Wyllis Cooper,92,462,Who was the director of Escape?,"[""Wyllis Cooper"", ""Willis Cooper""]",94,17,"['Wyllis Cooper', 'Willis Cooper', 'Kirsten Winter', 'Paul Emami', 'Mervyn LeRoy', 'Lee Jong-pil', 'Choi In-hyeon', 'Basil Dean', 'Yegor Konchalovsky', 'Ralph Hemecker', 'Rodrigo Cortés', 'Suren Tadevosyan', 'Joseph L. Mankiewicz', 'Carl Froelich', 'Kevin Fair', 'Roar Uthaug']",dramatic anthology television series from the United States,"Who was the director of Escape, the dramatic anthology television series from the United States?",True,"Who was the director of Escape, the television series from the United States?"
1269483,Emergency Landing,director,Arne Skouen,565478,526,897529,[],[],http://www.wikidata.org/entity/Q1765344,http://www.wikidata.org/entity/Q2666954,Emergency Landing (1952 film),Arne Skouen,88,235,Who was the director of Emergency Landing?,"[""Arne Skouen""]",3,2,"['Arne Skouen', 'William Beaudine']",1952 Norwegian film directed by Arne Skouen,"Who was the director of the 1952 Norwegian film, Emergency Landing?",True,Who was the director of the 1952 Norwegian film?
5453479,Pilot,director,Peter Horton,2425228,526,160257,"[""Dirty Sexy Money pilot""]",[],http://www.wikidata.org/entity/Q7194339,http://www.wikidata.org/entity/Q130034,Pilot (Dirty Sexy Money),Peter Horton,86,26199,Who was the director of Pilot?,"[""Peter Horton""]",475,226,"['Peter Horton', 'Ken Kwapis', 'David Wellington', 'Charles McDougall', 'Joe Russo', 'Aaron Augenblick', 'Albert Calleros', 'Julie Anne Robinson', 'Len Wiseman', 'Phillip Noyce', 'Jace Alexander', 'Anthony Russo', 'Jason Ensler', 'Michael Cuesta', 'Lucy A. Snyder', 'Michael Dante DiMartino', 'Richard Pearce', 'David Nutter', ""Peter O'Fallon"", 'Bryan Singer', 'Brett Ratner', 'Mark Mylod', 'Peter Levin', 'Robert Mandel', 'Marc Buckland', 'Todd Holland', 'Michael Mann', 'David Slade', 'Peter Berg', 'Michael Fresco', 'Jon Turteltaub', 'Howard Deutch', 'Doug Liman', 'Greg Daniels', 'Kevin Bray', 'John Tracy', 'Bill L. Norton', 'Steve Shill', 'Michael Engler', 'Ron Hughart', 'Allen Coulter', 'Gene Reynolds', 'Michael Lembeck', 'Alan Ball', 'Yves Simoneau', 'Thomas Schlamme', 'Michael M. Robin', 'James Burrows', 'J. J. Abrams', 'Declan Lowney', 'Jason Winer', 'Lesli Linka Glatter', 'Mick Jackson', 'Jake Kasdan', 'David Semel', 'Wes Archer', 'Robert Schwentke', 'Ryan Murphy', 'Bill Lawrence', 'Bryan Konietzko', 'Adam Bernstein', 'Michael Dinner', 'David Lynch', 'Anthony Lioi', 'Pamela Fryman', 'Mark Piznarski', 'Alex Graves', 'Greg Yaitanes', 'Dwayne Carey-Hill', 'Danny Cannon', 'Richard Shepard', 'Andy Ackerman', 'Gregory Hoblit', 'Robby Benson', 'Stephen Hopkins', 'Jay Sandrich', 'Bharat Nalluri', 'John Requa', 'Linda Day', 'Glenn Ficarra', 'Michael Lessac', 'Phil Lord', 'Michael Mayer', 'Joss Whedon', 'Paul McGuigan', 'Patty Jenkins', 'John Madden', 'Jamie Travis', 'Brad Anderson', 'Bryan Gordon', 'Seth Gordon', 'Virgil L. Fabian', 'Lee Daniels', 'Steve Hoefer', 'Alan Taylor', 'Glen Winter', 'Chris Koch', 'Scott Winant', 'John Gray', 'Rob Bowman', 'Francis Lawrence', 'Marc Webb', 'Seth Rogen', 'Vince Gilligan', 'Joe Carnahan', 'Jamal Shoorje', 'Marcos Siega', 'Bronwen Hughes', 'Michelle MacLaren', 'Clark Johnson', 'Nelson McCormick', 'Niels Arden Oplev', 'Jon Favreau', 'Justin Roiland', 'Chris Miller', 'Rich Moore', 'Louis C.K.', 'Scott Stewart', ""Gavin O'Connor"", 'Liz Friedlander', 'Lee Toland Krieger', 'Andy Cadiff', 'Darren Star', 'McG', 'Evan Goldberg', 'Lena Dunham', 'Neil Marshall', 'Michael Offer', 'Adam Davidson', 'Don Scardino', 'David Frankel', 'Amy Sherman-Palladino', 'Gary Fleder', 'Elizabeth Allen', 'Michael Showalter', 'Jesse Peretz', 'Bill Duke', 'Alan Poul', 'Jason Reitman', 'Tom Marshall', 'James Griffiths', 'Julie Plec', 'R. J. Cutler', 'Ruben Fleischer', 'Peter Atencio', 'Phil Traill', 'Craig Brewer', 'Neal Marlens', 'Robert King', 'Ted Wass', 'Dean Parisot', 'Vivienne Medrano', 'Bradley Buecker', 'Steve Zuckerman', 'Amy York Rubin', 'Larry Teng', 'Allan Arkush', 'Martin Scorsese', 'Morten Tyldum', 'James Frawley', 'Karyn Kusama', 'Mike White', 'Paul Krasny', 'Taika Waititi', 'Neil Burger', 'Randall Einhorn', 'Jeffrey Nachmanoff', 'Andrew D. Weyman', 'Rodrigo García Márquez', 'Michael W. Watkins', 'Paul Weitz', 'Kim Han Gyul', 'Rob Thomas']",episode of Dirty Sexy Money (S1 E1),"Who was the director of Pilot, the episode of Dirty Sexy Money (S1 E1)?",True,"Who was the director of the episode ""Pilot"" from Dirty Sexy Money (Season 1, Episode 1)?"
1404706,Echo,director,Hossein Shahabi,628257,526,368023,[],[],http://www.wikidata.org/entity/*********,http://www.wikidata.org/entity/*********,Echo (2001 film),Hossein Shahabi,97,138,Who was the director of Echo?,"[""Hossein Shahabi""]",147,11,"['Hossein Shahabi', 'Rúnar Rúnarsson', 'Daniel Sackheim', 'Anders Morgenthaler', 'Mareike Wegener', 'Wayne Rose', 'Joss Whedon', 'Stanisław Różewicz', 'Merlin Flügel', 'Charles Correll', 'Tom Oesch']",1997 film by Hossein Shahabi,"Who was the director of the 1997 film, Echo?",True,Who was the director of the 1997 film?
1035763,The Trap,director,Frank Reicher,442522,526,163321,"[""Trap""]",[],http://www.wikidata.org/entity/Q16679832,http://www.wikidata.org/entity/Q1307857,The Trap (1919 film),Frank Reicher,97,1872,Who was the director of The Trap?,"[""Frank Reicher""]",56,26,"['Frank Reicher', 'Andrzej Jerzy Piotrowski', 'Peter Watkins', 'Erik White', 'Ricardo Islas', 'Sidney Hayers', 'Carlos Hugo Christensen', 'Choi Moo-ryong', 'Lena Headey', 'Earl Bellamy', 'Sydney Ayres', 'Giuseppe Patroni Griffi', 'Paul Powell', 'William Humphrey', 'Norman Panama', 'Charles Giblyn', 'Adam Curtis', 'George Archainbaud', 'Jalal Mehraban', 'Jeremy Podeswa', 'Edwin August', 'http://www.wikidata.org/.well-known/genid/7fffb400c539ff829ba94a5501394d3e', 'Steve Shill', 'Martin Frič', 'Robert Thornby', 'Fred Toye', 'Howard Bretherton']",1919 film by Frank Reicher,"Who was the director of the 1919 film, The Trap?",True,Who was the director of the 1919 film?
3761375,Cocktail,director,Herman Yau,1635798,526,1033511,[],"[""Herman Yau Lai-to""]",http://www.wikidata.org/entity/Q5139690,http://www.wikidata.org/entity/Q3134110,Cocktail (2006 film),Herman Yau,97,1074,Who was the director of Cocktail?,"[""Herman Yau"", ""Herman Yau Lai-to""]",22,5,"['Herman Yau', 'Herman Yau Lai-to', 'Roger Donaldson', 'Homi Adajania', 'Arun Kumar Aravind', 'Emanuel Gregers']",2006 film by Herman Yau,"Who was the director of the 2006 film, Cocktail?",True,"Who directed the 2006 film, Cocktail?"
1153613,Mother and Child,director,Hans Steinhoff,510107,526,2283377,[],[],http://www.wikidata.org/entity/Q17026501,http://www.wikidata.org/entity/Q68370,Mother and Child (1934 film),Hans Steinhoff,59,404,Who was the director of Mother and Child?,"[""Hans Steinhoff""]",526,4,"['Hans Steinhoff', 'Minoru Shibuya', 'Carl Froelich', 'Rodrigo García Márquez']",1934 German drama film directed by Hans Steinhoff,"Who was the director of the 1934 German drama film, Mother and Child?",True,Who was the director of the 1934 German drama film?
2910921,Public Opinion,director,Frank Reicher,1247164,526,163321,[],[],http://www.wikidata.org/entity/Q3925282,http://www.wikidata.org/entity/Q1307857,Public Opinion (1916 film),Frank Reicher,97,1872,Who was the director of Public Opinion?,"[""Frank Reicher""]",12,3,"['Frank Reicher', 'Frank R. Strayer', 'Goffredo Alessandrini']",1916 film by Frank Reicher,"Who was the director of the 1916 film, Public Opinion?",True,"Who was responsible for directing the 1916 film, *Public Opinion*?"
2769263,College,director,Giuseppe Moccia,1188965,526,303275,[],[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q1526349,College (1984 film),Giuseppe Moccia,90,169,Who was the director of College?,"[""Franco Castellano"", ""Giuseppe Moccia""]",44,6,"['Franco Castellano', 'Giuseppe Moccia', 'Buster Keaton', 'Allen Coulter', 'James W. Horne', 'Don Scardino', 'Deb Hagan', 'Federico Moccia']","1984 film by Giuseppe Moccia, Franco Castellano","Who was the director of the 1984 film, College?",True,Who was the director of the 1984 film?
2819248,Day by Day,director,Alfredo Giannetti,1210103,526,2928942,"[""Giorno per giorno, disperatamente""]",[],http://www.wikidata.org/entity/Q3765991,http://www.wikidata.org/entity/Q973528,"Day by Day, Desperately",Alfredo Giannetti,73,191,Who was the director of Day by Day?,"[""Alfredo Giannetti""]",33,4,"['Alfredo Giannetti', 'Antonio del Amo', 'Vsevolod Shilovsky', 'Felix Herngren']",1961 film by Alfredo Giannetti,"Who was the director of the 1961 film, Day by Day?",True,Who was the director of the 1961 film?
5920612,The Day,director,Alfred Rolfe,2655196,526,1409967,"[""Day""]",[],http://www.wikidata.org/entity/Q7729142,http://www.wikidata.org/entity/Q4723361,The Day (1914 film),Alfred Rolfe (director),48,208,Who was the director of The Day?,"[""Alfred Rolfe""]",17,3,"['Alfred Rolfe', 'Douglas Aarniokoski', 'Peter Finch']",1914 film by Alfred Rolfe,"Who was the director of the 1914 film, The Day?",True,Who was the director of the 1914 film?
2208013,Bingo,director,Jean-Claude Lord,964378,526,1042863,[],[],http://www.wikidata.org/entity/Q2903940,http://www.wikidata.org/entity/Q3165079,Bingo (1974 film),Jean-Claude Lord,94,298,Who was the director of Bingo?,"[""Jean-Claude Lord""]",47,9,"['Jean-Claude Lord', 'Ray Arthur Wang', 'Markus Imboden', 'Garland Yee', 'Rudi Van Den Bossche', 'Larysa Kondracki', 'Matthew Robbins', 'Yōhei Fukuda', 'Chris Landreth', 'Andrew D. Weyman']",1974 film by Jean-Claude Lord,Who was the director of the 1974 film Bingo?,True,Who was the director of the 1974 film?
6232250,Young People,director,Luis Alcoriza,2812570,526,221447,[],"[""Luis Alcoriza de la Vega""]",http://www.wikidata.org/entity/Q8058342,http://www.wikidata.org/entity/Q1396992,Young People (1961 film),Luis Alcoriza,91,503,Who was the director of Young People?,"[""Luis Alcoriza"", ""Luis Alcoriza de la Vega""]",4,3,"['Luis Alcoriza', 'Luis Alcoriza de la Vega', 'Chang Cheh', 'Allan Dwan']",1961 film by Luis Alcoriza,"Who was the director of the 1961 film, Young People?",True,Who was the director of the 1961 film?
1424994,The Kiss,director,Dell Henderson,637334,526,996126,"[""Kiss"",""The Kiss (film 1916)""]",[],http://www.wikidata.org/entity/Q18708988,http://www.wikidata.org/entity/Q3021715,The Kiss (1916 film),Dell Henderson,96,501,Who was the director of The Kiss?,"[""Dell Henderson""]",98,19,"['Dell Henderson', 'Elliot Hegarty', 'Jack Conway', 'William Heise', 'John Hayes', 'Kunitoshi Manda', 'Ulysses Davis', 'Scott Ellis', 'Bruno Barreto', 'Pen Densham', 'Wallace Reid', 'Bille August', 'Bashar Shbib', 'Gorman Bechard', ""Terrence O'Hara"", 'Hilde Van Mieghem', 'Edwin Stanton Porter', 'Cecil Hepworth', 'Jacques Feyder']",1916 silent film directed by Dell Henderson,"Who was the director of the 1916 silent film, The Kiss?",True,Who was the director of the 1916 silent film?
3216989,Accident,director,Sergiu Nicolaescu,1378731,526,2113005,[],"[""Sergiu Nicolaiescu"",""Sergiu Florin Nicolaescu""]",http://www.wikidata.org/entity/Q4672550,http://www.wikidata.org/entity/Q634875,Accident (1976 film),Sergiu Nicolaescu,98,1317,Who was the director of Accident?,"[""Sergiu Nicolaescu"", ""Sergiu Nicolaiescu"", ""Sergiu Florin Nicolaescu""]",33,11,"['Sergiu Nicolaescu', 'Sergiu Nicolaiescu', 'Sergiu Florin Nicolaescu', 'Ernő Metzner', 'Nandita Roy', 'Hans-Henrik Jørgensen', 'Soi Cheang', 'Shankar Nag', 'Ramesh Aravind', 'Shiboprosad Mukherjee', 'Viktor Gertler', 'Ed Bye', 'Joseph Losey', 'Teco Benson']",1976 film directed by Sergiu Nicolaescu,"Who was the director of the 1976 film, Accident?",True,"Who directed the 1976 film, Accident?"
4108797,Fingers,director,Herbert Mason,1799084,526,1214925,[],"[""Samuel George Herbert Mason""]",http://www.wikidata.org/entity/Q5450289,http://www.wikidata.org/entity/Q3785141,Fingers (1941 film),Herbert Mason,59,292,Who was the director of Fingers?,"[""Herbert Mason"", ""Samuel George Herbert Mason""]",14,2,"['Herbert Mason', 'Samuel George Herbert Mason', 'James Toback']",1941 film by Herbert Mason,"Who was the director of the 1941 film, Fingers?",True,Who was the director of the 1941 film?
5720063,September,director,Tian Zhuangzhuang,2552817,526,1736675,[],"[""Zhuangzhuang Tian"",""T\u2018ien Chuang-chuang"",""T\u2018ien, Chuang-chuang"",""Chuang-chuang T\u2018ien""]",http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q532645,September (1984 film),Tian Zhuangzhuang,64,1139,Who was the director of September?,"[""Tian Zhuangzhuang"", ""Zhuangzhuang Tian"", ""T‘ien Chuang-chuang"", ""T‘ien, Chuang-chuang"", ""Chuang-chuang T‘ien""]",64,10,"['Tian Zhuangzhuang', 'Zhuangzhuang Tian', 'T‘ien Chuang-chuang', 'T‘ien, Chuang-chuang', 'Chuang-chuang T‘ien', 'Colin Bucksey', 'Kenneth Muller', 'Max Färberböck', 'Pamela Adlon', 'Lisa Jespersen', 'Cemil Agacikoglu', 'Peter Carstairs', 'Penny Panagiotopoulou', 'Woody Allen']",1984 film by Tian Zhuangzhuang,"Who was the director of the 1984 film, September?",True,Who was the director of the 1984 film?
5951427,The Return,director,Antun Vrdoljak,2670935,526,141408,"[""Povratak"",""Return""]",[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/********,The Return (1979 film),Antun Vrdoljak,98,1611,Who was the director of The Return?,"[""Antun Vrdoljak""]",94,20,"['Antun Vrdoljak', 'Uberto Pasolini', 'Paul A. Edwards', 'J. Miller Tobin', 'Alex Graves', 'Kelly Duane', 'Thomas N. Heffron', 'Patricia Ortega', 'Vladimir Tarasov', 'Jemaine Clement', 'Lena Dunham', '*********', 'Jacqueline Michel', 'Katie Galloway', 'Webster Cullison', 'Asif Kapadia', 'Malene Choi Jensen', 'Greydon Clark', 'Greg Daniels', 'Dermott Downs', 'Jens Jørgen Thorsen', 'Andrey Zvyagintsev']",1979 Croatian film by Antun Vrdoljak,"Who was the director of the 1979 Croatian film, The Return?",True,Who was the director of the 1979 Croatian film?
311014,Vanity,director,Adrian Brunel,125947,526,1199824,[],"[""Adrian Hope Brunel""]",http://www.wikidata.org/entity/Q12131146,http://www.wikidata.org/entity/Q372692,Vanity (1935 film),Adrian Brunel,54,322,Who was the director of Vanity?,"[""Adrian Brunel"", ""Adrian Hope Brunel""]",35,6,"['Adrian Brunel', 'Adrian Hope Brunel', 'Donald Crisp', ""John B. O'Brien"", 'Joe De Grasse', 'Giorgio Pàstina', 'Lionel Baier']",1935 film by Adrian Brunel,"Who was the director of the 1935 film, Vanity?",True,Who was the director of the 1935 film?
1398875,Ghost,director,Hossein Shahabi,625858,526,368023,[],[],http://www.wikidata.org/entity/*********,http://www.wikidata.org/entity/*********,Ghost (1998 film),Hossein Shahabi,85,138,Who was the director of Ghost?,"[""Hossein Shahabi""]",91,7,"['Hossein Shahabi', 'Vikram Bhatt', 'MG Srinivas', 'Jerry Zucker', 'Anthony Z. James', 'Puja Bedi', 'Joss Whedon']",1997 film by Hossein Shahabi,"Who was the director of the 1997 film, Ghost?",True,Who was the director of the 1997 film?
2665518,The Key,director,Antun Vrdoljak,1148049,526,141408,"[""Klju\u010d"",""Key""]",[],http://www.wikidata.org/entity/Q3508978,http://www.wikidata.org/entity/********,The Key (1965 film),Antun Vrdoljak,94,1611,Who was the director of The Key?,"[""Antun Vrdoljak""]",54,16,"['Antun Vrdoljak', 'David Barrett', 'Ron Underwood', 'Michael Curtiz', 'Tinto Brass', 'Sergio Mimica-Gezzan', 'Carol Reed', 'Lev Atamanov', 'David Frazee', 'Krsto Papić', 'Vanča Kljaković', 'Nicole Holofcener', 'Jefery Levy', 'Guillaume Nicloux', 'Vladimír Čech', 'Gregory Nicotero', 'Oscar A. C. Lund', 'Akitaka Kimata']","1965 anthology film by 3 different directors: Antun Vrdoljak, Krsto Papić, Vanča Kljaković","Who was the director of the 1965 anthology film, The Key?",True,Who was the director of the 1965 anthology film?
5965220,The Wolf,director,Michael Curtiz,2678064,526,1640467,"[""Wolf""]",[],http://www.wikidata.org/entity/Q7775416,http://www.wikidata.org/entity/Q51491,The Wolf (1916 film),Michael Curtiz,95,11864,Who was the director of The Wolf?,"[""Michael Curtiz""]",27,7,"['Michael Curtiz', 'James Young', 'Daniel Sackheim', 'Abd el-Halim el-Nahas', 'Guillaume Radot', 'Miguel Courtois', ""Barry O'Neil""]",1916 film by Michael Curtiz,"Who was the director of the 1916 film, The Wolf?",True,Who was the director of the 1916 film?
1345373,The Valley,director,Ghassan Salhab,599688,526,1856568,"[""Valley""]",[],http://www.wikidata.org/entity/Q18170614,http://www.wikidata.org/entity/Q5555872,The Valley (2014 film),Ghassan Salhab,89,128,Who was the director of The Valley?,"[""Ghassan Salhab""]",37,5,"['Ghassan Salhab', 'Saila Kariat', 'Mihály Györik', 'Roger Scholes', 'Peter Jackson']",2014 film directed by Ghassan Salhab,"Who was the director of the 2014 film, The Valley?",True,"Who directed the 2014 film, The Valley?"
5453504,Pilot,director,Andy Ackerman,2425237,526,1432429,"[""Life on a Stick pilot""]","[""Robert Andrew Ackerman""]",http://www.wikidata.org/entity/Q7194351,http://www.wikidata.org/entity/Q4760341,Pilot (Life on a Stick),Andy Ackerman,46,5452,Who was the director of Pilot?,"[""Andy Ackerman"", ""Robert Andrew Ackerman""]",475,226,"['Andy Ackerman', 'Robert Andrew Ackerman', 'Ken Kwapis', 'David Wellington', 'Charles McDougall', 'Joe Russo', 'Aaron Augenblick', 'Albert Calleros', 'Julie Anne Robinson', 'Len Wiseman', 'Phillip Noyce', 'Jace Alexander', 'Anthony Russo', 'Jason Ensler', 'Michael Cuesta', 'Lucy A. Snyder', 'Michael Dante DiMartino', 'Richard Pearce', 'David Nutter', ""Peter O'Fallon"", 'Bryan Singer', 'Brett Ratner', 'Mark Mylod', 'Peter Levin', 'Robert Mandel', 'Marc Buckland', 'Todd Holland', 'Peter Horton', 'Michael Mann', 'David Slade', 'Peter Berg', 'Michael Fresco', 'Jon Turteltaub', 'Howard Deutch', 'Doug Liman', 'Greg Daniels', 'Kevin Bray', 'John Tracy', 'Bill L. Norton', 'Steve Shill', 'Michael Engler', 'Ron Hughart', 'Allen Coulter', 'Gene Reynolds', 'Michael Lembeck', 'Alan Ball', 'Yves Simoneau', 'Thomas Schlamme', 'Michael M. Robin', 'James Burrows', 'J. J. Abrams', 'Declan Lowney', 'Jason Winer', 'Lesli Linka Glatter', 'Mick Jackson', 'Jake Kasdan', 'David Semel', 'Wes Archer', 'Robert Schwentke', 'Ryan Murphy', 'Bill Lawrence', 'Bryan Konietzko', 'Adam Bernstein', 'Michael Dinner', 'David Lynch', 'Anthony Lioi', 'Pamela Fryman', 'Mark Piznarski', 'Alex Graves', 'Greg Yaitanes', 'Dwayne Carey-Hill', 'Danny Cannon', 'Richard Shepard', 'Gregory Hoblit', 'Robby Benson', 'Stephen Hopkins', 'Jay Sandrich', 'Bharat Nalluri', 'John Requa', 'Linda Day', 'Glenn Ficarra', 'Michael Lessac', 'Phil Lord', 'Michael Mayer', 'Joss Whedon', 'Paul McGuigan', 'Patty Jenkins', 'John Madden', 'Jamie Travis', 'Brad Anderson', 'Bryan Gordon', 'Seth Gordon', 'Virgil L. Fabian', 'Lee Daniels', 'Steve Hoefer', 'Alan Taylor', 'Glen Winter', 'Chris Koch', 'Scott Winant', 'John Gray', 'Rob Bowman', 'Francis Lawrence', 'Marc Webb', 'Seth Rogen', 'Vince Gilligan', 'Joe Carnahan', 'Jamal Shoorje', 'Marcos Siega', 'Bronwen Hughes', 'Michelle MacLaren', 'Clark Johnson', 'Nelson McCormick', 'Niels Arden Oplev', 'Jon Favreau', 'Justin Roiland', 'Chris Miller', 'Rich Moore', 'Louis C.K.', 'Scott Stewart', ""Gavin O'Connor"", 'Liz Friedlander', 'Lee Toland Krieger', 'Andy Cadiff', 'Darren Star', 'McG', 'Evan Goldberg', 'Lena Dunham', 'Neil Marshall', 'Michael Offer', 'Adam Davidson', 'Don Scardino', 'David Frankel', 'Amy Sherman-Palladino', 'Gary Fleder', 'Elizabeth Allen', 'Michael Showalter', 'Jesse Peretz', 'Bill Duke', 'Alan Poul', 'Jason Reitman', 'Tom Marshall', 'James Griffiths', 'Julie Plec', 'R. J. Cutler', 'Ruben Fleischer', 'Peter Atencio', 'Phil Traill', 'Craig Brewer', 'Neal Marlens', 'Robert King', 'Ted Wass', 'Dean Parisot', 'Vivienne Medrano', 'Bradley Buecker', 'Steve Zuckerman', 'Amy York Rubin', 'Larry Teng', 'Allan Arkush', 'Martin Scorsese', 'Morten Tyldum', 'James Frawley', 'Karyn Kusama', 'Mike White', 'Paul Krasny', 'Taika Waititi', 'Neil Burger', 'Randall Einhorn', 'Jeffrey Nachmanoff', 'Andrew D. Weyman', 'Rodrigo García Márquez', 'Michael W. Watkins', 'Paul Weitz', 'Kim Han Gyul', 'Rob Thomas']",episode of Life on a Stick (S1 E1),"Who was the director of the episode ""Pilot"" from Life on a Stick (S1 E1)?",True,"Who was the director of the episode ""Pilot"" from Life on a Stick (Season 1, Episode 1)?"
4783338,Just Like Us,director,Ahmed Ahmed,2102966,526,1391478,[],[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/********,Just Like Us (film),Ahmed Ahmed,98,2102,Who was the director of Just Like Us?,"[""Ahmed Ahmed""]",3,2,"['Ahmed Ahmed', 'Piotr Mularuk', 'Ahmed Samih', 'Maria Siniarska']",2010 film by Ahmed Ahmed,"Who was the director of the 2010 film, Just Like Us?",True,"Who directed the 2010 film, Just Like Us?"
701533,Panic,director,Harry Piel,296697,526,2709530,[],"[""Heinrich Piel""]",http://www.wikidata.org/entity/Q15220579,http://www.wikidata.org/entity/Q78402,Panic (1928 film),Harry Piel,84,259,Who was the director of Panic?,"[""Harry Piel"", ""Heinrich Piel""]",49,7,"['Harry Piel', 'Heinrich Piel', 'Bashar Shbib', 'John Gilling', 'Jean-Claude Lord', 'Constantine Makris', 'Henry Bromell', 'Tonino Ricci']",1928 film by Harry Piel,"Who was the director of the 1928 film, Panic?",True,"""Who was the director of the 1928 film?"""
3060497,Victory,director,Mikhail Doller,1308059,526,571731,[],"[""Mikhail Ivanovich Doller""]",http://www.wikidata.org/entity/Q4366190,http://www.wikidata.org/entity/Q1775732,Victory (1938 film),Mikhail Doller,64,150,Who was the director of Victory?,"[""Mikhail Doller"", ""Mikhail Ivanovich Doller"", ""Vsevolod Pudovkin"", ""Vsevolod Illarionovich Pudovkin"", ""Wsewolod Illarionowitsch Pudowkin""]",119,11,"['Mikhail Doller', 'Mikhail Ivanovich Doller', 'Vsevolod Pudovkin', 'Vsevolod Illarionovich Pudovkin', 'Wsewolod Illarionowitsch Pudowkin', 'M. A. Wetherell', 'Eliran Peled', 'Yevgeny Matveyev', 'John Cromwell', 'Ajit Pal Mangat', 'Alex Zakrzewski', 'Nanda Kishore', 'Maurice Tourneur', 'Mark Peploe', 'Park Beom-su']","1938 film by Vsevolod Pudovkin, Mikhail Doller","Who was the director of the 1938 film, Victory?",True,Who was the director of the 1938 film?
5924855,The Fake,screenwriter,Frederick Lonsdale,2657349,533,2099005,"[""Fake""]",[],http://www.wikidata.org/entity/Q7733155,http://www.wikidata.org/entity/Q630412,The Fake (1927 film),Frederick Lonsdale,81,802,Who was the screenwriter for The Fake?,"[""Frederick Lonsdale""]",6,3,"['Frederick Lonsdale', 'Yeon Sang-ho', 'Bridget Boland']",1927 film by Georg Jacoby,"Who was the screenwriter for the 1927 film, The Fake?",True,Who was the screenwriter for the 1927 film?
5453555,Pilot,screenwriter,Nell Scovell,2425255,533,1096304,[],"[""Helen Vivian Scovell""]",http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/********,Pilot (Sabrina the Teenage Witch),Nell Scovell,16,1562,Who was the screenwriter for Pilot?,"[""Nell Scovell"", ""Helen Vivian Scovell""]",475,183,"['Nell Scovell', 'Helen Vivian Scovell', 'Jay Beattie', 'Joe Weisberg', 'Marc Cherry', 'Winnie Holzman', 'Jason Katims', 'Dan Fogelman', 'Andrew Kreisberg', 'David Shore', 'Maurissa Tancharoen', 'I. Marlene King', 'Michelle Ashford', 'Allison Adler', 'Jhonen Vasquez', 'Bruno Heller', 'Kevin Williamson', 'Julie Plec', 'Greg Berlanti', 'Robert King', 'Damon Lindelof', 'Michelle King', 'Emily Spivey', 'Vince Gilligan', 'George Pelecanos', 'Tom Perrotta', 'Shawn Ryan', 'Aaron Sorkin', 'Rob Thomas', 'Jon Bokenkamp', 'Mark Schwahn', 'Geoff Johns', 'Brian K. Vaughan', 'Michael Schur', 'David Kohan', 'Tim Kring', 'Al Jean', 'Jeff Eastin', 'Louis C.K.', 'Becky Mode', 'Daniel J. Goor', 'Dan Harmon', 'Marc Guggenheim', 'Bryan Cranston', 'Max Mutchnick', 'Jonathan Nolan', 'Dan Schneider', 'Mike Reiss', 'Hank Steinberg', 'Dan Dworkin', 'Jason Rothenberg', 'Jed Whedon', 'Ryan Murphy', 'Kari Lizer', 'Silvio Horta', 'Steve Franks', 'Chuck Lorre', 'Steven Bochco', 'Greg Garcia', 'Jim Kouf', 'Hart Hanson', 'Mitchell Hurwitz', 'Kevin Falls', 'Mike Bullen', 'Miles Millar', 'Alex Gansa', 'Howard Gordon', 'Alex Kurtzman', 'David Jacobs', 'Aaron Korsh', 'Richard Appel', 'Eric Kripke', 'Jane Espenson', 'Linwood Boomer', 'Brad Falchuk', 'Chris Carter', 'Mark Frost', 'Bill Prady', 'Carter Bays', 'Matt Nix', 'Seth MacFarlane', 'Andrew Cosby', 'J. J. Abrams', 'Jonathan E. Steinberg', 'Alfred Gough', 'Peter Berg', 'Christopher Lloyd', 'Kurt Sutter', 'Jaime Paglia', 'Craig Wright', 'Scott Peters', 'Greg Daniels', 'Gideon Raff', 'April Kelly', 'James Duff', 'Toby Whithouse', 'Tina Fey', 'Josh Friedman', 'Elizabeth Meriwether', 'Gregory Poirier', 'Paul Scheuring', 'Philip Rosenthal', 'Cheryl Heuton', 'Steven Levitan', 'Kyle Killen', 'Stephen Chbosky', 'Mike Kelley', 'Ed. Weinberger', 'Anthony E. Zuiker', 'David Greenwalt', 'Ian Brennan', 'David Lynch', 'Victor Fresco', 'Amy Sherman-Palladino', 'Matt Weitzman', 'Nicolas Falacci', 'David Milch', 'Roberto Orci', 'Craig Thomas', 'Mike Henry', 'Michael J. Leeson', 'Tom Kapinos', 'Robert Doherty', 'Mike Judge', 'Larry Gelbart', 'Josh Schwartz', 'Carly Mensch', 'Mark Reisman', 'Bill Lawrence', 'Jenna Bans', 'Michael Rauch', 'Brendan Hunt', 'Darren Star', 'Robert Kikman', 'Lena Dunham', 'Charlie Day', 'Peter Nowalk', 'Todd Helbing', 'Meredith Averill', 'Caroline Dries', 'Jeff Rake', 'Ed Redlich', 'Nahnatchka Khan', 'Tom Hertz', 'Mindy Kaling', 'Bryan Fuller', 'Liz Flahive', 'Kevin Biegel', 'David S. Goyer', 'Amy Poehler', 'James Cameron', 'Ted Griffin', 'John Bellucci', 'Mike Scully', 'Sera Gamble', 'Joss Whedon', 'Callie Khouri', 'Julie Thacker', 'Joseph Fink', 'Dick Wolf', 'Alexi Hawley', 'Dave Erickson', 'Jason Sudeikis', 'Michael J. Weithorn', 'Justin Spitzer', 'David Kendall', 'Jeremy Carver', 'Jeffrey Cranor', 'Charles H. Eglee', 'Kenya Barris', 'Zander Lehmann', 'Andrew Ross Sorkin', 'Roman Coppola', 'Vivienne Medrano', 'Bentley Kyle Evans', 'Christopher Silber', 'Jason Schwartzman', 'Joey Gutierrez', 'David Levien', 'Barbara Hall', 'Graham Roland', 'Tim Minear', 'Liz Feldman', 'Gary Scott Thompson', 'Alex Timbers', 'Randi Mayem Singer', 'Carlton Cuse', 'Martin Scorsese', 'Will Scheffer', 'Diane Burroughs', 'Quinta Brunson', 'Mark V. Olsen', 'Jemaine Clement', 'Mike White', 'Jamie Foxx', 'Dave Capdevielle', 'Raymond T. Hernandez', 'Kendraw Cook', 'Maritza Medrano', 'David E. Kelley', 'Leila Gerstein', 'David Caspe', 'Emily Kapnek', 'Derek Haas', 'Michael Brandt', 'Diane Ruggiero', 'Terence Winter', 'Mick Jagger', 'George Mastras', 'Rich Cohen', 'Ashley Lyle', 'Brian Koppelman', 'Mark Roberts', 'Laura Dern']","pilot episode of Sabrina, the Teenage Witch directed by Robby Benson","Who was the screenwriter for Pilot, the pilot episode of Sabrina, the Teenage Witch directed by Robby Benson?",True,"Who was the screenwriter for the pilot episode of *Sabrina, the Teenage Witch* directed by Robby Benson?"
1808819,Goodbye,screenwriter,Heddy Honigmann,798070,533,826959,[],[],http://www.wikidata.org/entity/Q2337775,http://www.wikidata.org/entity/Q2438043,Goodbye (1995 film),Heddy Honigmann,82,450,Who was the screenwriter for Goodbye?,"[""Heddy Honigmann""]",82,5,"['Heddy Honigmann', 'Krista Vernoff', 'Steve Hudson', 'Brad Falchuk', 'Mohammad Rasoulof']",1995 film by Heddy Honigmann,"Who was the screenwriter for the 1995 film, Goodbye?",True,"Who was the writer for the 1995 film, Goodbye?"
5386404,Party,screenwriter,Jay Woelfel,2395663,533,2050226,[],[],http://www.wikidata.org/entity/Q7140881,http://www.wikidata.org/entity/Q6167359,Party (1994 film),,64,-2,Who was the screenwriter for Party?,"[""Jay Woelfel""]",40,6,"['Jay Woelfel', 'Leonard Dick', 'Venkat Prabhu', 'Anand Satyanand', 'Govind Nihalani', 'Manoel de Oliveira']",1994 film by Eric Swelstad,"Who was the screenwriter for the 1994 film, Party?",True,"Who was responsible for writing the screenplay for the 1994 film, Party?"
4271609,Grace,screenwriter,Melinda Hsu Taylor,1872744,533,2272870,[],[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/********,Grace (Falling Skies),Melinda Hsu Taylor,85,681,Who was the screenwriter for Grace?,"[""Melinda Hsu Taylor""]",136,5,"['Melinda Hsu Taylor', 'Damian Kindler', 'Tom Kapinos', 'Leila Gerstein', 'Alex Taub', 'Paul Solet']",episode of Falling Skies (S1 E4),"Who was the screenwriter for Grace, an episode of Falling Skies (Season 1, Episode 4)?",True,"Who was the screenwriter for the episode ""Grace"" from Falling Skies (Season 1, Episode 4)?"
1398876,Ghost,screenwriter,Hossein Shahabi,625858,533,368023,[],[],http://www.wikidata.org/entity/*********,http://www.wikidata.org/entity/*********,Ghost (1998 film),Hossein Shahabi,85,138,Who was the screenwriter for Ghost?,"[""Hossein Shahabi""]",91,4,"['Hossein Shahabi', 'Kang Eun-kyung', 'Joss Whedon', 'Bruce Joel Rubin']",1997 film by Hossein Shahabi,"Who was the screenwriter for the 1997 film, Ghost?",True,Who was the screenwriter for the 1997 film?
5907057,The Accused,screenwriter,Mario Soffici,2648243,533,44376,"[""Los acusados"",""Accused""]",[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/*********,The Accused (1960 film),Mario Soffici,56,127,Who was the screenwriter for The Accused?,"[""Mario Soffici"", ""Marco Denevi"", ""Marco Denev""]",10,3,"['Mario Soffici', 'Marco Denevi', 'Marco Denev', 'Tom Topor', 'Ketti Frings']",1960 film,"Who was the screenwriter for the 1960 film, The Accused?",True,"Who was responsible for writing the screenplay for the 1960 film, *The Accused*?"
1413015,Democracy,screenwriter,Sidney Morgan,631928,533,163008,[],[],http://www.wikidata.org/entity/Q18639171,http://www.wikidata.org/entity/Q1306984,Democracy (film),Sidney Morgan,82,204,Who was the screenwriter for Democracy?,"[""Sidney Morgan""]",18,2,"['Sidney Morgan', 'Nicolas Falacci', 'Cheryl Heuton']",1918 film by Sidney Morgan,"Who was the screenwriter for the 1918 film, Democracy?",True,Who was the screenwriter for the 1918 film?
5563710,Revelations,screenwriter,Tony Gayton,2479689,533,2702217,[],[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/********,Revelations (Hell on Wheels),Tony Gayton,86,374,Who was the screenwriter for Revelations?,"[""Tony Gayton""]",51,7,"['Tony Gayton', 'Joseph Mallozzi', 'Doug Petrie', 'Bradley Thompson', 'Fredrick Kotto', 'Paul Mullie', 'Mark Kruger', 'Corey Miller', 'J. Michael Straczynski', 'David Seltzer', 'David Weddle']",episode of Hell on Wheels (S1 E7),"Who was the screenwriter for Revelations, an episode of Hell on Wheels (Season 1, Episode 7)?",True,"Who was the screenwriter for Revelations, an episode of Hell on Wheels (Season 1)?"
4294776,Guilty,screenwriter,Evan Katz,1884030,533,1782013,[],[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/********,Guilty (Awake),Evan Katz,69,661,Who was the screenwriter for Guilty?,"[""Howard Gordon"", ""Evan Katz""]",61,6,"['Howard Gordon', 'Evan Katz', 'Vincent Garenq', 'Sari Tervaniemi', 'Nils Willbrandt', 'David Slack', 'Erik Oleson', 'Keto Shimizu']",episode of Awake (S1 E3),"Who was the screenwriter for Guilty, an episode of Awake (S1 E3)?",True,"Who was the screenwriter for the episode ""Guilty"" from the series *Awake* (Season 1, Episode 3)?"
5674281,Salvation,screenwriter,Paul Cox,2530070,533,808109,[],"[""Paulus Henriqus Benedictus Cox"",""Paulus Henrique Benedictus Cox""]",http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q237268,Salvation (2008 film),Paul Cox (director),99,852,Who was the screenwriter for Salvation?,"[""Paul Cox"", ""Paulus Henriqus Benedictus Cox"", ""Paulus Henrique Benedictus Cox""]",38,6,"['Paul Cox', 'Paulus Henriqus Benedictus Cox', 'Paulus Henrique Benedictus Cox', 'Ivan Vyrypaev', 'Alex Kurtzman', 'Ed Zuckerman', 'Raelle Tucker', 'Eric Kripke', 'Wendy Mericle', 'Sera Gamble', 'Drew Z. Greenberg', 'Roberto Orci']",2008 Australian film directed by Paul Cox,"Who was the screenwriter for the 2008 Australian film, Salvation?",True,Who was the screenwriter for the 2008 Australian film?
5937084,The Last Word,screenwriter,Binka Zhelyazkova,2663513,533,964381,"[""Last Word""]",[],http://www.wikidata.org/entity/Q7746198,http://www.wikidata.org/entity/Q2903953,The Last Word (1973 film),Binka Zhelyazkova,86,263,Who was the screenwriter for The Last Word?,"[""Binka Zhelyazkova""]",30,5,"['Binka Zhelyazkova', 'James Duff', 'Wasfi Darwish', 'Iris Wagner', 'Michael Varhol']",1973 film by Binka Zhelyazkova,"Who was the screenwriter for the 1973 film, The Last Word?",True,"Who was responsible for writing the screenplay for the 1973 film, *The Last Word*?"
6164657,White Gold,screenwriter,John Jopson,2780120,533,1049563,[],"[""John Charles Jopson""]",http://www.wikidata.org/entity/Q7994726,http://www.wikidata.org/entity/Q3181837,White Gold (2003 film),John Jopson,71,232,Who was the screenwriter for White Gold?,"[""John Jopson"", ""John Charles Jopson""]",11,4,"['John Jopson', 'John Charles Jopson', 'Q5948008', 'Viktor Ivanov', 'John Farrow', 'Larry Gelbart']",2003 film by Viktor Ivanov,"Who was the screenwriter for White Gold, the 2003 film directed by Viktor Ivanov?",True,"Who was the screenwriter for White Gold, the 2003 film directed by Viktor Ivanov?"
5453489,Pilot,screenwriter,Kevin Falls,2425232,533,2133287,"[""Franklin & Bash pilot""]",[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/********,Pilot (Franklin & Bash),Kevin Falls,5,250,Who was the screenwriter for Pilot?,"[""Kevin Falls""]",475,183,"['Kevin Falls', 'Jay Beattie', 'Joe Weisberg', 'Marc Cherry', 'Winnie Holzman', 'Jason Katims', 'Dan Fogelman', 'Andrew Kreisberg', 'David Shore', 'Maurissa Tancharoen', 'I. Marlene King', 'Michelle Ashford', 'Allison Adler', 'Jhonen Vasquez', 'Bruno Heller', 'Kevin Williamson', 'Julie Plec', 'Greg Berlanti', 'Robert King', 'Damon Lindelof', 'Michelle King', 'Emily Spivey', 'Vince Gilligan', 'George Pelecanos', 'Tom Perrotta', 'Shawn Ryan', 'Aaron Sorkin', 'Rob Thomas', 'Jon Bokenkamp', 'Mark Schwahn', 'Geoff Johns', 'Brian K. Vaughan', 'Michael Schur', 'David Kohan', 'Tim Kring', 'Al Jean', 'Jeff Eastin', 'Louis C.K.', 'Becky Mode', 'Daniel J. Goor', 'Dan Harmon', 'Marc Guggenheim', 'Bryan Cranston', 'Max Mutchnick', 'Jonathan Nolan', 'Dan Schneider', 'Mike Reiss', 'Hank Steinberg', 'Dan Dworkin', 'Jason Rothenberg', 'Jed Whedon', 'Ryan Murphy', 'Kari Lizer', 'Silvio Horta', 'Steve Franks', 'Chuck Lorre', 'Steven Bochco', 'Greg Garcia', 'Jim Kouf', 'Hart Hanson', 'Mitchell Hurwitz', 'Mike Bullen', 'Miles Millar', 'Alex Gansa', 'Howard Gordon', 'Alex Kurtzman', 'David Jacobs', 'Aaron Korsh', 'Richard Appel', 'Eric Kripke', 'Jane Espenson', 'Linwood Boomer', 'Brad Falchuk', 'Chris Carter', 'Mark Frost', 'Bill Prady', 'Carter Bays', 'Matt Nix', 'Seth MacFarlane', 'Andrew Cosby', 'J. J. Abrams', 'Jonathan E. Steinberg', 'Alfred Gough', 'Peter Berg', 'Christopher Lloyd', 'Kurt Sutter', 'Jaime Paglia', 'Craig Wright', 'Scott Peters', 'Greg Daniels', 'Gideon Raff', 'April Kelly', 'James Duff', 'Toby Whithouse', 'Tina Fey', 'Josh Friedman', 'Elizabeth Meriwether', 'Gregory Poirier', 'Paul Scheuring', 'Philip Rosenthal', 'Cheryl Heuton', 'Steven Levitan', 'Kyle Killen', 'Stephen Chbosky', 'Mike Kelley', 'Ed. Weinberger', 'Anthony E. Zuiker', 'David Greenwalt', 'Ian Brennan', 'Nell Scovell', 'David Lynch', 'Victor Fresco', 'Amy Sherman-Palladino', 'Matt Weitzman', 'Nicolas Falacci', 'David Milch', 'Roberto Orci', 'Craig Thomas', 'Mike Henry', 'Michael J. Leeson', 'Tom Kapinos', 'Robert Doherty', 'Mike Judge', 'Larry Gelbart', 'Josh Schwartz', 'Carly Mensch', 'Mark Reisman', 'Bill Lawrence', 'Jenna Bans', 'Michael Rauch', 'Brendan Hunt', 'Darren Star', 'Robert Kikman', 'Lena Dunham', 'Charlie Day', 'Peter Nowalk', 'Todd Helbing', 'Meredith Averill', 'Caroline Dries', 'Jeff Rake', 'Ed Redlich', 'Nahnatchka Khan', 'Tom Hertz', 'Mindy Kaling', 'Bryan Fuller', 'Liz Flahive', 'Kevin Biegel', 'David S. Goyer', 'Amy Poehler', 'James Cameron', 'Ted Griffin', 'John Bellucci', 'Mike Scully', 'Sera Gamble', 'Joss Whedon', 'Callie Khouri', 'Julie Thacker', 'Joseph Fink', 'Dick Wolf', 'Alexi Hawley', 'Dave Erickson', 'Jason Sudeikis', 'Michael J. Weithorn', 'Justin Spitzer', 'David Kendall', 'Jeremy Carver', 'Jeffrey Cranor', 'Charles H. Eglee', 'Kenya Barris', 'Zander Lehmann', 'Andrew Ross Sorkin', 'Roman Coppola', 'Vivienne Medrano', 'Bentley Kyle Evans', 'Christopher Silber', 'Jason Schwartzman', 'Joey Gutierrez', 'David Levien', 'Barbara Hall', 'Graham Roland', 'Tim Minear', 'Liz Feldman', 'Gary Scott Thompson', 'Alex Timbers', 'Randi Mayem Singer', 'Carlton Cuse', 'Martin Scorsese', 'Will Scheffer', 'Diane Burroughs', 'Quinta Brunson', 'Mark V. Olsen', 'Jemaine Clement', 'Mike White', 'Jamie Foxx', 'Dave Capdevielle', 'Raymond T. Hernandez', 'Kendraw Cook', 'Maritza Medrano', 'David E. Kelley', 'Leila Gerstein', 'David Caspe', 'Emily Kapnek', 'Derek Haas', 'Michael Brandt', 'Diane Ruggiero', 'Terence Winter', 'Mick Jagger', 'George Mastras', 'Rich Cohen', 'Ashley Lyle', 'Brian Koppelman', 'Mark Roberts', 'Laura Dern']",episode of Franklin & Bash (S1 E1),"Who was the screenwriter for Pilot, the episode of Franklin & Bash (S1 E1)?",True,Who was the screenwriter for the first episode of Franklin & Bash (S1 E1)?
5495438,Prototype,screenwriter,Gary David Goldberg,2446277,533,2898390,"[""Pilot""]",[],http://www.wikidata.org/entity/Q7252168,http://www.wikidata.org/entity/Q932508,Prototype (Spin City),Gary David Goldberg,4,3815,Who was the screenwriter for Prototype?,"[""Bill Lawrence"", ""William Van Duzer Lawrence IV"", ""Gary David Goldberg""]",28,4,"['Bill Lawrence', 'William Van Duzer Lawrence IV', 'Gary David Goldberg', 'Paul Jenkins', 'George Lopez', 'Dennis Detwiller', 'Nicholas J. Corea', 'Robert Borden']",pilot episode of the ABC sitcom Spin City,"Who was the screenwriter for Prototype, the pilot episode of the ABC sitcom Spin City?",True,"Who was the screenwriter for Prototype, the pilot episode of the ABC sitcom?"
1332312,The City,screenwriter,Clyde Fitch,592187,533,983985,"[""City""]","[""William Clyde Fitch""]",http://www.wikidata.org/entity/Q18148906,http://www.wikidata.org/entity/Q2980425,The City (1916 film),Clyde Fitch,71,570,Who was the screenwriter for The City?,"[""Clyde Fitch"", ""William Clyde Fitch""]",72,5,"['Clyde Fitch', 'William Clyde Fitch', 'T. Damodaran', 'Jiri Divis', 'David Riker', 'Dmitry Konstantinov']",1916 film by Thurlow Bergen,"Who was the screenwriter for the 1916 film, The City?",True,"Who contributed as the screenwriter for the 1916 film, *The City*?"
4294775,Guilty,screenwriter,Howard Gordon,1884030,533,818261,[],[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q2410088,Guilty (Awake),Howard Gordon,69,1797,Who was the screenwriter for Guilty?,"[""Howard Gordon"", ""Evan Katz""]",61,6,"['Howard Gordon', 'Evan Katz', 'Vincent Garenq', 'Sari Tervaniemi', 'Nils Willbrandt', 'David Slack', 'Erik Oleson', 'Keto Shimizu']",episode of Awake (S1 E3),"Who was the screenwriter for Guilty, an episode of Awake (S1 E3)?",True,"Who was the screenwriter for the episode ""Guilty"" from the show *Awake* (Season 1, Episode 3)?"
5951428,The Return,screenwriter,Antun Vrdoljak,2670935,533,141408,"[""Povratak"",""Return""]",[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/********,The Return (1979 film),Antun Vrdoljak,98,1611,Who was the screenwriter for The Return?,"[""Antun Vrdoljak""]",94,13,"['Antun Vrdoljak', 'Uberto Pasolini', 'Q70252752', 'Kevin Williamson', 'Julie Plec', 'John Collee', 'William E. Wing', 'Kelly Duane', 'Judd Apatow', 'Erik Oleson', 'Jemaine Clement', 'Lena Dunham', 'Katie Galloway', 'Marc Guggenheim', 'Vladimir Moiseenko', 'Edward Bond', 'Gene Stupnitsky', 'Jens Jørgen Thorsen', 'Lee Eisenberg', 'Jane Espenson', 'Michael Schur', 'Aleksandr Novototsky-Vlasov']",1979 Croatian film by Antun Vrdoljak,"Who was the screenwriter for the 1979 Croatian film, The Return?",True,"Who was responsible for writing the screenplay for the 1979 Croatian film, *The Return*?"
2208014,Bingo,screenwriter,Jean-Claude Lord,964378,533,1042863,[],[],http://www.wikidata.org/entity/Q2903940,http://www.wikidata.org/entity/Q3165079,Bingo (1974 film),Jean-Claude Lord,94,298,Who was the screenwriter for Bingo?,"[""Jean-Claude Lord""]",47,5,"['Jean-Claude Lord', 'Rudi Van Den Bossche', 'Joe Brumm', 'Greg Kotis', 'Lise Thouin', 'Gennifer Hutchison']",1974 film by Jean-Claude Lord,"Who was the screenwriter for the 1974 film, Bingo?",True,Who was the screenwriter for the 1974 film?
5907058,The Accused,screenwriter,Marco Denevi,2648243,533,1341298,"[""Los acusados"",""Accused""]","[""Marco Denev""]",http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q457338,The Accused (1960 film),Marco Denevi,56,612,Who was the screenwriter for The Accused?,"[""Mario Soffici"", ""Marco Denevi"", ""Marco Denev""]",10,3,"['Mario Soffici', 'Marco Denevi', 'Marco Denev', 'Tom Topor', 'Ketti Frings']",1960 film,"Who was the screenwriter for the 1960 film, The Accused?",True,"Who was responsible for writing the screenplay for the 1960 film, The Accused?"
2779584,Daybreak,screenwriter,Albert Capellani,1193153,533,715932,[],[],http://www.wikidata.org/entity/Q3703816,http://www.wikidata.org/entity/Q2037315,Daybreak (1918 film),Albert Capellani,75,256,Who was the screenwriter for Daybreak?,"[""Albert Capellani""]",58,10,"['Albert Capellani', 'Dolgoryn Dojoodorj', 'June Mathis', 'Muriel Box', 'Sydney Box', 'Ronald D. Moore', 'Stephen Tolkin', 'Cyril Hume', 'Oliver Storz', 'Badrahin Sumhu', 'Jon Raymond', 'Björn Runge', 'Werner P. Zibaso', 'Monckton Hoffe']",1918 American silent drama film directed by Albert Capellani,"Who was the screenwriter for the 1918 American silent drama film, Daybreak?",True,Who was the screenwriter for the 1918 American silent drama film?
5342026,One,composer,Michel van der Aa,2373288,639,2433214,[],[],http://www.wikidata.org/entity/Q7092429,http://www.wikidata.org/entity/Q721124,One (opera),Michel van der Aa,58,709,Who was the composer of One?,"[""Michel van der Aa""]",124,5,"['Michel van der Aa', 'Lars Ulrich', 'James Hetfield', 'Ane Brun', 'U2', 'Arindom Chatterjee']",chamber opera by Michel van der Aa,Who was the composer of the chamber opera One?,True,Who was the composer of the chamber opera titled One?
4365923,Hello,composer,Masaharu Fukuyama,1918406,639,83380,[],[],http://www.wikidata.org/entity/Q5708575,http://www.wikidata.org/entity/Q1141809,Hello (Masaharu Fukuyama song),Masaharu Fukuyama,67,3535,Who was the composer of Hello?,"[""Masaharu Fukuyama""]",79,13,"['Masaharu Fukuyama', 'Kelly Clarkson', 'Felix Riebl', 'Bonnie McKee', 'Christina Milian', 'Alex James', 'Ilan Kidron', 'Martina Sorbara', 'Mike Leander', 'Noel Gallagher', 'Anoop Rubens', 'Adele', 'Greg Kurstin', 'Josh Abraham', 'Lil Wayne', 'Jon Marsh', 'Deva', 'Gary Glitter', 'Sajid-Wajid', 'Lionel Richie']",1995 single by Masaharu Fukuyama,"Who was the composer of the 1995 single, Hello?",True,"Who was the creator of the 1995 single, Hello?"
1398877,Ghost,composer,Hossein Shahabi,625858,639,368023,[],[],http://www.wikidata.org/entity/*********,http://www.wikidata.org/entity/*********,Ghost (1998 film),Hossein Shahabi,85,138,Who was the composer of Ghost?,"[""Hossein Shahabi""]",91,4,"['Hossein Shahabi', 'Glen Ballard', 'Halsey', 'Maurice Jarre', 'David A. Stewart']",1997 film by Hossein Shahabi,"Who was the composer of the 1997 film, Ghost?",True,"Who was the composer for the 1997 film, Ghost?"
5785289,Solo,composer,Karlheinz Stockhausen,2585276,639,316249,[],"[""Karl-Heinz Stockhausen"",""Karlheinz Stockhausen""]",http://www.wikidata.org/entity/Q7558434,http://www.wikidata.org/entity/Q154556,Solo (Stockhausen),Karlheinz Stockhausen,96,21179,Who was the composer of Solo?,"[""Karlheinz Stockhausen"", ""Karl-Heinz Stockhausen"", ""Karlheinz Stockhausen""]",131,11,"['Karlheinz Stockhausen', 'Karl-Heinz Stockhausen', 'Carly Rae Jepsen', 'Todor Kobakov', 'Georges Moustaki', 'Blanka Stajkow', 'Tribbs', 'James Flannigan', 'Bilal Wahib', 'Nicola Piovani', 'Christopher Franke', 'Mani Sharma', 'Sandro Gorli', 'Lisa Gerrard', 'Zoë Tauran', 'Nate Campany']",composition by Karlheinz Stockhausen,Who was the composer of the composition titled Solo?,True,Who created the composition titled Solo?
2944504,To Live,composer,Tito Schipa,1260682,639,1339293,"[""Vivere""]","[""Raffaele Attilio Amedeo Schipa"",""Tito Skipa"",""Tito Shipa"",""Tito Skipu"",""Titus Schipa"",""Titto Shippa"",""Titto Skipa"",""Raffaele Schipa""]",http://www.wikidata.org/entity/Q4015669,http://www.wikidata.org/entity/Q456827,To Live (1937 film),Tito Schipa,98,868,Who was the composer of To Live?,"[""Tito Schipa"", ""Raffaele Attilio Amedeo Schipa"", ""Tito Skipa"", ""Tito Shipa"", ""Tito Skipu"", ""Titus Schipa"", ""Titto Shippa"", ""Titto Skipa"", ""Raffaele Schipa""]",11,3,"['Tito Schipa', 'Raffaele Attilio Amedeo Schipa', 'Tito Skipa', 'Tito Shipa', 'Tito Skipu', 'Titus Schipa', 'Titto Shippa', 'Titto Skipa', 'Raffaele Schipa', 'Zhao Jiping', 'Jakob Hansonis']",1937 film by Guido Brignone,"Who was the composer of To Live, the 1937 film directed by Guido Brignone?",True,"Who was the composer of the 1937 film *To Live*, directed by Guido Brignone?"
5965071,The Witch,composer,Maurice Ravel,2677991,639,104623,[],"[""Joseph-Maurice Ravel"",""Maurice Joseph Ravel"",""Joseph Maurice Ravel""]",http://www.wikidata.org/entity/Q7775273,http://www.wikidata.org/entity/Q1178,The Witch (ballet),Maurice Ravel,72,42435,Who was the composer of The Witch?,"[""Maurice Ravel"", ""Joseph-Maurice Ravel"", ""Maurice Joseph Ravel"", ""Joseph Maurice Ravel""]",33,3,"['Maurice Ravel', 'Joseph-Maurice Ravel', 'Maurice Joseph Ravel', 'Joseph Maurice Ravel', 'Bert Grund', 'Mark Korven']",ballet,"Who was the composer of the ballet, The Witch?",True,Who was the composer of the ballet titled *The Witch*?
4549284,Images,composer,Claude Debussy,2005856,639,1393777,[],"[""Claud Debussy"",""Debussy"",""Claude Achille Debussy"",""Achille Claude Debussy"",""C. Debussy"",""Claude-Achille Debussy"",""Achille-Claude"",""Achille-Claude Debussy""]",http://www.wikidata.org/entity/Q6002359,http://www.wikidata.org/entity/Q4700,Images (ballet),Claude Debussy,74,70258,Who was the composer of Images?,"[""Claude Debussy"", ""Claud Debussy"", ""Debussy"", ""Claude Achille Debussy"", ""Achille Claude Debussy"", ""C. Debussy"", ""Claude-Achille Debussy"", ""Achille-Claude"", ""Achille-Claude Debussy""]",43,4,"['Claude Debussy', 'Claud Debussy', 'Debussy', 'Claude Achille Debussy', 'Achille Claude Debussy', 'C. Debussy', 'Claude-Achille Debussy', 'Achille-Claude', 'Achille-Claude Debussy', 'John Williams', 'Howard Skempton']",ballet by Miriam Mahdaviani,"Who was the composer of Images, a ballet by Miriam Mahdaviani?",True,Who was the composer of the ballet Images by Miriam Mahdaviani?
75913,I'm in Love,composer,Bobby Ljunggren,29524,639,1528690,[],"[""Robert Vasilis Ljunggren"",""Robert Vasilis Engdahl""]",http://www.wikidata.org/entity/Q10532742,http://www.wikidata.org/entity/Q4935297,I'm in Love (Sanna Nielsen song),Bobby Ljunggren,78,231,Who was the composer of I'm in Love?,"[""Bobby Ljunggren"", ""Robert Vasilis Ljunggren"", ""Robert Vasilis Engdahl""]",17,2,"['Bobby Ljunggren', 'Robert Vasilis Ljunggren', 'Robert Vasilis Engdahl', 'Arvid Solvang', 'Thomas G:son', 'Peter Boström', 'Maria Mena']","song written and composed by Peter Boström, Thomas Gustafsson, Bobby Ljunggren and Irini Michas, originally performed by Sanna Nielsen at Melodifestivalen 2011","Who was the composer of I'm in Love, the song originally performed by Sanna Nielsen at Melodifestivalen 2011?",True,Who was the composer of the song originally performed by Sanna Nielsen at Melodifestivalen 2011?
4060489,Piano Concerto,composer,Tan Dun,1775094,639,1036475,"[""The Fire""]","[""Dun Tan""]",http://www.wikidata.org/entity/Q5398790,http://www.wikidata.org/entity/Q314176,Piano Concerto (Tan Dun),Tan Dun,54,2716,Who was the composer of Piano Concerto?,"[""Tan Dun"", ""Dun Tan""]",67,55,"['Tan Dun', 'Dun Tan', 'John Corigliano', 'Francis Poulenc', 'Elena Kats-Chernin', 'Alexander Scriabin', 'Antonín Dvořák', 'Howard Hanson', 'Akio Yashiro', 'Eduard Nápravník', 'Benjamin Britten', 'Ferruccio Busoni', 'Esa-Pekka Salonen', 'Maurice Ravel', 'Heinz Karl Gruber', 'Willem Pijper', 'Ralph Vaughan Williams', 'Jules Massenet', 'Wilhelm Furtwängler', 'Arthur Somervell', 'Robert Schumann', 'Amy Beach', 'Kimmo Hakola', 'Carter Pann', 'Judith Weir', 'Vítězslava Kaprálová', 'John Ireland', 'Samuel Barber', 'Christian Sinding', 'Elliott Carter', 'Aram Khachaturian', 'Edvard Grieg', 'Jennifer Higdon', 'Eyvind Alnæs', 'Boris Tishchenko', 'Witold Lutosławski', 'Peter Maxwell Davies', 'Richard Arnell', 'Nikolai Rimsky-Korsakov', 'Thomas Adès', 'Aaron Copland', 'Albert Roussel', 'Krzysztof Penderecki', 'Carlos Chávez', 'Alun Hoddinott', 'Ignacy Jan Paderewski', 'David Winkler', 'Beat Furrer', 'Max Reger', 'Arnold Schoenberg', 'Christopher Norton', 'Frederick Delius', 'Michael Tippett', 'Arthur Bliss', 'Kurt Atterberg', 'György Ligeti', 'Gabriel Pierné']",concerto by Tan Dun,Who was the composer of the Piano Concerto?,True,Who created the Piano Concerto?
1137769,Homecoming,composer,Sammy Adams,501356,639,2532039,[],"[""Samuel Adams Wisner""]",http://www.wikidata.org/entity/Q16995754,http://www.wikidata.org/entity/Q7409698,Homecoming (EP),Sammy Adams,93,3649,Who was the composer of Homecoming?,"[""Sammy Adams"", ""Samuel Adams Wisner""]",132,9,"['Sammy Adams', 'Samuel Adams Wisner', 'Kitarō', 'Willy Schmidt-Gentner', 'Georg Buljo', 'Bronisław Kaper', 'Jon Bernstein', 'Gaili Schoen', 'Hummie Mann']",EP by Sammy Adams,"Who was the composer of the EP, Homecoming?",True,"Who was the creator of the EP, Homecoming?"
778998,To the Sky,composer,Darko Dimitrov,332015,639,993479,"[""Tamu kaj \u0161to pripagjam""]",[],http://www.wikidata.org/entity/Q15705420,http://www.wikidata.org/entity/Q3016341,To the Sky (Tijana song),Darko Dimitrov,94,492,Who was the composer of To the Sky?,"[""Darko Dimitrov""]",5,2,"['Darko Dimitrov', 'Adam Young']",song by Tijana Dapčević,"Who was the composer of To the Sky, a song by Tijana Dapčević?",True,Who was the composer of the song To the Sky by Tijana Dapčević?
5700988,Say When,composer,Ray Henderson,2542961,639,696330,[],"[""Raymond Brost""]",http://www.wikidata.org/entity/Q7429028,http://www.wikidata.org/entity/Q1980821,Say When (musical),Ray Henderson,69,953,Who was the composer of Say When?,"[""Ray Henderson"", ""Raymond Brost""]",6,2,"['Ray Henderson', 'Raymond Brost', 'Larry Boone', 'Paul Nelson', 'John Rich']","American musical with music by Ray Henderson, lyrics by Ted Koehler, and a musical book by Jack McGowan","Who was the composer of Say When, the American musical with lyrics by Ted Koehler and a musical book by Jack McGowan?",True,"Who was the composer of Say When, the American musical with lyrics by Ted Koehler and a book by Jack McGowan?"
2162041,Alone,composer,Evan Brewer,945151,639,1236512,[],[],http://www.wikidata.org/entity/Q2839224,http://www.wikidata.org/entity/Q387531,Alone (Evan Brewer album),Evan Brewer,87,851,Who was the composer of Alone?,"[""Evan Brewer""]",117,8,"['Evan Brewer', 'Josh Carter', 'Jesper Borgen', 'Gunnar Greve', 'Maciej Śledziecki', 'Peter Sculthorpe', 'Mood Melodies', 'Tony Hester', 'Bojan Adamič', 'Mithoon', 'Ankit Tiwari', 'Raghav Sachar', 'Dan Wilson', 'Jeet Ganguly', 'Halsey', 'Dmitri Shostakovich', 'Ricky Reed', 'Noonie Bao', 'Alan Walker']",2011 album by Evan Brewer,"Who was the composer of Alone, the 2011 album?",True,Who was the composer of the 2011 album titled Alone?
4090925,Famous,composer,Tinchy Stryder,1790214,639,2390411,[],"[""The Star in the Hood""]",http://www.wikidata.org/entity/Q5433439,http://www.wikidata.org/entity/Q712992,Famous (Tinchy Stryder song),Tinchy Stryder,79,11900,Who was the composer of Famous?,"[""Tinchy Stryder"", ""The Star in the Hood""]",22,2,"['Tinchy Stryder', 'The Star in the Hood', 'Oathmademedoit', 'Jonna Fraser']",2010 song composed by Tinchy Stryder performed by Tinchy Stryder,"Who was the composer of the 2010 song, Famous?",True,"Who created the 2010 song, Famous?"
4549280,Images,composer,Howard Skempton,2005854,639,777905,[],"[""Howard While Skempton""]",http://www.wikidata.org/entity/Q6002354,http://www.wikidata.org/entity/Q2261216,Images (Skempton),Howard Skempton,41,668,Who was the composer of Images?,"[""Howard Skempton"", ""Howard While Skempton""]",43,4,"['Howard Skempton', 'Howard While Skempton', 'John Williams', 'Claude Debussy']",album of piano pieces by Howard Skempton and played by Michael Finnissy,"Who was the composer of Images, an album of piano pieces played by Michael Finnissy?",True,"Who was the composer of Images, an album of piano pieces performed by Michael Finnissy?"
1465830,Time Machine,composer,Michael Daugherty,654744,639,775523,[],"[""Michael Kevin Daugherty""]",http://www.wikidata.org/entity/Q19059515,http://www.wikidata.org/entity/Q2251665,Time Machine (composition),Michael Daugherty,81,1138,Who was the composer of Time Machine?,"[""Michael Daugherty"", ""Michael Kevin Daugherty""]",38,2,"['Michael Daugherty', 'Michael Kevin Daugherty', 'Rob Cantor', 'Joe Hawley']",composition,Who was the composer of Time Machine?,False,Who created the musical work titled Time Machine?
5745774,Shine,composer,Luna Sea,2565092,639,839260,[],"[""Lunacy""]",http://www.wikidata.org/entity/Q7497340,http://www.wikidata.org/entity/Q24760,Shine (Luna Sea song),Luna Sea,56,2942,Who was the composer of Shine?,"[""Luna Sea"", ""Lunacy""]",121,7,"['Luna Sea', 'Lunacy', 'Lemmy', 'Phil Taylor', 'Brian Robertson', 'Dimitris Kontopoulos', 'David Hirschfelder', 'John Legend', 'Martin Gore', 'Joni Mitchell', 'Philipp Kirkorov']",song by Luna Sea,Who was the composer of the song Shine?,True,Who created the song Shine?
1825771,String Quartet No. 3,composer,Carl Nielsen,804385,639,720076,"[""String Quartet No. 3 in E flat major"",""Quartet for Two Violins, Viola and Cello in E flat major""]","[""Carl August Nielsen""]",http://www.wikidata.org/entity/Q2358947,http://www.wikidata.org/entity/Q205139,String Quartet No. 3 (Nielsen),Carl Nielsen,76,7711,Who was the composer of String Quartet No. 3?,"[""Carl Nielsen"", ""Carl August Nielsen""]",45,42,"['Carl Nielsen', 'Carl August Nielsen', 'Milton Babbitt', 'William Alwyn', 'Benjamin Britten', 'Louis Spohr', 'Aulis Sallinen', 'Giacinto Scelsi', 'Ludwig van Beethoven', 'Karel Husa', 'Heitor Villa-Lobos', 'Per Nørgård', 'Henrique Oswald', 'Ernő Dohnányi', 'Joseph Haydn', 'Franz Schubert', 'Pyotr Ilyich Tchaikovsky', 'Johan Kvandal', 'Wilhelm Stenhammar', 'Julián Bautista', 'Silvestre Revueltas', 'Walter Piston', 'George Rochberg', 'Dmitri Shostakovich', 'Max Reger', 'Jón Leifs', 'Grażyna Bacewicz', 'Béla Bartók', 'Antonín Dvořák', 'Frank Bridge', 'Elliott Carter', 'Alexander Glazunov', 'Malcolm Williamson', 'Paul Hindemith', 'Ernst Krenek', 'Arthur Honegger', 'Boris Tchaikovsky', 'Henryk Górecki', 'Vítězslav Novák', 'Felix Mendelssohn', 'Johannes Brahms', 'Ernest Bloch', ""Vincent d'Indy""]",String Quartet by Carl Nielsen,Who was the composer of String Quartet No. 3?,False,Who created String Quartet No. 3?
2664743,Symphony No. 33,composer,Michael Haydn,1147757,639,1510582,[],"[""Johann Michael Haydn""]",http://www.wikidata.org/entity/Q3507744,http://www.wikidata.org/entity/Q490381,Symphony No. 33 (Michael Haydn),Michael Haydn,59,3635,Who was the composer of Symphony No. 33?,"[""Michael Haydn"", ""Johann Michael Haydn""]",4,3,"['Michael Haydn', 'Johann Michael Haydn', 'Joseph Haydn', 'Wolfgang Amadeus Mozart']",symphony by Michael Haydn,Who was the composer of Symphony No. 33?,False,Who created Symphony No. 33?
5866070,Symphony No. 8,composer,Einojuhani Rautavaara,2627484,639,1301677,"[""The Journey""]","[""Eino Juhani Rautavaara"",""Eino-Juhani Rautavaara""]",http://www.wikidata.org/entity/Q7661622,http://www.wikidata.org/entity/Q433592,Symphony No. 8 (Rautavaara),Einojuhani Rautavaara,92,2667,Who was the composer of Symphony No. 8?,"[""Einojuhani Rautavaara"", ""Eino Juhani Rautavaara"", ""Eino-Juhani Rautavaara""]",36,35,"['Einojuhani Rautavaara', 'Eino Juhani Rautavaara', 'Eino-Juhani Rautavaara', 'Alexander Glazunov', 'Heitor Villa-Lobos', 'Peter Maxwell Davies', 'Vagn Holmboe', 'Philip Bračanin', 'Dmitri Shostakovich', 'Nikolai Myaskovsky', 'Philip Glass', 'Aulis Sallinen', 'Miloslav Kabeláč', 'Malcolm Arnold', 'Alfred Schnittke', 'Joseph Haydn', 'Ludwig van Beethoven', 'Roger Sessions', 'Havergal Brian', 'Antonín Dvořák', 'Gustav Mahler', 'Gian Francesco Malipiero', 'Robert Simpson', 'Jean Sibelius', 'Darius Milhaud', 'Walter Piston', 'Hans Werner Henze', 'Erkki-Sven Tüür', 'Wolfgang Amadeus Mozart', 'Karl Amadeus Hartmann', 'Michael Haydn', 'Anton Bruckner', 'William Schuman', 'Krzysztof Penderecki', 'Max Butting', 'Ralph Vaughan Williams', 'Franz Schubert']",symphony by Einojuhani Rautavaara,Who was the composer of Symphony No. 8?,False,Who created Symphony No. 8?
780748,One More Time,composer,James LaBrie,332864,639,979479,[],"[""Kevin James LaBrie"",""00219295358 IPI""]",http://www.wikidata.org/entity/Q15711359,http://www.wikidata.org/entity/Q296039,One More Time (James LaBrie song),James LaBrie,75,12739,Who was the composer of One More Time?,"[""James LaBrie"", ""Kevin James LaBrie"", ""00219295358 IPI""]",49,6,"['James LaBrie', 'Kevin James LaBrie', '00219295358 IPI', 'Joe McGinty', 'Les Reed', 'Barrie Gledden', 'Simon Gallup', 'Robert Smith', 'Lol Tolhurst', 'Frank Marsales', 'Porl Thompson', 'Peter Shand', 'Boris Williams']",2010 song by James LaBrie,"Who was the composer of the 2010 song, One More Time?",True,Who was the composer of the 2010 song?
4732699,John Richardson,religion,Anglicanism,2081996,106,2145926,"[""John Henry Richardson"",""Rt. Rev. John Richardson""]","[""Anglicanism, Anglican Church""]",http://www.wikidata.org/entity/Q6254817,http://www.wikidata.org/entity/Q6423963,John Richardson (bishop of Bedford),Anglicanism,84,56744,What is the religion of John Richardson?,"[""Anglicanism"", ""Anglicanism, Anglican Church""]",96,8,"['Anglicanism', 'Anglicanism, Anglican Church', 'Quakers']",Bishop of Bedford; British Anglican bishop,"What is the religion of John Richardson, the British bishop of Bedford?",True,"What is the religion of John Richardson, the British bishop?"
4906189,Ladislav Žák,sport,association football,2162627,560,920845,"[""Ladislav Zak""]","[""football"",""soccer""]",http://www.wikidata.org/entity/Q6469741,http://www.wikidata.org/entity/Q2736,Ladislav Žák (footballer),Association football,30,197767,What sport does Ladislav Žák play?,"[""association football"", ""football"", ""soccer""]",4,2,"['association football', 'football', 'soccer', 'ice hockey']",Slovak association football player,"What sport does Ladislav Žák, a Slovak athlete, play?",True,"What sport is associated with Ladislav Žák, a Slovak athlete?"
331490,Walter Pfeiffer,sport,association football,133773,560,920845,[],"[""football"",""soccer""]",http://www.wikidata.org/entity/Q12344117,http://www.wikidata.org/entity/Q2736,Walter Pfeiffer (footballer),Association football,77,197767,What sport does Walter Pfeiffer play?,"[""association football"", ""football"", ""soccer""]",7,2,"['association football', 'football', 'soccer', 'rowing']",Austrian footballer (1927-2014),"What sport does Walter Pfeiffer, the Austrian athlete (1927–2014), play?",True,"What sport is associated with Walter Pfeiffer, the Austrian athlete (1927–2014)?"
2748925,Bob Priddy,sport,basketball,1180871,560,1762290,[],"[""hoops"",""b-ball"",""basket ball"",""BB"",""Basketball""]",http://www.wikidata.org/entity/Q3641332,http://www.wikidata.org/entity/Q5372,Bob Priddy (basketball),Basketball,78,149310,What sport does Bob Priddy play?,"[""basketball"", ""hoops"", ""b-ball"", ""basket ball"", ""BB"", ""Basketball""]",2,2,"['basketball', 'hoops', 'b-ball', 'basket ball', 'BB', 'Basketball', 'baseball']",American professional basketball player,"What sport does Bob Priddy, an American professional athlete, play?",True,"What sport does Bob Priddy, an American professional athlete, participate in?"
3331448,Andrew Tucker,sport,association football,1431328,560,920845,[],"[""football"",""soccer""]",http://www.wikidata.org/entity/Q4758767,http://www.wikidata.org/entity/Q2736,Andrew Tucker (soccer),Association football,88,197767,What sport does Andrew Tucker play?,"[""association football"", ""football"", ""soccer""]",10,2,"['association football', 'football', 'soccer', 'shooting sport', 'fullbore target rifle']",Association footballer,What sport does Andrew Tucker play?,False,What sport is associated with Andrew Tucker?
3508703,Bill Wilkinson,sport,ice hockey,1515645,560,1279430,"[""William Wilkinson""]",[],http://www.wikidata.org/entity/Q4911376,http://www.wikidata.org/entity/Q41466,Bill Wilkinson (ice hockey),Ice hockey,92,59363,What sport does Bill Wilkinson play?,"[""ice hockey""]",5,3,"['ice hockey', 'athletics', 'baseball']",Canadian ice hockey defenceman,"What sport does Bill Wilkinson, a Canadian athlete, play?",True,"What sport does Bill Wilkinson, the Canadian athlete, participate in?"
3539436,Bobby Windsor,sport,association football,1528997,560,920845,[],"[""football"",""soccer""]",http://www.wikidata.org/entity/Q4935732,http://www.wikidata.org/entity/Q2736,Bobby Windsor (footballer),Association football,74,197767,What sport does Bobby Windsor play?,"[""association football"", ""football"", ""soccer""]",2,2,"['association football', 'football', 'soccer', 'rugby union']",Footballer (1926-2000),"What sport does Bobby Windsor, a footballer (1926–2000), play?",True,What sport does Bobby Windsor (1926–2000) play?
5153011,Michael Gibson,sport,association football,2279900,560,920845,[],"[""football"",""soccer""]",http://www.wikidata.org/entity/Q6830606,http://www.wikidata.org/entity/Q2736,Michael Gibson (soccer),Association football,99,197767,What sport does Michael Gibson play?,"[""association football"", ""football"", ""soccer""]",11,5,"['association football', 'football', 'soccer', 'Australian rules football', 'rugby union', 'basketball', 'biathlon']",Australian association football player,"What sport does Michael Gibson, an Australian athlete, play?",True,"What sport does Michael Gibson, an Australian, play?"
3862326,David Davidson,sport,association football,1685047,560,920845,[],"[""football"",""soccer""]",http://www.wikidata.org/entity/Q5232837,http://www.wikidata.org/entity/Q2736,"David Davidson (footballer, born 1934)",Association football,32,197767,What sport does David Davidson play?,"[""association football"", ""football"", ""soccer""]",16,5,"['association football', 'football', 'soccer', 'baseball', 'archery']",Scottish footballer (born 1934),"What sport does David Davidson, the Scottish athlete born in 1934, play?",True,"What sport does David Davidson, the Scottish athlete born in 1934, participate in?"
972414,Gustavo García,sport,association football,414565,560,920845,[],"[""football"",""soccer""]",http://www.wikidata.org/entity/Q16242191,http://www.wikidata.org/entity/Q2736,Gustavo García (1980s footballer),Association football,56,197767,What sport does Gustavo García play?,"[""association football"", ""football"", ""soccer""]",6,2,"['association football', 'football', 'soccer', 'shooting sport']",Mexican footballer,"What sport does Gustavo García, a Mexican athlete, play?",True,"What is the sport played by Gustavo García, the Mexican athlete?"
4209409,George Burley,sport,association football,1845724,560,920845,"[""George Marcus Burley""]","[""football"",""soccer""]",http://www.wikidata.org/entity/Q5537437,http://www.wikidata.org/entity/Q2736,George Burley (English footballer),Association football,69,197767,What sport does George Burley play?,"[""association football"", ""football"", ""soccer""]",4,2,"['association football', 'football', 'soccer']",English footballer (born 1900),"What sport does George Burley, an English athlete born in 1900, play?",True,"What sport does George Burley, an English athlete born in 1900, participate in?"
1238197,Pablo Aguilar,sport,association football,550349,560,920845,[],"[""football"",""soccer""]",http://www.wikidata.org/entity/Q17504750,http://www.wikidata.org/entity/Q2736,"Pablo Aguilar (footballer, born 1984)",Association football,45,197767,What sport does Pablo Aguilar play?,"[""association football"", ""football"", ""soccer""]",7,4,"['association football', 'football', 'soccer', 'basketball', 'badminton']",Argentinian footballer born 1984,"What sport does Pablo Aguilar, the Argentinian athlete born in 1984, play?",True,"What sport does Pablo Aguilar, the Argentinian born in 1984, play?"
6354799,1994–95 Fußball-Bundesliga,sport,association football,2864682,560,920845,[],"[""football"",""soccer""]",http://www.wikidata.org/entity/Q880548,http://www.wikidata.org/entity/Q2736,1994–95 Frauen-Bundesliga,Association football,73,197767,What sport does 1994–95 Fußball-Bundesliga play?,"[""association football"", ""football"", ""soccer""]",2,2,"['association football', 'football', 'soccer']",women's football season,What sport does the 1994–95 season of the women's Fußball-Bundesliga play?,True,What sport is played during the 1994–95 season of the women's Fußball-Bundesliga?
2025544,Francis,sport,association football,887778,560,920845,"[""Francisco Jes\u00fas P\u00e9rez Malia"",""Francisco Jesus Perez Malia""]","[""football"",""soccer""]",http://www.wikidata.org/entity/Q2635435,http://www.wikidata.org/entity/Q2736,"Francis (footballer, born 1981)",Association football,60,197767,What sport does Francis play?,"[""association football"", ""football"", ""soccer""]",36,2,"['association football', 'football', 'soccer', 'cricket']",Spanish football player,"What sport does Francis, a Spanish athlete, play?",True,"What is the sport played by Francis, a Spanish athlete?"
5707942,Scott Muirhead,sport,association football,2546581,560,920845,[],"[""football"",""soccer""]",http://www.wikidata.org/entity/Q7436917,http://www.wikidata.org/entity/Q2736,Scott Muirhead,Association football,74,197767,What sport does Scott Muirhead play?,"[""association football"", ""football"", ""soccer""]",2,2,"['association football', 'football', 'soccer', 'basketball']",Scottish footballer (born 1984),"What sport does Scott Muirhead, the Scottish athlete born in 1984, play?",True,"What sport does Scott Muirhead, the Scottish athlete born in 1984, participate in?"
4729881,John Parsons,sport,association football,2080891,560,920845,"[""John Stuart Parsons""]","[""football"",""soccer""]",http://www.wikidata.org/entity/Q6251994,http://www.wikidata.org/entity/Q2736,John Parsons (footballer),Association football,69,197767,What sport does John Parsons play?,"[""association football"", ""football"", ""soccer""]",25,2,"['association football', 'football', 'soccer', 'horse racing']",Welsh footballer (born 1950),"What sport does John Parsons, the Welsh athlete born in 1950, play?",True,"What sport does John Parsons, the Welsh athlete born in 1950, participate in?"
901152,Paul Chapman,sport,association football,382284,560,920845,"[""Paul Christopher Chapman""]","[""football"",""soccer""]",http://www.wikidata.org/entity/Q16148201,http://www.wikidata.org/entity/Q2736,"Paul Chapman (footballer, born 1951)",Association football,53,197767,What sport does Paul Chapman play?,"[""association football"", ""football"", ""soccer""]",8,2,"['association football', 'football', 'soccer', 'Australian rules football']",English footballer (born 1951),"What sport does Paul Chapman, the English athlete born in 1951, play?",True,"What sport does Paul Chapman, the English athlete born in 1951, participate in?"
323685,Atanas Atanasov,sport,association football,130548,560,920845,"[""Atanas Atanasov - Orela""]","[""football"",""soccer""]",http://www.wikidata.org/entity/Q12272534,http://www.wikidata.org/entity/Q2736,"Atanas Atanasov (footballer, born 1969)",Association football,94,197767,What sport does Atanas Atanasov play?,"[""association football"", ""football"", ""soccer""]",21,7,"['association football', 'football', 'soccer', 'basketball', 'cycle sport', 'athletics', 'amateur wrestling']",Bulgarian association football player and manager,"What sport does Atanas Atanasov, the Bulgarian player and manager, play?",True,"What sport is associated with Atanas Atanasov, the Bulgarian?"
5085889,Mario Aguilar,sport,association football,2248290,560,920845,[],"[""football"",""soccer""]",http://www.wikidata.org/entity/Q6764515,http://www.wikidata.org/entity/Q2736,Mario Aguilar (footballer),Association football,83,197767,What sport does Mario Aguilar play?,"[""association football"", ""football"", ""soccer""]",7,2,"['association football', 'football', 'soccer']",football player,What sport does Mario Aguilar play?,False,What sport is associated with Mario Aguilar?
3487891,Best,sport,association football,1506612,560,920845,"[""Artur Paulo Oliveira da Silva""]","[""football"",""soccer""]",http://www.wikidata.org/entity/Q4896547,http://www.wikidata.org/entity/Q2736,"Best (footballer, born 1968)",Association football,80,197767,What sport does Best play?,"[""association football"", ""football"", ""soccer""]",35,2,"['association football', 'football', 'soccer']",Portuguese footballer,"What sport does Best, the Portuguese athlete, play?",True,"What sport does Best, the Portuguese athlete, participate in?"
4211857,George Douglas,sport,association football,1846596,560,920845,"[""George Harold Douglas""]","[""football"",""soccer""]",http://www.wikidata.org/entity/Q5538598,http://www.wikidata.org/entity/Q2736,George Douglas (footballer),Association football,54,197767,What sport does George Douglas play?,"[""association football"", ""football"", ""soccer""]",31,3,"['association football', 'football', 'soccer', 'golf', 'rugby', 'rugby league']",English footballer (1893-1979),"What sport does George Douglas, the English athlete (1893–1979), play?",True,"What sport is associated with George Douglas, the English athlete (1893–1979)?"
6147968,Watchers at the Strait Gate,author,Russell Kirk,2770778,484,343466,[],"[""Russell Amos Kirk""]",http://www.wikidata.org/entity/Q7973124,http://www.wikidata.org/entity/Q158997,Watchers at the Strait Gate,Russell Kirk,84,6817,Who is the author of Watchers at the Strait Gate?,"[""Russell Kirk"", ""Russell Amos Kirk""]",2,2,"['Russell Kirk', 'Russell Amos Kirk']",book by Russell Kirk,"Who is the author of the book, Watchers at the Strait Gate?",True,"Who is the author of the book, *Watchers at the Strait Gate*?"
3545210,Bones,author,Jonathan Kellerman,1531860,484,189232,[],"[""Jonathan Seth Kellerman""]",http://www.wikidata.org/entity/Q4941740,http://www.wikidata.org/entity/Q1349245,Bones (Kellerman novel),Jonathan Kellerman,50,3362,Who is the author of Bones?,"[""Jonathan Kellerman"", ""Jonathan Seth Kellerman""]",45,5,"['Jonathan Kellerman', 'Jonathan Seth Kellerman', 'Pat Murphy', 'Rob Bidder', 'Gabrielle Lord', 'Chenjerai Hove']",novel by Jonathan Kellerman,Who is the author of the novel Bones?,True,Who wrote the novel Bones?
5343876,Only Human,author,Eileen Wilks,2374223,484,161626,[],[],http://www.wikidata.org/entity/Q7094201,http://www.wikidata.org/entity/Q1303988,Only Human (short story),Eileen Wilks,75,300,Who is the author of Only Human?,"[""Eileen Wilks""]",33,4,"['Eileen Wilks', 'Gareth Roberts', 'Sidney Fields', 'Sylvain Neuvel']",short story by Eileen Wilks,Who is the author of the short story Only Human?,True,Who is the author of the short story titled *Only Human*?
5360441,Out of the Dark,author,Welwyn Wilton Katz,2382622,484,2774427,[],[],http://www.wikidata.org/entity/Q7111735,http://www.wikidata.org/entity/Q7982157,Out of the Dark (Wilton Katz novel),Welwyn Wilton Katz,68,121,Who is the author of Out of the Dark?,"[""Welwyn Wilton Katz""]",18,5,"['Welwyn Wilton Katz', 'Ursula Curtiss', 'David Weber', 'Patrick Modiano', 'Helen Keller']",book by Welwyn Wilton Katz,Who is the author of the book *Out of the Dark*?,True,Who wrote the book *Out of the Dark*?
5952436,The Romantic,author,Barbara Gowdy,2671471,484,1764984,[],[],http://www.wikidata.org/entity/Q7761418,http://www.wikidata.org/entity/Q537699,The Romantic (2003 novel),Barbara Gowdy,72,507,Who is the author of The Romantic?,"[""Barbara Gowdy""]",4,2,"['Barbara Gowdy', 'Hermann Broch']",book by Barbara Gowdy,"Who is the author of the book, The Romantic?",True,"Who wrote the book, *The Romantic*?"
5346275,Operator,author,David Williamson,2375471,484,2925937,[],"[""David Keith Williamson""]",http://www.wikidata.org/entity/Q7097833,http://www.wikidata.org/entity/Q968980,Operator (play),David Williamson,70,3666,Who is the author of Operator?,"[""David Williamson"", ""David Keith Williamson""]",20,2,"['David Williamson', 'David Keith Williamson', 'Elvira Moya de Guerra']",play by David Williamson,Who is the author of the play Operator?,True,Who wrote the play Operator?
1120770,Sail,author,James Patterson,490412,484,1027692,[],"[""James Brendan Patterson"",""James B. Patterson""]",http://www.wikidata.org/entity/Q16961056,http://www.wikidata.org/entity/Q311671,Sail (novel),James Patterson,46,43182,Who is the author of Sail?,"[""James Patterson"", ""James Brendan Patterson"", ""James B. Patterson""]",13,2,"['James Patterson', 'James Brendan Patterson', 'James B. Patterson', 'Randall Munroe']",book by James Patterson,Who is the author of the book Sail?,True,Who wrote the book Sail?
4109854,Fire,author,Alan Rodgers,1799623,484,1398552,[],[],http://www.wikidata.org/entity/Q5451304,http://www.wikidata.org/entity/Q4707654,Fire (Rodgers novel),Alan Rodgers,62,119,Who is the author of Fire?,"[""Alan Rodgers""]",154,8,"['Alan Rodgers', 'Stephanie Trigg', 'Randall Munroe', 'Valerie Martínez', 'Teddy Park', 'Jacob de Boer', 'Mats Strandberg', 'Brian Michael Bendis', 'Sara Bergmark Elfgren', 'Kristin Cashore']",1990 novel by Alan Rodgers,"Who is the author of the 1990 novel, Fire?",True,Who is the author of the 1990 novel?
668466,Carnival of Souls,author,Melissa Marr,282628,484,372451,[],"[""M. A. Marr"",""Melissa A Marr""]",http://www.wikidata.org/entity/Q15032966,http://www.wikidata.org/entity/Q1606880,Untamed City: Carnival of Secrets,Melissa Marr,69,474,Who is the author of Carnival of Souls?,"[""Melissa Marr"", ""M. A. Marr"", ""Melissa A Marr""]",10,3,"['Melissa Marr', 'M. A. Marr', 'Melissa A Marr', 'Nancy Holder']",young adult fantasy novel,Who is the author of the young adult fantasy novel Carnival of Souls?,True,Who is the author of the young adult fantasy novel *Carnival of Souls*?
2877692,Rage,author,Jonathan Kellerman,1232688,484,189232,[],"[""Jonathan Seth Kellerman""]",http://www.wikidata.org/entity/Q3853913,http://www.wikidata.org/entity/Q1349245,Rage (Kellerman novel),Jonathan Kellerman,12,3362,Who is the author of Rage?,"[""Jonathan Kellerman"", ""Jonathan Seth Kellerman""]",57,8,"['Jonathan Kellerman', 'Jonathan Seth Kellerman', 'Justin Taylor', 'Michael Novotny', 'Shuichi Yoshida', 'Stephen King', 'Bob Woodward', 'Jackie Kessler', 'Wilbur Smith']",novel by Jonathan Kellerman,Who is the author of the novel Rage?,True,Who wrote the novel Rage?
981484,Kid,author,Simon Armitage,419377,484,696387,[],"[""Simon Robert Armitage""]",http://www.wikidata.org/entity/Q16255818,http://www.wikidata.org/entity/Q1981093,Kid (poetry collection),Simon Armitage,84,11550,Who is the author of Kid?,"[""Simon Armitage"", ""Simon Robert Armitage""]",23,2,"['Simon Armitage', 'Simon Robert Armitage', 'Eddy de Pretto']",book by Simon Armitage,Who is the author of the book Kid?,True,Who wrote the book Kid?
4357548,Heaven,author,Jack Cohen,1914449,484,1413418,[],[],http://www.wikidata.org/entity/Q5694612,http://www.wikidata.org/entity/Q472872,Heaven (Stewart and Cohen novel),Jack Cohen (biologist),51,416,Who is the author of Heaven?,"[""Jack Cohen""]",104,7,"['Jack Cohen', 'Johnson Oatman Jr.', 'V. C. Andrews', 'Mieko Kawakami', 'Randall Munroe', 'Ian Stewart', 'Rupert Brook', 'Jesse Randall Baxter, Jr.']",science fiction novel written by Ian Stewart and Jack Cohen,"Who is the author of the science fiction novel, Heaven?",True,"Who is the author of the novel, Heaven?"
3776549,Conan the Valiant,author,Roland J. Green,1643885,484,863173,[],"[""Roland James Green""]",http://www.wikidata.org/entity/Q5158190,http://www.wikidata.org/entity/Q2558680,Conan the Valiant,Roland J. Green,98,234,Who is the author of Conan the Valiant?,"[""Roland J. Green"", ""Roland James Green""]",2,2,"['Roland J. Green', 'Roland James Green']",novel by Roland J. Green,"Who is the author of the novel, Conan the Valiant?",True,"Who is the author of the novel, *Conan the Valiant*?"
1110634,Regeneration,author,H. Rider Haggard,483823,484,807869,[],"[""Henry Rider Haggard"",""Sir Henry Rider Haggard"",""H. R. Haggard"",""H Rider Haggard""]",http://www.wikidata.org/entity/Q16931738,http://www.wikidata.org/entity/Q237196,Regeneration (Haggard book),H. Rider Haggard,66,8650,Who is the author of Regeneration?,"[""H. Rider Haggard"", ""Henry Rider Haggard"", ""Sir Henry Rider Haggard"", ""H. R. Haggard"", ""H Rider Haggard""]",31,3,"['H. Rider Haggard', 'Henry Rider Haggard', 'Sir Henry Rider Haggard', 'H. R. Haggard', 'H Rider Haggard', 'Paul Hawken', 'Pat Barker']",book by Henry Rider Haggard,Who is the author of the book Regeneration?,True,Who wrote the book Regeneration?
1114616,Saint,author,Lino Rulli,486290,484,2192797,[],[],http://www.wikidata.org/entity/Q16948730,http://www.wikidata.org/entity/Q6554689,Saint (book),Lino Rulli,29,650,Who is the author of Saint?,"[""Lino Rulli""]",29,2,"['Lino Rulli', 'Ted Dekker']",book,Who is the author of the book Saint?,True,Who wrote the book Saint?
1487811,Homecoming,author,Robin Hobb,663964,484,799897,[],"[""Margaret Astrid Lindholm Ogden"",""Megan Lindholm"",""Margaret Astrid Lindholm""]",http://www.wikidata.org/entity/Q1931478,http://www.wikidata.org/entity/Q234403,Homecoming (Robin Hobb short story),Robin Hobb,33,15180,Who is the author of Homecoming?,"[""Robin Hobb"", ""Margaret Astrid Lindholm Ogden"", ""Megan Lindholm"", ""Margaret Astrid Lindholm""]",132,14,"['Robin Hobb', 'Margaret Astrid Lindholm Ogden', 'Megan Lindholm', 'Margaret Astrid Lindholm', ""Ngũgĩ wa Thiong'o"", 'Lauren Weedman', 'R. A. Salvatore', 'Cynthia Voigt', 'Christie Golden', 'Peter David', 'C. J. Cherryh', 'Anna Shattuck Palmer', 'Michael Jan Friedman', 'Bruce Dawe']",short story by Robin Hobb,Who is the author of the short story Homecoming?,True,Who wrote the short story Homecoming?
5360349,Out of This World,author,Lawrence Watt-Evans,2382574,484,735598,[],[],http://www.wikidata.org/entity/Q7111643,http://www.wikidata.org/entity/Q2095351,Out of This World (Watt-Evans novel),Lawrence Watt-Evans,48,526,Who is the author of Out of This World?,"[""Lawrence Watt-Evans""]",29,4,"['Lawrence Watt-Evans', 'Murray Leinster', 'Graham Swift', 'Henry Hasse']",fantasy novel in The Worlds of Shadow trilogy by Lawrence Watt-Evans,"Who is the author of Out of This World, a fantasy novel in The Worlds of Shadow trilogy?",True,"Who is the author of Out of This World, a novel in The Worlds of Shadow trilogy?"
475186,Memory,author,Poul Anderson,194243,484,763131,"[""A World Called Maanerek""]","[""Poul William Anderson"",""Winston P. Sanders"",""A. A. Craig"",""Michael Karageorge"",""Petronius Arbiter Kingsley"",""P. A. Kingsley""]",http://www.wikidata.org/entity/Q1354634,http://www.wikidata.org/entity/Q220883,Memory (Poul Anderson),Poul Anderson,66,7848,Who is the author of Memory?,"[""Poul Anderson"", ""Poul William Anderson"", ""Winston P. Sanders"", ""A. A. Craig"", ""Michael Karageorge"", ""Petronius Arbiter Kingsley"", ""P. A. Kingsley""]",79,16,"['Poul Anderson', 'Poul William Anderson', 'Winston P. Sanders', 'A. A. Craig', 'Michael Karageorge', 'Petronius Arbiter Kingsley', 'P. A. Kingsley', 'H. P. Lovecraft', 'Lois McMaster Bujold', 'Bernard Stiegler', 'Aaron Williamon', 'Stephen King', 'Oliver Goldsmith', 'Donald E. Westlake', 'Giovanni Battista Angioletti', 'Lydia Sigourney', 'Friedrich Hölderlin', 'Howard B. Eichenbaum', 'Margaret Mahy', 'Rob Bidder']",short story by Poul Anderson,Who is the author of the short story Memory?,True,Who wrote the short story Memory?
5818151,Stations,author,Seamus Heaney,2604036,484,2899244,[],"[""Seamus Justin Heaney"",""Seamus Heaney""]",http://www.wikidata.org/entity/Q7604358,http://www.wikidata.org/entity/Q93356,Stations (poetry collection),Seamus Heaney,85,21738,Who is the author of Stations?,"[""Seamus Heaney"", ""Seamus Justin Heaney"", ""Seamus Heaney""]",8,2,"['Seamus Heaney', 'Seamus Justin Heaney', 'Clare McCallan']",book by Seamus Heaney,"Who is the author of the book, Stations?",True,"""Who wrote the book, Stations?"""
6039129,Trust Me,author,John Updike,2712706,484,32973,[],"[""John Hoyer Updike""]",http://www.wikidata.org/entity/Q7848118,http://www.wikidata.org/entity/Q105756,Trust Me (short story collection),John Updike,67,21723,Who is the author of Trust Me?,"[""John Updike"", ""John Hoyer Updike""]",37,2,"['John Updike', 'John Hoyer Updike', 'Rajashree']",book by John Updike,Who is the author of the book Trust Me?,True,Who wrote the book Trust Me?
5549934,Recursion,author,Tony Ballantyne,2473225,484,2701786,[],[],http://www.wikidata.org/entity/Q7303332,http://www.wikidata.org/entity/Q7821816,Recursion (novel),Tony Ballantyne,97,301,Who is the author of Recursion?,"[""Tony Ballantyne""]",4,3,"['Tony Ballantyne', 'Blake Crouch']",book by Tony Ballantyne,Who is the author of the book Recursion?,True,Who wrote the book Recursion?
5878640,Talent,author,Christopher Golden,2634057,484,701317,[],[],http://www.wikidata.org/entity/Q7679176,http://www.wikidata.org/entity/Q1995311,Talent (comics),Christopher Golden,97,1187,Who is the author of Talent?,"[""Christopher Golden""]",20,3,"['Christopher Golden', 'Anton Chekhov']",comics,Who is the author of Talent?,False,Who wrote Talent?
5727009,Shadow,author,Dean Wesley Smith,2556217,484,106308,"[""Shadow""]",[],http://www.wikidata.org/entity/Q7460439,http://www.wikidata.org/entity/Q1181143,Shadow (Star Trek),Dean Wesley Smith,14,414,Who is the author of Shadow?,"[""Dean Wesley Smith"", ""Kristine Kathryn Rusch""]",63,9,"['Dean Wesley Smith', 'Kristine Kathryn Rusch', 'Evgeny Shvarts', 'Erin Hunter', 'Edgar Allan Poe', 'Marcia Brown', 'Bob Woodward', 'Juhan Liiv']",Star Trek: Section 31 novel,"Who is the author of the Star Trek: Section 31 novel, Shadow?",True,Who is the author of the Star Trek: Section 31 novel?
5614635,Robots,author,Jack Dann,2501782,484,2028127,[],"[""Jack M. Dann"",""Jack Mayo Dann""]",http://www.wikidata.org/entity/Q7353464,http://www.wikidata.org/entity/Q6112002,Robots (anthology),Jack Dann,70,455,Who is the author of Robots?,"[""Jack Dann"", ""Jack M. Dann"", ""Jack Mayo Dann"", ""Gardner Dozois"", ""Gardner Raymond Dozois""]",18,2,"['Jack Dann', 'Jack M. Dann', 'Jack Mayo Dann', 'Gardner Dozois', 'Gardner Raymond Dozois', 'Randall Munroe', 'Amardeep Singh']",book by Jack Dann,Who is the author of the book Robots?,True,Who wrote the book Robots?
2456925,Shame,author,Karin Alvtegen,1064150,484,876626,[],[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q260420,Shame (Alvtegen novel),Karin Alvtegen,60,255,Who is the author of Shame?,"[""Karin Alvtegen""]",58,7,"['Karin Alvtegen', 'Larry Dossey', 'Dominick Gamache', 'Jennifer Biddle', 'Gavin Brent Sullivan', 'Salman Rushdie']",novel by Swedish crime-writer Karin Alvtegen,"Who is the author of Shame, a novel by the Swedish crime-writer?",True,"Who is the author of Shame, a novel by the Swedish writer?"
5914869,The Burning,author,Justin Richards,2652212,484,2103673,[],"[""Justin C Richards""]",http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/********,The Burning (novel),Justin Richards,70,485,Who is the author of The Burning?,"[""Justin Richards"", ""Justin C Richards""]",17,4,"['Justin Richards', 'Justin C Richards', 'Nancy Holder', 'James Gunn', 'Stewart Conn', 'Jeff Mariotte']",book by Justin Richards,"Who is the author of the book, The Burning?",True,"Who wrote the book, *The Burning*?"
5930682,The Guard,author,Ezzat el Kamhawi,2660274,484,824504,[],[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/********,The Guard (novel),Ezzat el Kamhawi,43,115,Who is the author of The Guard?,"[""Ezzat el Kamhawi""]",17,2,"['Ezzat el Kamhawi', 'Peter Terrin']",novel by Ezzat el Kamhawi,"Who is the author of the novel, The Guard?",True,Who is the author of the novel titled *The Guard*?
6157088,West,author,Orson Scott Card,2775541,484,754904,[],"[""Brian Green"",""Frederick Bliss"",""Byron Walley"",""Scott Richards"",""Dinah Kirkham"",""P.Q. Gump"",""Byron S. Walley""]",http://www.wikidata.org/entity/Q7984206,http://www.wikidata.org/entity/Q217110,West (short story),Orson Scott Card,73,30166,Who is the author of West?,"[""Orson Scott Card"", ""Brian Green"", ""Frederick Bliss"", ""Byron Walley"", ""Scott Richards"", ""Dinah Kirkham"", ""P.Q. Gump"", ""Byron S. Walley""]",44,2,"['Orson Scott Card', 'Brian Green', 'Frederick Bliss', 'Byron Walley', 'Scott Richards', 'Dinah Kirkham', 'P.Q. Gump', 'Byron S. Walley', 'Edith Pattou', 'Julia Franck']",short story by Orson Scott Card,"Who is the author of the short story, West?",True,"Who wrote the short story, *West*?"
5727010,Shadow,author,Kristine Kathryn Rusch,2556217,484,1321316,"[""Shadow""]",[],http://www.wikidata.org/entity/Q7460439,http://www.wikidata.org/entity/Q446973,Shadow (Star Trek),Kristine Kathryn Rusch,14,787,Who is the author of Shadow?,"[""Dean Wesley Smith"", ""Kristine Kathryn Rusch""]",63,9,"['Dean Wesley Smith', 'Kristine Kathryn Rusch', 'Evgeny Shvarts', 'Erin Hunter', 'Edgar Allan Poe', 'Marcia Brown', 'Bob Woodward', 'Juhan Liiv']",Star Trek: Section 31 novel,"Who is the author of the Star Trek: Section 31 novel, Shadow?",True,Who is the author of the Star Trek: Section 31 novel?
5562162,Responsibility,author,Nigel Cox,2478931,484,699331,[],[],http://www.wikidata.org/entity/Q7315981,http://www.wikidata.org/entity/Q1989531,Responsibility (novel),Nigel Cox (author),68,122,Who is the author of Responsibility?,"[""Nigel Cox""]",20,3,"['Nigel Cox', 'Barbara Prainsack', 'Sabina Leonelli', 'Antony Duff']",book by Nigel Cox,Who is the author of the book Responsibility?,True,Who wrote the book Responsibility?
6146167,Warrior,author,Jennifer Fallon,2769728,484,607574,[],[],http://www.wikidata.org/entity/Q7970758,http://www.wikidata.org/entity/Q182934,Warrior (Fallon novel),Jennifer Fallon,5,414,Who is the author of Warrior?,"[""Jennifer Fallon""]",66,3,"['Jennifer Fallon', 'Alan Moore', 'Marie Brennan']",2004 novel by Jennifer Fallon,"Who is the author of the 2004 novel, Warrior?",True,Who is the author of the 2004 novel?
1535277,Beyond,author,Chris Impey,686151,484,121668,[],"[""Christopher David Impey""]",http://www.wikidata.org/entity/Q19627375,http://www.wikidata.org/entity/Q12060704,Beyond (book),Chris Impey,93,442,Who is the author of Beyond?,"[""Chris Impey"", ""Christopher David Impey""]",41,5,"['Chris Impey', 'Christopher David Impey', 'Charles Michael Platt', 'Florence Earle Coates', 'Ron Marz', 'Joy Harjo']",book by Chris Impey,"Who is the author of the book, Beyond?",True,Who is the author of the book titled *Beyond*?
5946123,The Other Place,author,Monica Hughes,2668203,484,1314350,[],"[""Monica Mary Ince Hughes""]",http://www.wikidata.org/entity/Q7755512,http://www.wikidata.org/entity/Q4412561,The Other Place (novel),Monica Hughes,82,520,Who is the author of The Other Place?,"[""Monica Hughes"", ""Monica Mary Ince Hughes""]",10,5,"['Monica Hughes', 'Monica Mary Ince Hughes', 'J. B. Priestley', 'Ibrahim Abdel Meguid', 'Julie Wheelwright', 'Sharr White']",book by Monica Hughes,"Who is the author of the book, The Other Place?",True,"Who wrote the book, *The Other Place?*"
3950033,Down,author,Warren Ellis,1724813,484,1778905,[],"[""Warren Girard Ellis""]",http://www.wikidata.org/entity/Q5302779,http://www.wikidata.org/entity/Q540787,Down (comics),Warren Ellis,91,18145,Who is the author of Down?,"[""Warren Ellis"", ""Warren Girard Ellis""]",41,2,"['Warren Ellis', 'Warren Girard Ellis', 'Lawrence Miles']",four-issue American comic book limited series,"Who is the author of Down, the four-issue American comic book limited series?",True,"Who is the author of Down, the four-issue American comic book series?"
85538,Max,author,Barbro Lindgren,33105,484,1359454,[],[],http://www.wikidata.org/entity/Q10578682,http://www.wikidata.org/entity/Q462827,Max (book series),Barbro Lindgren,81,452,Who is the author of Max?,"[""Barbro Lindgren""]",90,5,"['Barbro Lindgren', 'Paolo Conte', 'Sarah Cohen-Scali', 'Günter Grass', 'Howard Fast']",children's book series by Barbro Lindgren,"Who is the author of the children's book series, Max?",True,Who is the author of the children's book series titled Max?
5962867,The Voice,author,Thomas Hardy,2676903,484,174163,[],[],http://www.wikidata.org/entity/Q7772986,http://www.wikidata.org/entity/Q132805,The Voice (poem),Thomas Hardy,77,46350,Who is the author of The Voice?,"[""Thomas Hardy""]",61,7,"['Thomas Hardy', 'Gabriel Okara', 'Forceythe Willson']",poem by Thomas Hardy,"Who is the author of the poem, The Voice?",True,"Who wrote the poem, The Voice?"
5996529,Time After Time,author,Allen Appel,2692535,484,1415075,[],[],http://www.wikidata.org/entity/Q7804907,http://www.wikidata.org/entity/Q4731507,Time After Time (Appel novel),Allen Appel,84,134,Who is the author of Time After Time?,"[""Allen Appel""]",35,2,"['Allen Appel', 'Karl Alexander']",1985 novel by Allen Appel,"Who is the author of the 1985 novel, Time After Time?",True,Who is the author of the 1985 novel?
3290608,Alice,author,Judith Hermann,1411598,484,2023965,[],[],http://www.wikidata.org/entity/Q4725696,http://www.wikidata.org/entity/Q60984,Alice (short story collection),Judith Hermann,44,562,Who is the author of Alice?,"[""Judith Hermann""]",282,4,"['Judith Hermann', 'Joseph Delaney', 'Emma Becker', 'Maria Amália Vaz de Carvalho']",Hermann book,Who is the author of the book Alice?,True,Who wrote the book Alice?
5773456,Skin,author,Mo Hayder,2578775,484,1759437,[],"[""Beatrice Clare Dunkel"",""Clare Damaris Bastin"",""Candy Davis"",""Theo Clare"",""Clare Dunkel""]",http://www.wikidata.org/entity/Q7535312,http://www.wikidata.org/entity/Q53672,Skin (Hayder novel),Mo Hayder,69,5673,Who is the author of Skin?,"[""Mo Hayder"", ""Beatrice Clare Dunkel"", ""Clare Damaris Bastin"", ""Candy Davis"", ""Theo Clare"", ""Clare Dunkel""]",94,4,"['Mo Hayder', 'Beatrice Clare Dunkel', 'Clare Damaris Bastin', 'Candy Davis', 'Theo Clare', 'Clare Dunkel', 'Ted Dekker', 'Isha Karki', 'Roald Dahl']",novel by Mo Hayder,Who is the author of the novel Skin?,True,Who wrote the novel Skin?
6029039,Transcension,author,Damien Broderick,2707272,484,93065,[],"[""Damien Francis Broderick"",""D. Broderick"",""D Broderick"",""Roger Delaney"",""Edgar Grieve"",""Jack Harding"",""Alan Harlison"",""Philip Jenkins"",""Horace West"",""Iago Yarrick"",""O'Flaherty Gribbles""]",http://www.wikidata.org/entity/Q7833881,http://www.wikidata.org/entity/Q1158494,Transcension (novel),Damien Broderick,42,683,Who is the author of Transcension?,"[""Damien Broderick"", ""Damien Francis Broderick"", ""D. Broderick"", ""D Broderick"", ""Roger Delaney"", ""Edgar Grieve"", ""Jack Harding"", ""Alan Harlison"", ""Philip Jenkins"", ""Horace West"", ""Iago Yarrick"", ""O'Flaherty Gribbles""]",3,2,"['Damien Broderick', 'Damien Francis Broderick', 'D. Broderick', 'D Broderick', 'Roger Delaney', 'Edgar Grieve', 'Jack Harding', 'Alan Harlison', 'Philip Jenkins', 'Horace West', 'Iago Yarrick', ""O'Flaherty Gribbles""]",book by Damien Broderick,Who is the author of the book Transcension?,True,Who wrote the book Transcension?
3921722,Dirt,author,Stuart Woods,1712118,484,1145478,[],[],http://www.wikidata.org/entity/Q5280926,http://www.wikidata.org/entity/Q3500849,Dirt (novel),Stuart Woods,97,5032,Who is the author of Dirt?,"[""Stuart Woods""]",24,3,"['Stuart Woods', 'Robert Schneider']",novel by Stuart Woods,Who is the author of the novel Dirt?,True,Who is the author of the novel titled Dirt?
5914872,The Burning,author,Stewart Conn,2652214,484,2610646,[],[],http://www.wikidata.org/entity/Q7720662,http://www.wikidata.org/entity/Q7615756,The Burning (play),Stewart Conn,93,121,Who is the author of The Burning?,"[""Stewart Conn""]",17,4,"['Stewart Conn', 'Justin Richards', 'Nancy Holder', 'James Gunn', 'Jeff Mariotte']",play written by Stewart Conn,Who is the author of the play titled The Burning?,True,Who is the author of the play titled *The Burning*?
5909447,The Aware,author,Glenda Larke,2649447,484,1024693,[],"[""Glenda Noramly""]",http://www.wikidata.org/entity/Q7715077,http://www.wikidata.org/entity/Q3108728,The Aware,Glenda Larke,74,130,Who is the author of The Aware?,"[""Glenda Larke"", ""Glenda Noramly""]",2,2,"['Glenda Larke', 'Glenda Noramly']",2003 novel by Glenda Larke,"Who is the author of the 2003 novel, The Aware?",True,"Who wrote the 2003 novel, *The Aware*?"
670516,Sin,author,Zakhar Prilepin,283593,484,307521,[],"[""Yevgeny Nikolayevich Prilepin""]",http://www.wikidata.org/entity/Q15042127,http://www.wikidata.org/entity/Q1530559,Sin (Prilepin novel),Zakhar Prilepin,92,1626,Who is the author of Sin?,"[""Zakhar Prilepin"", ""Yevgeny Nikolayevich Prilepin""]",35,2,"['Zakhar Prilepin', 'Yevgeny Nikolayevich Prilepin', 'F. Sionil José']",novel by  Zakhar Prilepin,Who is the author of the novel *Sin*?,True,Who wrote the novel *Sin*?
1156997,Weekend,author,William McIlvanney,511925,484,1056510,[],[],http://www.wikidata.org/entity/Q17034987,http://www.wikidata.org/entity/Q320327,Weekend (novel),William McIlvanney,75,4412,Who is the author of Weekend?,"[""William McIlvanney""]",46,4,"['William McIlvanney', 'Randall Munroe', 'Gore Vidal', 'Gerard Koerts']",novel by William McIlvanney,Who is the author of the novel Weekend?,True,Who wrote the novel Weekend?
4034856,Empire,author,H. Beam Piper,1763493,484,202938,[],"[""Henry Beam Piper"",""Horace Beam Piper"",""Herbert Beam Piper""]",http://www.wikidata.org/entity/Q5374012,http://www.wikidata.org/entity/Q1364100,Empire (H. Beam Piper book),H. Beam Piper,82,1857,Who is the author of Empire?,"[""H. Beam Piper"", ""Henry Beam Piper"", ""Horace Beam Piper"", ""Herbert Beam Piper""]",96,10,"['H. Beam Piper', 'Henry Beam Piper', 'Horace Beam Piper', 'Herbert Beam Piper', 'Michael Hardt', 'Barry Kitson', 'Niall Ferguson', 'Antonio Negri', 'Gore Vidal', 'Orson Scott Card', 'Steven Saylor', 'Igor Kordey', 'Mark Waid', 'Samuel R. Delany']",collection of short stories by H. Beam Piper,"Who is the author of the collection of short stories, Empire?",True,Who is the author of the collection of short stories titled Empire?
2727560,Abel,author,Vittorio Alfieri,1172725,484,979936,[],"[""Count Vittorio Alfieri""]",http://www.wikidata.org/entity/Q3603443,http://www.wikidata.org/entity/Q296244,Abele (opera),Vittorio Alfieri,64,1501,Who is the author of Abel?,"[""Vittorio Alfieri"", ""Count Vittorio Alfieri""]",38,2,"['Vittorio Alfieri', 'Count Vittorio Alfieri', 'Claudia Salvatori']",play written by Vittorio Alfieri,Who is the author of the play Abel?,True,Who wrote the play Abel?
3980147,Eclipse,author,James Swallow,1739998,484,2041609,[],[],http://www.wikidata.org/entity/Q5332987,http://www.wikidata.org/entity/Q6143871,Eclipse (Judge Dredd novel),James Swallow,69,1050,Who is the author of Eclipse?,"[""James Swallow""]",111,12,"['James Swallow', 'Erin Hunter', 'John Banville', 'Stephenie Meyer', 'K. A. Bedford', 'John Shirley', 'Dalton Trumbo', 'Ali Akbar Navis', 'Dora Sigerson Shorter']",original novel written by James Swallow,"Who is the author of the original novel, Eclipse?",True,"Who is the author of the novel, Eclipse?"
5962059,The Valley,author,Barry Pilton,2676449,484,1488880,[],[],http://www.wikidata.org/entity/Q7771937,http://www.wikidata.org/entity/Q4864611,The Valley (novel),Barry Pilton,65,107,Who is the author of The Valley?,"[""Barry Pilton""]",37,2,"['Barry Pilton', 'Josephine Spencer']",book by Barry Pilton,"Who is the author of the book, The Valley?",True,"Who wrote the book, *The Valley*?"
5957133,The Squirrel Wife,author,Philippa Pearce,2673881,484,1607650,[],"[""Ann Philippa Pearce""]",http://www.wikidata.org/entity/Q7766130,http://www.wikidata.org/entity/Q508765,The Squirrel Wife,Philippa Pearce,73,1041,Who is the author of The Squirrel Wife?,"[""Philippa Pearce"", ""Ann Philippa Pearce""]",2,2,"['Philippa Pearce', 'Ann Philippa Pearce']",children's fairy tale written by Philippa Pearce,"Who is the author of the children's fairy tale, The Squirrel Wife?",True,"Who is the author of the children's fairy tale, The Squirrel Wife?"
5222712,Moving Day,author,Ralph Fletcher,2313337,484,2464141,[],[],http://www.wikidata.org/entity/Q6927051,http://www.wikidata.org/entity/Q7287492,Moving Day (poetry collection),Ralph Fletcher,38,381,Who is the author of Moving Day?,"[""Ralph Fletcher""]",21,2,"['Ralph Fletcher', 'Hans Christian Andersen']",book by Ralph Fletcher,"Who is the author of the book, Moving Day?",True,"Who wrote the book, *Moving Day*?"
3406610,August,author,Gerard Woodward,1466204,484,1854100,[],[],http://www.wikidata.org/entity/Q4820487,http://www.wikidata.org/entity/Q5550203,August (Woodward novel),Gerard Woodward,59,149,Who is the author of August?,"[""Gerard Woodward""]",55,5,"['Gerard Woodward', 'Judith Rossner', 'Callan Wink', 'Knut Hamsun']",novel by author Gerard Woodward,Who is the author of the novel August?,True,"""Who wrote the novel August?"""
4868873,Kite,author,Melvin Burgess,2143752,484,2913325,[],[],http://www.wikidata.org/entity/Q6418112,http://www.wikidata.org/entity/Q951657,Kite (novel),Melvin Burgess,89,868,Who is the author of Kite?,"[""Melvin Burgess""]",36,3,"['Melvin Burgess', 'Kiyoshi Shigematsu', 'Randall Munroe']",novel by Melvin Burgess,Who is the author of the novel Kite?,True,Who wrote the novel Kite?
3519439,Black,author,Joyce Carol Oates,1520268,484,755881,[],[],http://www.wikidata.org/entity/Q4920234,http://www.wikidata.org/entity/Q217557,Black (play),Joyce Carol Oates,75,43252,Who is the author of Black?,"[""Joyce Carol Oates""]",64,3,"['Joyce Carol Oates', 'Dirk Bracke', 'Ted Dekker']",play by Joyce Carol Oates,"Who is the author of the play, Black?",True,"Who wrote the play, Black?"
5959353,The Test,author,Mary Tappan Wright,2675056,484,2258151,[],[],http://www.wikidata.org/entity/Q7768553,http://www.wikidata.org/entity/Q6780814,The Test (Wright novel),Mary Tappan Wright,53,110,Who is the author of The Test?,"[""Mary Tappan Wright""]",52,7,"['Mary Tappan Wright', 'Sylvain Neuvel', 'Katherine Applegate', 'Franz Kafka', 'Thomas Arkle Clark', 'Richard Matheson']",1904 novel by Mary Tappan Wright,"Who is the author of the 1904 novel, The Test?",True,Who is the author of the 1904 novel?
3846992,Darkness,author,Bharati Mukherjee,1678511,484,1306959,[],[],http://www.wikidata.org/entity/Q5223690,http://www.wikidata.org/entity/Q4357833,Darkness (short story collection),Bharati Mukherjee,56,2461,Who is the author of Darkness?,"[""Bharati Mukherjee""]",42,11,"['Bharati Mukherjee', 'Lord Byron', 'André Carneiro', 'Dacia Maraini', 'Vladimir Mayakovsky', 'Anton Chekhov', 'John Saul', 'Michael Jan Friedman', 'Randall Munroe']",collection of short stories by Bharati Mukherjee,Who is the author of the collection of short stories titled Darkness?,True,Who is the author of the collection of short stories titled *Darkness*?
5141442,Men and Women,author,David Belasco,2274096,484,1890018,[],[],http://www.wikidata.org/entity/Q6816239,http://www.wikidata.org/entity/Q562719,Men and Women (play),David Belasco,91,2961,Who is the author of Men and Women?,"[""David Belasco""]",8,2,"['David Belasco', 'Robert Browning', 'Henry C. De Mille']",1890 play written by David Belasco,Who is the author of the 1890 play Men and Women?,True,Who wrote the 1890 play Men and Women?
5342660,One More Time,author,Carol Burnett,2373630,484,862372,[],"[""Carol Creighton Burnett""]",http://www.wikidata.org/entity/Q7092984,http://www.wikidata.org/entity/Q255565,One More Time (book),Carol Burnett,98,104043,Who is the author of One More Time?,"[""Carol Burnett"", ""Carol Creighton Burnett""]",49,2,"['Carol Burnett', 'Carol Creighton Burnett', 'Brent Hayes Edwards']",book by Carol Burnett,Who is the author of the book One More Time?,True,Who wrote the book One More Time?
3424332,Baby,author,Kirsten Thorup,1475741,484,1352730,[],[],http://www.wikidata.org/entity/Q4838181,http://www.wikidata.org/entity/Q460626,Baby (Thorup novel),Kirsten Thorup,83,218,Who is the author of Baby?,"[""Kirsten Thorup""]",114,4,"['Kirsten Thorup', 'Patricia MacLachlan', 'Randall Munroe', 'Kathe Koja']",1973 novel by Danish author Kirsten Thorup,"Who is the author of the 1973 novel, Baby, written by the Danish author Kirsten Thorup?",True,"Who is the author of the 1973 novel, Baby, written by the Danish writer?"
5390803,Patience,author,Jason Sherman,2397608,484,2048733,[],"[""Jason Sherman""]",http://www.wikidata.org/entity/Q7144928,http://www.wikidata.org/entity/Q6163463,Patience (play),Jason Sherman,65,235,Who is the author of Patience?,"[""Jason Sherman"", ""Jason Sherman""]",64,3,"['Jason Sherman', 'Pearl poet', 'Jeffrey R. Stevens', 'David W. Stephens']",1998 play by Jason Sherman,"Who is the author of the 1998 play, Patience?",True,"Who wrote the 1998 play, Patience?"
1115292,Smoke,author,Lisa Unger,486767,484,1065921,[],"[""Lisa Miscione""]",http://www.wikidata.org/entity/Q16950203,http://www.wikidata.org/entity/Q3242453,Smoke (Miscione novel),Lisa Unger,37,1367,Who is the author of Smoke?,"[""Lisa Unger"", ""Lisa Miscione""]",43,6,"['Lisa Unger', 'Lisa Miscione', 'Ivan Turgenev', 'Kevin McIlvoy', 'Carl Sandburg', 'Henry David Thoreau']",novel by Lisa Unger,Who is the author of the novel Smoke?,True,Who is the author of the novel titled Smoke?
5930072,The Great Perhaps,author,Joe Meno,2659957,484,2065924,[],[],http://www.wikidata.org/entity/Q7737885,http://www.wikidata.org/entity/Q6211330,The Great Perhaps,Joe Meno,83,235,Who is the author of The Great Perhaps?,"[""Joe Meno""]",3,2,['Joe Meno'],novel by Joe Meno,"Who is the author of the novel, The Great Perhaps?",True,"Who is the author of the novel, *The Great Perhaps*?"
3237866,Against the Odds,author,Elizabeth Moon,1388957,484,824678,[],[],http://www.wikidata.org/entity/Q4691088,http://www.wikidata.org/entity/Q243027,Against the Odds (novel),Elizabeth Moon,69,2430,Who is the author of Against the Odds?,"[""Elizabeth Moon""]",15,5,"['Elizabeth Moon', 'Ben Igwe', 'Portia Arthur']",book by Elizabeth Moon,Who is the author of the book Against the Odds?,True,Who wrote the book *Against the Odds*?
5279632,New York,author,Anthony Burgess,2342576,484,756044,[],"[""John Anthony Burgess Wilson"",""John Burgess Wilson"",""Joseph Kell""]",http://www.wikidata.org/entity/Q7012981,http://www.wikidata.org/entity/Q217619,New York (Burgess book),Anthony Burgess,64,24098,Who is the author of New York?,"[""Anthony Burgess"", ""John Anthony Burgess Wilson"", ""John Burgess Wilson"", ""Joseph Kell""]",183,6,"['Anthony Burgess', 'John Anthony Burgess Wilson', 'John Burgess Wilson', 'Joseph Kell', 'Ameen Rihani', 'Edward Rutherfurd', 'Paul Morand', 'Don Marquis']",1976 book by Anthony Burgess,"Who is the author of the 1976 book, New York?",True,Who is the author of the 1976 book titled *New York*?
1081666,Challenge,author,H. C. McNeile,466099,484,1033490,[],"[""Herman Cyril McNeile"",""Cyril McNeile"",""Sapper""]",http://www.wikidata.org/entity/Q16858896,http://www.wikidata.org/entity/Q3134064,Challenge (novel),H. C. McNeile,77,1231,Who is the author of Challenge?,"[""H. C. McNeile"", ""Herman Cyril McNeile"", ""Cyril McNeile"", ""Sapper""]",70,2,"['H. C. McNeile', 'Herman Cyril McNeile', 'Cyril McNeile', 'Sapper', 'Vita Sackville-West']",book by H. C. McNeile,Who is the author of the book Challenge?,True,Who wrote the book Challenge?
3954118,Dreams,author,Ivan Bunin,1726890,484,1371488,[],"[""Ivan Alekseyevich Bunin""]",http://www.wikidata.org/entity/Q5306683,http://www.wikidata.org/entity/Q46602,Dreams (Ivan Bunin),Ivan Bunin,61,5790,Who is the author of Dreams?,"[""Ivan Bunin"", ""Ivan Alekseyevich Bunin""]",119,10,"['Ivan Bunin', 'Ivan Alekseyevich Bunin', 'Anne Brontë', 'Rainer Maria Rilke', 'Paul Laurence Dunbar', 'Randall Munroe', 'Anton Chekhov', 'Olive Schreiner', 'Josephine Spencer', 'Carl Jung']",novella by Ivan Bunin,Who is the author of the novella Dreams?,True,Who wrote the novella Dreams?
4089904,Falling,author,Anne Provoost,1789640,484,1372761,"[""Vallen""]",[],http://www.wikidata.org/entity/Q5432294,http://www.wikidata.org/entity/Q466234,Falling (Provoost novel),Anne Provoost,83,201,Who is the author of Falling?,"[""Anne Provoost""]",56,3,"['Anne Provoost', 'T. J. Newman', 'Elizabeth Jane Howard', 'Colin Thubron']",novel by the Flemish author Anne Provoost,"Who is the author of Falling, the novel by the Flemish writer?",True,"Who is the author of *Falling*, the novel by the Flemish individual?"
5931767,The Hero,author,Rabindranath Tagore,2660843,484,2441594,[],"[""Rab\u012bndran\u0101tha Th\u0101kur"",""Kabiguru"",""Tagore"",""Bishwakabi"",""R. Tagore"",""Rabindranat Tagor"",""Bhanu Singha Thakur"",""Gurudev"",""Biswakabi"",""Nyi Wang G\u00f6npo"",""Tagore, rabindranath""]",http://www.wikidata.org/entity/Q7739487,http://www.wikidata.org/entity/Q7241,The Hero (poem),Rabindranath Tagore,22,189626,Who is the author of The Hero?,"[""Rabindranath Tagore"", ""Rabīndranātha Thākur"", ""Kabiguru"", ""Tagore"", ""Bishwakabi"", ""R. Tagore"", ""Rabindranat Tagor"", ""Bhanu Singha Thakur"", ""Gurudev"", ""Biswakabi"", ""Nyi Wang Gönpo"", ""Tagore, rabindranath""]",48,6,"['Rabindranath Tagore', 'Rabīndranātha Thākur', 'Kabiguru', 'Tagore', 'Bishwakabi', 'R. Tagore', 'Rabindranat Tagor', 'Bhanu Singha Thakur', 'Gurudev', 'Biswakabi', 'Nyi Wang Gönpo', 'Tagore, rabindranath', 'Karl Schroeder', 'George R. R. Martin', 'Michael Z. Williamson', 'John Ringo', 'Lee Child', 'Siegfried Sassoon']",poem written by Rabindranath Tagore,"Who is the author of the poem, The Hero?",True,"Who wrote the poem, The Hero?"
5923312,The Economics and Ethics of Private Property,author,Hans-Hermann Hoppe,2656538,484,2630365,[],[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q76688,The Economics and Ethics of Private Property,Hans-Hermann Hoppe,62,13449,Who is the author of The Economics and Ethics of Private Property?,"[""Hans-Hermann Hoppe""]",2,2,['Hans-Hermann Hoppe'],book by Hans-Hermann Hoppe,"Who is the author of the book, The Economics and Ethics of Private Property?",True,"Who is the author of the book, *The Economics and Ethics of Private Property*?"
5941833,The Middle Years,author,Henry James,2665897,484,514507,[],"[""Henricus James""]",http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q170509,The Middle Years (autobiography),Henry James,90,42955,Who is the author of The Middle Years?,"[""Henry James"", ""Henricus James""]",4,2,"['Henry James', 'Henricus James']",book by Henry James,"Who is the author of the book, The Middle Years?",True,"Who wrote the book, *The Middle Years*?"
5561986,Resistance,author,Mike Costa,2478836,484,2287634,[],[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/********,Resistance (comics),Mike Costa,59,1094,Who is the author of Resistance?,"[""Mike Costa""]",50,6,"['Mike Costa', 'Jeanne Kalogridis', 'Val McDermid', 'Steve Lyons', 'Owen Sheers']",comic book series published by Wildstorm,"Who is the author of Resistance, the comic book series published by Wildstorm?",True,"Who is the author of Resistance, the comic book series published by Wildstorm?"
5409905,Pearl,author,John Arden,2405147,484,1856701,[],[],http://www.wikidata.org/entity/********,http://www.wikidata.org/entity/Q555621,Pearl (radio play),John Arden,70,1033,Who is the author of Pearl?,"[""John Arden""]",73,2,"['John Arden', 'Pearl poet']",1978 radio play by John Arden,"Who is the author of the 1978 radio play, Pearl?",True,Who is the author of the 1978 radio play?
5744353,Shift,author,Tim Kring,2564416,484,1053486,[],"[""Richard Timothy \""Tim\"" Kring"",""Richard Timothy Kring""]",http://www.wikidata.org/entity/Q7496236,http://www.wikidata.org/entity/Q319389,Shift (novel),Tim Kring,65,3364,Who is the author of Shift?,"[""Dale Peck"", ""Tim Kring"", ""Richard Timothy \""Tim\"" Kring"", ""Richard Timothy Kring""]",32,3,"['Dale Peck', 'Tim Kring', 'Richard Timothy ""Tim"" Kring', 'Richard Timothy Kring', 'Hugh Howey', 'Walter F. Lamacki']",book by Tim Kring,Who is the author of the book Shift?,True,Who wrote the book Shift?
4098367,Federation,author,H. Beam Piper,1794207,484,202938,[],"[""Henry Beam Piper"",""Horace Beam Piper"",""Herbert Beam Piper""]",http://www.wikidata.org/entity/Q5440718,http://www.wikidata.org/entity/Q1364100,Federation (short story collection),H. Beam Piper,41,1857,Who is the author of Federation?,"[""H. Beam Piper"", ""Henry Beam Piper"", ""Horace Beam Piper"", ""Herbert Beam Piper""]",12,3,"['H. Beam Piper', 'Henry Beam Piper', 'Horace Beam Piper', 'Herbert Beam Piper', 'Judith Reeves-Stevens', 'Garfield Reeves-Stevens']",collection of short stories by H. Beam Piper,Who is the author of the collection of short stories titled Federation?,True,Who is the author of the collection of short stories titled *Federation*?
5969822,Therapy,author,Jonathan Kellerman,2680149,484,189232,[],"[""Jonathan Seth Kellerman""]",http://www.wikidata.org/entity/Q7782537,http://www.wikidata.org/entity/Q1349245,Therapy (Kellerman novel),Jonathan Kellerman,51,3362,Who is the author of Therapy?,"[""Jonathan Kellerman"", ""Jonathan Seth Kellerman""]",39,6,"['Jonathan Kellerman', 'Jonathan Seth Kellerman', 'Sebastian Fitzek', 'Eun-So Lee', 'David Lodge', 'Rob Bidder']",novel by Jonathan Kellerman published in 2004,"Who is the author of Therapy, the novel published in 2004?",True,"Who is the author of the novel Therapy, published in 2004?"
5347073,Opus,author,Michael Hollinger,2375895,484,2280237,[],[],http://www.wikidata.org/entity/Q7099098,http://www.wikidata.org/entity/Q6831208,Opus (play),Michael Hollinger,92,111,Who is the author of Opus?,"[""Michael Hollinger""]",31,2,"['Michael Hollinger', 'Satoshi Kon']",2006 play written by Michael Hollinger,"Who is the author of the 2006 play, Opus?",True,"Who wrote the 2006 play, Opus?"
5488469,Prime Time,author,Mike Tucker,2442600,484,2289252,[],[],http://www.wikidata.org/entity/Q7243350,http://www.wikidata.org/entity/Q6849100,Prime Time (novel),Mike Tucker,73,261,Who is the author of Prime Time?,"[""Mike Tucker""]",25,2,"['Mike Tucker', 'Liza Marklund']",2000 novel by Mike Tucker,"Who is the author of the 2000 novel, Prime Time?",True,Who is the author of the 2000 novel titled *Prime Time*?
3434761,Balance of Power,author,Dafydd ab Hugh,1480893,484,1282166,"[""Balance of Power""]",[],http://www.wikidata.org/entity/Q4849966,http://www.wikidata.org/entity/Q4172119,Balance of Power (Star Trek),Dafydd ab Hugh,83,407,Who is the author of Balance of Power?,"[""Dafydd ab Hugh""]",12,3,"['Dafydd ab Hugh', 'Brian Stableford', 'Daffyd Ab Hugh']",book by Dafydd ab Hugh,Who is the author of the book Balance of Power?,True,Who wrote the book Balance of Power?
1338998,Panic,author,Archibald MacLeish,595988,484,2108558,[],[],http://www.wikidata.org/entity/Q18158198,http://www.wikidata.org/entity/Q633354,Panic (play),Archibald MacLeish,75,3915,Who is the author of Panic?,"[""Archibald MacLeish""]",49,6,"['Archibald MacLeish', 'Gosho Aoyama', 'Takeshi Kaikō', 'Jeff Abbott', 'Alfonso Vallejo']",1935 play by Archibald MacLeish,"Who is the author of the 1935 play, Panic?",True,"Who is the writer of the 1935 play, Panic?"
1127226,The Lie,author,Georges Sari,494780,484,1852422,[],[],http://www.wikidata.org/entity/Q16975931,http://www.wikidata.org/entity/Q5546893,The Lie (novel),Georges Sari,5,433,Who is the author of The Lie?,"[""Georges Sari""]",29,5,"['Georges Sari', 'Kurt Vonnegut', 'Leonid Andreyev', 'Grete Meisel-Hess']",book by Georges Sari,"Who is the author of the book, The Lie?",True,"Who wrote the book, *The Lie*?"
5504958,Pursuit,author,Andy Mangels,2451382,484,1432947,[],[],http://www.wikidata.org/entity/Q7261575,http://www.wikidata.org/entity/Q4760985,Pursuit (novel),Andy Mangels,38,500,Who is the author of Pursuit?,"[""Andy Mangels""]",30,2,"['Andy Mangels', 'Joyce Carol Oates', 'Michael A. Martin']",Andy Mangels novel,"Who is the author of the novel, Pursuit?",True,Who is the author of the novel titled *Pursuit*?
4554689,Incoming,author,Andrew Motion,2008339,484,1593991,[],"[""Sir Andrew Motion""]",http://www.wikidata.org/entity/Q6015115,http://www.wikidata.org/entity/Q506410,Incoming (play),Andrew Motion,14,2707,Who is the author of Incoming?,"[""Andrew Motion"", ""Sir Andrew Motion""]",10,2,"['Andrew Motion', 'Sir Andrew Motion', 'Adam Pettle']",play written by Andrew Motion,Who is the author of the play Incoming?,True,Who wrote the play Incoming?
3776335,"Conan, Lord of the Black River",author,Leonard Carpenter,1643818,484,861991,[],"[""Leonard Paul Carpenter""]",http://www.wikidata.org/entity/Q5158100,http://www.wikidata.org/entity/Q2553892,"Conan, Lord of the Black River",Leonard Carpenter,97,203,"Who is the author of Conan, Lord of the Black River?","[""Leonard Carpenter"", ""Leonard Paul Carpenter""]",2,2,"['Leonard Carpenter', 'Leonard Paul Carpenter']",novel by Leonard Carpenter,"Who is the author of the novel Conan, Lord of the Black River?",True,"Who wrote the novel Conan, Lord of the Black River?"
6259298,Beast,author,Ally Kennen,2826314,484,891957,[],[],http://www.wikidata.org/entity/Q813042,http://www.wikidata.org/entity/Q2649517,Beast (Kennen novel),Ally Kennen,82,139,Who is the author of Beast?,"[""Ally Kennen""]",48,4,"['Ally Kennen', 'Peter Benchley', 'Irene Solà']",young adult novel by Ally Kennen,Who is the author of the young adult novel Beast?,True,Who is the author of the young adult novel titled Beast?
3788989,Corridor,author,Alfian Sa'at,1650357,484,135028,[],"[""Alfian bin Sa'at""]",http://www.wikidata.org/entity/Q5172934,http://www.wikidata.org/entity/Q1237904,Corridor (short story collection),Alfian Sa'at,71,1247,Who is the author of Corridor?,"[""Alfian Sa'at"", ""Alfian bin Sa'at""]",18,3,"[""Alfian Sa'at"", ""Alfian bin Sa'at"", 'Sarnath Banerjee']",book by Alfian Sa'at,Who is the author of the book Corridor?,True,Who wrote the book Corridor?
4077484,Everything,author,Henry Rollins,1783441,484,1051046,[],"[""Henry Garfield"",""Henry Lawrence Garfield""]",http://www.wikidata.org/entity/Q5417952,http://www.wikidata.org/entity/Q318509,Everything (Henry Rollins album),Henry Rollins,90,83308,Who is the author of Everything?,"[""Henry Rollins"", ""Henry Garfield"", ""Henry Lawrence Garfield""]",49,2,"['Henry Rollins', 'Henry Garfield', 'Henry Lawrence Garfield', 'Randall Munroe']",1997 spoken word album by Henry Rollins; audiobook,"Who is the author of *Everything*, the 1997 spoken word album and audiobook?",True,Who is the author of the 1997 spoken word album and audiobook titled *Everything*?
2805229,Find Me,author,Rosie O'Donnell,1204182,484,918549,[],"[""Roseann O'Donnell"",""Roseann Teresa O'Donnell"",""Roseanne Teresa O'Donnell""]",http://www.wikidata.org/entity/Q3745660,http://www.wikidata.org/entity/Q272929,Find Me (book),Rosie O'Donnell,20,69451,Who is the author of Find Me?,"[""Rosie O'Donnell"", ""Roseann O'Donnell"", ""Roseann Teresa O'Donnell"", ""Roseanne Teresa O'Donnell""]",11,3,"[""Rosie O'Donnell"", ""Roseann O'Donnell"", ""Roseann Teresa O'Donnell"", ""Roseanne Teresa O'Donnell"", 'Tahereh Mafi', 'André Aciman']",memoir written by Rosie O'Donnell,Who is the author of the memoir titled Find Me?,True,Who is the author of the memoir titled *Find Me*?
5847596,Suicide,author,Viktor Suvorov,2618238,484,163322,[],[],http://www.wikidata.org/entity/Q7635319,http://www.wikidata.org/entity/Q130786,Suicide (Suvorov book),Viktor Suvorov,45,3535,Who is the author of Suicide?,"[""Viktor Suvorov""]",78,10,"['Viktor Suvorov', 'Scott B. Patten', 'Annette Beautrais', 'Kimberly A. Van Orden', 'Anthony Busuttil', 'Émile Durkheim', 'Seena Fazel', 'Edouard Levé', 'Thomas Joiner', 'Norwood East']",book by Viktor Suvorov,Who is the author of the book titled *Suicide*?,True,Who is the author of the book *Suicide*?
1130523,On the Road,author,Jimmie Johnson,496873,484,755225,[],"[""Jimmie Kenneth Johnson""]",http://www.wikidata.org/entity/Q16983176,http://www.wikidata.org/entity/Q217238,On the Road (Johnson book),Jimmie Johnson,51,27013,Who is the author of On the Road?,"[""Jimmie Johnson"", ""Jimmie Kenneth Johnson""]",39,3,"['Jimmie Johnson', 'Jimmie Kenneth Johnson', 'Jack Kerouac', 'Anton Chekhov']",book by Jimmie Johnson,Who is the author of the book On the Road?,True,Who wrote the book On the Road?
5946292,The Outing,author,James Baldwin,2668293,484,919251,[],"[""James Arthur Baldwin""]",http://www.wikidata.org/entity/Q7755661,http://www.wikidata.org/entity/Q273210,The Outing (short story),James Baldwin,60,112514,Who is the author of The Outing?,"[""James Baldwin"", ""James Arthur Baldwin""]",5,2,"['James Baldwin', 'James Arthur Baldwin', 'Dylan Thomas']",short story by James Baldwin,"Who is the author of the short story, The Outing?",True,"Who wrote the short story, *The Outing*?"
468396,Louis,mother,Rotrude,191004,292,905421,[],"[""Hruodrud""]",http://www.wikidata.org/entity/Q1351496,http://www.wikidata.org/entity/Q269691,Louis (abbot of Saint-Denis),Rotrude,72,707,Who is the mother of Louis?,"[""Rotrude"", ""Hruodrud""]",32,3,"['Rotrude', 'Hruodrud', 'Liutgard of Saxony', 'Catherine of Vendôme']",abbot of Saint-Denis (9th c.),"Who is the mother of Louis, the abbot of Saint-Denis in the 9th century?",True,"Who is the mother of Louis, the abbot of Saint-Denis in the 9th century?"
2091805,Sal,capital,Espargos,916062,422,1556167,"[""Concelho do Sal"",""Sal Municipality""]",[],http://www.wikidata.org/entity/Q2721003,http://www.wikidata.org/entity/Q498968,"Sal, Cape Verde (municipality)",Espargos,30,907,What is the capital of Sal?,"[""Espargos""]",17,2,['Espargos'],"municipality on the island of Sal, Cape Verde","What is the capital of Sal, a municipality on the island of Sal in Cape Verde?",True,"What is the capital of Sal, a municipality in Cape Verde?"
