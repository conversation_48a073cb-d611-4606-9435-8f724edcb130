#!/bin/bash
#SBATCH --job-name=llama_baseline_no_retrieval
#SBATCH --output=slurm_outputs/llama_baseline_no_retrieval_output.txt
#SBATCH --error=slurm_outputs/llama_baseline_no_retrieval_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=48:00:00
#SBATCH --partition=ampere


conda activate selfrag

# Qwen 7B for original 
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
--model_name Qwen/Qwen2.5-7B-Instruct \
--download_dir /home/<USER>/.cache/huggingface/hub \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve20_contrievermsm.json \
--max_new_tokens 100 \
--metric match \
--result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_qwen2_7b_instruct_no_retrieval.json \
--task qa \
--prompt_name "prompt_no_input"

# Qwen 7B for disambiguated version
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
--model_name Qwen/Qwen2.5-7B-Instruct \
--download_dir /home/<USER>/.cache/huggingface/hub \
--input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_disambiguated_retrieve20_contrievermsm.json \
--max_new_tokens 100 \
--metric match \
--result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_disambig_qwen2_7b_instruct_no_retrieval.json \
--task qa \
--prompt_name "prompt_no_input"