
Loading safetensors checkpoint shards:   0% Completed | 0/4 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:  25% Completed | 1/4 [00:12<00:38, 12.84s/it]

Loading safetensors checkpoint shards:  50% Completed | 2/4 [00:25<00:25, 12.96s/it]

Loading safetensors checkpoint shards:  75% Completed | 3/4 [00:37<00:12, 12.51s/it]

Loading safetensors checkpoint shards: 100% Completed | 4/4 [00:51<00:00, 12.91s/it]

Loading safetensors checkpoint shards: 100% Completed | 4/4 [00:51<00:00, 12.85s/it]


  0%|          | 0/279 [00:00<?, ?it/s]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.42s/it, est. speed input: 5.80 toks/s, output: 41.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.07it/s, est. speed input: 29.40 toks/s, output: 207.01 toks/s]

  0%|          | 1/279 [00:02<11:13,  2.42s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 5.99 toks/s, output: 42.75 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 31.63 toks/s, output: 213.74 toks/s]

  1%|          | 2/279 [00:04<10:57,  2.37s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 6.85 toks/s, output: 42.80 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 31.67 toks/s, output: 213.96 toks/s]

  1%|          | 3/279 [00:07<10:50,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.27s/it, est. speed input: 11.77 toks/s, output: 42.36 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.15s/it, est. speed input: 15.46 toks/s, output: 66.12 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.15it/s, est. speed input: 33.05 toks/s, output: 194.89 toks/s]

  1%|▏         | 4/279 [00:09<10:45,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 6.84 toks/s, output: 42.77 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 33.36 toks/s, output: 213.85 toks/s]

  2%|▏         | 5/279 [00:11<10:42,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 7.70 toks/s, output: 42.77 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 32.93 toks/s, output: 213.80 toks/s]

  2%|▏         | 6/279 [00:14<10:39,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 5.99 toks/s, output: 42.76 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 32.49 toks/s, output: 213.76 toks/s]

  3%|▎         | 7/279 [00:16<10:37,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 7.70 toks/s, output: 42.75 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 32.06 toks/s, output: 213.73 toks/s]

  3%|▎         | 8/279 [00:18<10:34,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 6.84 toks/s, output: 42.75 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 31.63 toks/s, output: 213.72 toks/s]

  3%|▎         | 9/279 [00:21<10:32,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 6.83 toks/s, output: 42.71 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 32.03 toks/s, output: 213.53 toks/s]

  4%|▎         | 10/279 [00:23<10:30,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 6.41 toks/s, output: 42.73 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 33.75 toks/s, output: 213.62 toks/s]

  4%|▍         | 11/279 [00:25<10:27,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 6.84 toks/s, output: 42.74 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 32.48 toks/s, output: 213.67 toks/s]

  4%|▍         | 12/279 [00:28<10:25,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 5.98 toks/s, output: 42.73 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 30.34 toks/s, output: 213.63 toks/s]

  5%|▍         | 13/279 [00:30<10:23,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 6.41 toks/s, output: 42.72 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 33.74 toks/s, output: 213.57 toks/s]

  5%|▌         | 14/279 [00:32<10:20,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 8.53 toks/s, output: 42.67 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 35.41 toks/s, output: 213.33 toks/s]

  5%|▌         | 15/279 [00:35<10:18,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 5.55 toks/s, output: 42.66 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 34.12 toks/s, output: 213.26 toks/s]

  6%|▌         | 16/279 [00:37<10:16,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 6.40 toks/s, output: 42.68 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 32.43 toks/s, output: 213.36 toks/s]

  6%|▌         | 17/279 [00:39<10:14,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 6.40 toks/s, output: 42.68 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 32.01 toks/s, output: 213.39 toks/s]

  6%|▋         | 18/279 [00:42<10:11,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 5.97 toks/s, output: 42.67 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 32.00 toks/s, output: 213.33 toks/s]

  7%|▋         | 19/279 [00:44<10:09,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 5.97 toks/s, output: 42.67 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 34.56 toks/s, output: 213.30 toks/s]

  7%|▋         | 20/279 [00:46<10:07,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.34s/it, est. speed input: 6.83 toks/s, output: 42.66 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 31.57 toks/s, output: 213.28 toks/s]

  8%|▊         | 21/279 [00:49<10:05,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.10 toks/s, output: 42.63 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 35.38 toks/s, output: 213.10 toks/s]

  8%|▊         | 22/279 [00:51<10:02,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:03,  1.23it/s, est. speed input: 18.41 toks/s, output: 41.73 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.23s/it, est. speed input: 14.59 toks/s, output: 57.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 33.03 toks/s, output: 186.19 toks/s]

  8%|▊         | 23/279 [00:53<09:59,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.39 toks/s, output: 42.63 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 35.81 toks/s, output: 213.14 toks/s]

  9%|▊         | 24/279 [00:56<09:57,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.54 toks/s, output: 42.62 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 33.24 toks/s, output: 213.07 toks/s]

  9%|▉         | 25/279 [00:58<09:55,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.82 toks/s, output: 42.62 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 34.52 toks/s, output: 213.08 toks/s]

  9%|▉         | 26/279 [01:00<09:53,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.67 toks/s, output: 42.63 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 32.82 toks/s, output: 213.11 toks/s]

 10%|▉         | 27/279 [01:03<09:51,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.82 toks/s, output: 42.60 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 36.21 toks/s, output: 212.98 toks/s]

 10%|█         | 28/279 [01:05<09:49,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.25 toks/s, output: 42.62 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 35.37 toks/s, output: 213.07 toks/s]

 10%|█         | 29/279 [01:08<09:46,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.39 toks/s, output: 42.60 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 33.65 toks/s, output: 213.00 toks/s]

 11%|█         | 30/279 [01:10<09:44,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.39 toks/s, output: 42.60 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 34.50 toks/s, output: 212.96 toks/s]

 11%|█         | 31/279 [01:12<09:42,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:03,  1.26it/s, est. speed input: 20.17 toks/s, output: 41.61 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.23s/it, est. speed input: 12.85 toks/s, output: 56.98 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 33.84 toks/s, output: 185.48 toks/s]

 11%|█▏        | 32/279 [01:15<09:39,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.53 toks/s, output: 42.57 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 32.35 toks/s, output: 212.83 toks/s]

 12%|█▏        | 33/279 [01:17<09:37,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:04,  1.07s/it, est. speed input: 13.07 toks/s, output: 42.02 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.19s/it, est. speed input: 11.99 toks/s, output: 62.07 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 33.39 toks/s, output: 190.48 toks/s]

 12%|█▏        | 34/279 [01:19<09:34,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.81 toks/s, output: 42.56 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 35.75 toks/s, output: 212.80 toks/s]

 13%|█▎        | 35/279 [01:22<09:32,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.96 toks/s, output: 42.57 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 34.05 toks/s, output: 212.81 toks/s]

 13%|█▎        | 36/279 [01:24<09:30,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.81 toks/s, output: 42.56 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 33.20 toks/s, output: 212.80 toks/s]

 13%|█▎        | 37/279 [01:26<09:28,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:02,  1.34it/s, est. speed input: 21.42 toks/s, output: 41.51 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:00<00:01,  2.57it/s, est. speed input: 44.07 toks/s, output: 76.83 toks/s][A

Processed prompts:  60%|██████    | 3/5 [00:02<00:01,  1.15it/s, est. speed input: 23.63 toks/s, output: 72.17 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.15it/s, est. speed input: 38.66 toks/s, output: 158.08 toks/s]

 14%|█▎        | 38/279 [01:29<09:24,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:02,  1.53it/s, est. speed input: 22.94 toks/s, output: 41.28 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.26s/it, est. speed input: 13.71 toks/s, output: 54.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 35.56 toks/s, output: 182.94 toks/s]

 14%|█▍        | 39/279 [01:31<09:21,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.96 toks/s, output: 42.56 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 31.92 toks/s, output: 212.78 toks/s]

 14%|█▍        | 40/279 [01:33<09:20,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.38 toks/s, output: 42.55 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 32.33 toks/s, output: 212.72 toks/s]

 15%|█▍        | 41/279 [01:36<09:18,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.94 toks/s, output: 42.57 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 38.73 toks/s, output: 212.81 toks/s]

 15%|█▌        | 42/279 [01:38<09:16,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.53 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 35.29 toks/s, output: 212.61 toks/s]

 15%|█▌        | 43/279 [01:40<09:14,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.30s/it, est. speed input: 6.07 toks/s, output: 42.52 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 33.18 toks/s, output: 211.82 toks/s]

 16%|█▌        | 44/279 [01:43<09:12,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.81 toks/s, output: 42.55 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 34.89 toks/s, output: 212.73 toks/s]

 16%|█▌        | 45/279 [01:45<09:10,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.52 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 34.01 toks/s, output: 212.59 toks/s]

 16%|█▋        | 46/279 [01:47<09:07,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.51 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.58 toks/s, output: 212.50 toks/s]

 17%|█▋        | 47/279 [01:50<09:05,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.38 toks/s, output: 42.54 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 34.02 toks/s, output: 212.65 toks/s]

 17%|█▋        | 48/279 [01:52<09:03,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:02,  1.43it/s, est. speed input: 19.98 toks/s, output: 41.38 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.25s/it, est. speed input: 14.55 toks/s, output: 55.22 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 35.53 toks/s, output: 183.63 toks/s]

 18%|█▊        | 49/279 [01:54<09:00,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.53 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 36.15 toks/s, output: 212.62 toks/s]

 18%|█▊        | 50/279 [01:57<08:58,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.70s/it, est. speed input: 10.58 toks/s, output: 42.34 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:02,  1.07it/s, est. speed input: 15.77 toks/s, output: 76.94 toks/s][A

Processed prompts:  60%|██████    | 3/5 [00:02<00:01,  1.61it/s, est. speed input: 20.05 toks/s, output: 111.32 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 33.26 toks/s, output: 196.60 toks/s]

 18%|█▊        | 51/279 [01:59<08:55,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:03,  1.26it/s, est. speed input: 20.14 toks/s, output: 41.55 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.23s/it, est. speed input: 14.97 toks/s, output: 56.89 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 36.78 toks/s, output: 185.20 toks/s]

 19%|█▊        | 52/279 [02:02<08:52,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.70s/it, est. speed input: 10.00 toks/s, output: 42.33 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.08s/it, est. speed input: 15.34 toks/s, output: 73.30 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 34.94 toks/s, output: 201.12 toks/s]

 19%|█▉        | 53/279 [02:04<08:50,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.33s/it, est. speed input: 6.88 toks/s, output: 42.54 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 38.29 toks/s, output: 212.31 toks/s]

 19%|█▉        | 54/279 [02:06<08:48,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.23 toks/s, output: 42.54 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 34.46 toks/s, output: 212.70 toks/s]

 20%|█▉        | 55/279 [02:09<08:46,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.23 toks/s, output: 42.52 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 34.44 toks/s, output: 212.58 toks/s]

 20%|██        | 56/279 [02:11<08:44,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.71 toks/s, output: 212.43 toks/s]

 20%|██        | 57/279 [02:13<08:42,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.94 toks/s, output: 212.31 toks/s]

 21%|██        | 58/279 [02:16<08:40,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.79 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.52 toks/s, output: 212.31 toks/s]

 21%|██        | 59/279 [02:18<08:38,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 4.68 toks/s, output: 42.54 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 25.52 toks/s, output: 212.69 toks/s]

 22%|██▏       | 60/279 [02:20<08:35,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.52 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 27.21 toks/s, output: 212.60 toks/s]

 22%|██▏       | 61/279 [02:23<08:33,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.10 toks/s, output: 42.50 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 26.77 toks/s, output: 212.46 toks/s]

 22%|██▏       | 62/279 [02:25<08:30,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.10 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 25.92 toks/s, output: 212.42 toks/s]

 23%|██▎       | 63/279 [02:27<08:28,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.10 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 25.07 toks/s, output: 212.45 toks/s]

 23%|██▎       | 64/279 [02:30<08:26,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.61s/it, est. speed input: 8.08 toks/s, output: 42.24 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.10s/it, est. speed input: 12.78 toks/s, output: 71.54 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 32.36 toks/s, output: 199.28 toks/s]

 23%|██▎       | 65/279 [02:32<08:23,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 4.67 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 26.76 toks/s, output: 212.39 toks/s]

 24%|██▎       | 66/279 [02:34<08:21,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.52 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 25.91 toks/s, output: 212.40 toks/s]

 24%|██▍       | 67/279 [02:37<08:19,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.10 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 24.63 toks/s, output: 212.32 toks/s]

 24%|██▍       | 68/279 [02:39<08:16,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 4.67 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 27.58 toks/s, output: 212.19 toks/s]

 25%|██▍       | 69/279 [02:42<08:14,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 4.67 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 24.22 toks/s, output: 212.45 toks/s]

 25%|██▌       | 70/279 [02:44<08:12,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 27.17 toks/s, output: 212.25 toks/s]

 25%|██▌       | 71/279 [02:46<08:10,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 28.42 toks/s, output: 212.07 toks/s]

 26%|██▌       | 72/279 [02:49<08:07,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:03,  1.10it/s, est. speed input: 13.19 toks/s, output: 41.78 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.22s/it, est. speed input: 10.25 toks/s, output: 58.93 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 25.19 toks/s, output: 187.01 toks/s]

 26%|██▌       | 73/279 [02:51<08:04,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.22s/it, est. speed input: 5.41 toks/s, output: 42.37 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:02,  1.02it/s, est. speed input: 10.28 toks/s, output: 82.65 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 27.98 toks/s, output: 209.01 toks/s]

 27%|██▋       | 74/279 [02:53<08:02,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.09 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 26.72 toks/s, output: 212.09 toks/s]

 27%|██▋       | 75/279 [02:56<08:00,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.52 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.13 toks/s, output: 212.19 toks/s]

 27%|██▋       | 76/279 [02:58<07:58,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 11.46 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.52 toks/s, output: 212.15 toks/s]

 28%|██▊       | 77/279 [03:00<07:56,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 4.67 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 24.20 toks/s, output: 212.25 toks/s]

 28%|██▊       | 78/279 [03:03<07:53,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.28s/it, est. speed input: 9.34 toks/s, output: 42.05 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.15s/it, est. speed input: 9.80 toks/s, output: 65.62 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 25.14 toks/s, output: 193.42 toks/s]

 28%|██▊       | 79/279 [03:05<07:50,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 11.02 toks/s, output: 42.40 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.79 toks/s, output: 211.96 toks/s]

 29%|██▊       | 80/279 [03:07<07:48,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.36s/it, est. speed input: 8.86 toks/s, output: 42.06 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:01<00:02,  1.23it/s, est. speed input: 13.38 toks/s, output: 74.14 toks/s][A

Processed prompts:  60%|██████    | 3/5 [00:02<00:01,  1.44it/s, est. speed input: 17.48 toks/s, output: 99.34 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 29.42 toks/s, output: 184.60 toks/s]

 29%|██▉       | 81/279 [03:10<07:46,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 4.67 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.11 toks/s, output: 212.06 toks/s]

 29%|██▉       | 82/279 [03:12<07:44,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.21 toks/s, output: 42.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.50 toks/s, output: 212.04 toks/s]

 30%|██▉       | 83/279 [03:15<07:41,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.78 toks/s, output: 42.40 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.49 toks/s, output: 211.96 toks/s]

 30%|███       | 84/279 [03:17<07:39,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.21 toks/s, output: 42.40 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.49 toks/s, output: 211.96 toks/s]

 30%|███       | 85/279 [03:19<07:37,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.51 toks/s, output: 42.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.04 toks/s, output: 212.00 toks/s]

 31%|███       | 86/279 [03:22<07:35,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.96 toks/s, output: 212.05 toks/s]

 31%|███       | 87/279 [03:24<07:32,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:04,  1.19s/it, est. speed input: 10.08 toks/s, output: 41.98 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.17s/it, est. speed input: 10.23 toks/s, output: 63.92 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 28.97 toks/s, output: 191.74 toks/s]

 32%|███▏      | 88/279 [03:26<07:30,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:04,  1.15s/it, est. speed input: 13.97 toks/s, output: 41.91 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.18s/it, est. speed input: 13.21 toks/s, output: 63.09 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 32.39 toks/s, output: 190.95 toks/s]

 32%|███▏      | 89/279 [03:29<07:27,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.63 toks/s, output: 42.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.92 toks/s, output: 212.01 toks/s]

 32%|███▏      | 90/279 [03:31<07:25,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 28.83 toks/s, output: 212.01 toks/s]

 33%|███▎      | 91/279 [03:33<07:23,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.12 toks/s, output: 212.11 toks/s]

 33%|███▎      | 92/279 [03:36<07:20,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.64 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.76 toks/s, output: 212.11 toks/s]

 33%|███▎      | 93/279 [03:38<07:18,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.51 toks/s, output: 42.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.65 toks/s, output: 212.01 toks/s]

 34%|███▎      | 94/279 [03:40<07:16,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.29s/it, est. speed input: 14.00 toks/s, output: 42.01 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.15s/it, est. speed input: 14.90 toks/s, output: 65.57 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 38.74 toks/s, output: 193.30 toks/s]

 34%|███▍      | 95/279 [03:43<07:13,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.21 toks/s, output: 42.39 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.79 toks/s, output: 211.95 toks/s]

 34%|███▍      | 96/279 [03:45<07:11,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.36 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.08 toks/s, output: 212.08 toks/s]

 35%|███▍      | 97/279 [03:48<07:09,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.10 toks/s, output: 212.18 toks/s]

 35%|███▌      | 98/279 [03:50<07:06,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.36 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.39 toks/s, output: 212.11 toks/s]

 35%|███▌      | 99/279 [03:52<07:04,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 9.33 toks/s, output: 42.40 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.19 toks/s, output: 212.00 toks/s]

 36%|███▌      | 100/279 [03:55<07:02,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.52 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 28.43 toks/s, output: 212.16 toks/s]

 36%|███▌      | 101/279 [03:57<06:59,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.82 toks/s, output: 212.16 toks/s]

 37%|███▋      | 102/279 [03:59<06:57,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:02,  1.64it/s, est. speed input: 22.99 toks/s, output: 41.06 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.27s/it, est. speed input: 14.10 toks/s, output: 53.40 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 33.32 toks/s, output: 181.53 toks/s]

 37%|███▋      | 103/279 [04:02<06:54,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.52 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.82 toks/s, output: 212.11 toks/s]

 37%|███▋      | 104/279 [04:04<06:52,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.51 toks/s, output: 42.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.77 toks/s, output: 212.01 toks/s]

 38%|███▊      | 105/279 [04:06<06:50,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.64 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.09 toks/s, output: 212.10 toks/s]

 38%|███▊      | 106/279 [04:09<06:47,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:04,  1.10s/it, est. speed input: 10.93 toks/s, output: 41.89 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.19s/it, est. speed input: 13.65 toks/s, output: 62.26 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 31.55 toks/s, output: 190.18 toks/s]

 38%|███▊      | 107/279 [04:11<06:44,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.94s/it, est. speed input: 9.29 toks/s, output: 42.34 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.04s/it, est. speed input: 14.03 toks/s, output: 77.36 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 32.30 toks/s, output: 204.87 toks/s]

 39%|███▊      | 108/279 [04:13<06:42,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.92 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.90 toks/s, output: 212.26 toks/s]

 39%|███▉      | 109/279 [04:16<06:40,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.91 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 38.62 toks/s, output: 212.21 toks/s]

 39%|███▉      | 110/279 [04:18<06:38,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.63 toks/s, output: 42.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.80 toks/s, output: 212.03 toks/s]

 40%|███▉      | 111/279 [04:21<06:36,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.36 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.09 toks/s, output: 212.13 toks/s]

 40%|████      | 112/279 [04:23<06:33,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.11 toks/s, output: 212.24 toks/s]

 41%|████      | 113/279 [04:25<06:31,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.09 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.51 toks/s, output: 212.10 toks/s]

 41%|████      | 114/279 [04:28<06:29,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.07 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.23 toks/s, output: 212.22 toks/s]

 41%|████      | 115/279 [04:30<06:26,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.67 toks/s, output: 212.11 toks/s]

 42%|████▏     | 116/279 [04:32<06:24,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.22 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.83 toks/s, output: 212.19 toks/s]

 42%|████▏     | 117/279 [04:35<06:22,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.52s/it, est. speed input: 13.19 toks/s, output: 42.20 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.11s/it, est. speed input: 13.63 toks/s, output: 69.86 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 32.37 toks/s, output: 197.65 toks/s]

 42%|████▏     | 118/279 [04:37<06:19,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.49 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.92 toks/s, output: 212.17 toks/s]

 43%|████▎     | 119/279 [04:39<06:17,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.37 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.10 toks/s, output: 212.21 toks/s]

 43%|████▎     | 120/279 [04:42<06:14,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.79 toks/s, output: 212.15 toks/s]

 43%|████▎     | 121/279 [04:44<06:12,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:02,  1.78it/s, est. speed input: 30.22 toks/s, output: 40.89 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.28s/it, est. speed input: 13.25 toks/s, output: 52.56 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 37.17 toks/s, output: 180.75 toks/s]

 44%|████▎     | 122/279 [04:46<06:09,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.10 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.58 toks/s, output: 212.35 toks/s]

 44%|████▍     | 123/279 [04:49<06:07,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.53 toks/s, output: 212.22 toks/s]

 44%|████▍     | 124/279 [04:51<06:05,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 28.87 toks/s, output: 212.28 toks/s]

 45%|████▍     | 125/279 [04:54<06:02,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.98 toks/s, output: 212.16 toks/s]

 45%|████▌     | 126/279 [04:56<06:00,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.79 toks/s, output: 212.14 toks/s]

 46%|████▌     | 127/279 [04:58<05:58,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.64 toks/s, output: 212.16 toks/s]

 46%|████▌     | 128/279 [05:01<05:55,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.68 toks/s, output: 212.19 toks/s]

 46%|████▌     | 129/279 [05:03<05:53,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.79 toks/s, output: 212.12 toks/s]

 47%|████▋     | 130/279 [05:05<05:51,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.51 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.39 toks/s, output: 212.07 toks/s]

 47%|████▋     | 131/279 [05:08<05:49,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.52 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 29.70 toks/s, output: 212.12 toks/s]

 47%|████▋     | 132/279 [05:10<05:46,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:04,  1.24s/it, est. speed input: 11.31 toks/s, output: 42.00 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.16s/it, est. speed input: 11.93 toks/s, output: 64.78 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 34.09 toks/s, output: 192.63 toks/s]

 48%|████▊     | 133/279 [05:12<05:43,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.15s/it, est. speed input: 6.53 toks/s, output: 42.41 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.01s/it, est. speed input: 12.32 toks/s, output: 81.15 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 28.04 toks/s, output: 208.59 toks/s]

 48%|████▊     | 134/279 [05:15<05:41,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.22 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.84 toks/s, output: 212.28 toks/s]

 48%|████▊     | 135/279 [05:17<05:39,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.64 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.12 toks/s, output: 212.28 toks/s]

 49%|████▊     | 136/279 [05:19<05:36,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.09 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 29.71 toks/s, output: 212.22 toks/s]

 49%|████▉     | 137/279 [05:22<05:34,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:03,  1.16it/s, est. speed input: 19.64 toks/s, output: 41.59 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.22s/it, est. speed input: 15.80 toks/s, output: 58.07 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 35.43 toks/s, output: 186.14 toks/s]

 49%|████▉     | 138/279 [05:24<05:31,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.42 toks/s, output: 212.32 toks/s]

 50%|████▉     | 139/279 [05:27<05:29,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.07 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.42 toks/s, output: 212.31 toks/s]

 50%|█████     | 140/279 [05:29<05:27,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.65 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.80 toks/s, output: 212.35 toks/s]

 51%|█████     | 141/279 [05:31<05:25,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.33s/it, est. speed input: 7.72 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.53 toks/s, output: 211.77 toks/s]

 51%|█████     | 142/279 [05:34<05:22,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.97 toks/s, output: 212.12 toks/s]

 51%|█████▏    | 143/279 [05:36<05:20,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.38 toks/s, output: 212.21 toks/s]

 52%|█████▏    | 144/279 [05:38<05:18,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.49s/it, est. speed input: 9.38 toks/s, output: 42.20 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.12s/it, est. speed input: 11.50 toks/s, output: 69.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 30.67 toks/s, output: 197.22 toks/s]

 52%|█████▏    | 145/279 [05:41<05:15,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.79 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.27 toks/s, output: 212.30 toks/s]

 52%|█████▏    | 146/279 [05:43<05:13,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.53 toks/s, output: 212.01 toks/s]

 53%|█████▎    | 147/279 [05:45<05:11,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.75s/it, est. speed input: 7.43 toks/s, output: 42.29 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.07s/it, est. speed input: 11.06 toks/s, output: 74.04 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 29.78 toks/s, output: 201.67 toks/s]

 53%|█████▎    | 148/279 [05:48<05:08,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.35s/it, est. speed input: 11.08 toks/s, output: 42.09 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.14s/it, est. speed input: 12.78 toks/s, output: 66.89 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 34.93 toks/s, output: 194.69 toks/s]

 53%|█████▎    | 149/279 [05:50<05:05,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.22 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.41 toks/s, output: 212.21 toks/s]

 54%|█████▍    | 150/279 [05:52<05:03,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.59s/it, est. speed input: 8.19 toks/s, output: 42.22 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.10s/it, est. speed input: 11.92 toks/s, output: 71.07 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 30.21 toks/s, output: 198.73 toks/s]

 54%|█████▍    | 151/279 [05:55<05:01,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.52 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.56 toks/s, output: 212.25 toks/s]

 54%|█████▍    | 152/279 [05:57<04:59,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.75s/it, est. speed input: 7.43 toks/s, output: 42.28 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.07s/it, est. speed input: 12.33 toks/s, output: 73.98 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 31.46 toks/s, output: 201.52 toks/s]

 55%|█████▍    | 153/279 [05:59<04:56,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.08s/it, est. speed input: 6.26 toks/s, output: 42.40 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.02s/it, est. speed input: 11.48 toks/s, output: 79.92 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 29.75 toks/s, output: 207.43 toks/s]

 55%|█████▌    | 154/279 [06:02<04:54,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.52 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.16 toks/s, output: 212.37 toks/s]

 56%|█████▌    | 155/279 [06:04<04:52,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.00 toks/s, output: 212.31 toks/s]

 56%|█████▌    | 156/279 [06:07<04:49,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.68s/it, est. speed input: 9.52 toks/s, output: 42.22 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.02s/it, est. speed input: 14.76 toks/s, output: 74.23 toks/s][A

Processed prompts:  60%|██████    | 3/5 [00:02<00:01,  1.65it/s, est. speed input: 21.69 toks/s, output: 113.13 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 35.72 toks/s, output: 198.17 toks/s]

 56%|█████▋    | 157/279 [06:09<04:47,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.24 toks/s, output: 212.13 toks/s]

 57%|█████▋    | 158/279 [06:11<04:45,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.52 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.14 toks/s, output: 212.26 toks/s]

 57%|█████▋    | 159/279 [06:14<04:42,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 29.74 toks/s, output: 212.43 toks/s]

 57%|█████▋    | 160/279 [06:16<04:40,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.72s/it, est. speed input: 7.54 toks/s, output: 42.32 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.08s/it, est. speed input: 11.07 toks/s, output: 73.68 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 28.96 toks/s, output: 201.42 toks/s]

 58%|█████▊    | 161/279 [06:18<04:37,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.37 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.83 toks/s, output: 212.23 toks/s]

 58%|█████▊    | 162/279 [06:21<04:35,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.85 toks/s, output: 212.32 toks/s]

 58%|█████▊    | 163/279 [06:23<04:33,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.85 toks/s, output: 212.36 toks/s]

 59%|█████▉    | 164/279 [06:25<04:30,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.15 toks/s, output: 212.33 toks/s]

 59%|█████▉    | 165/279 [06:28<04:28,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.52 toks/s, output: 42.50 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 29.74 toks/s, output: 212.45 toks/s]

 59%|█████▉    | 166/279 [06:30<04:26,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.94 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.12 toks/s, output: 212.29 toks/s]

 60%|█████▉    | 167/279 [06:32<04:23,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.38s/it, est. speed input: 9.44 toks/s, output: 42.14 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.14s/it, est. speed input: 11.93 toks/s, output: 67.33 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 31.10 toks/s, output: 195.15 toks/s]

 60%|██████    | 168/279 [06:35<04:21,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.22 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.80 toks/s, output: 212.20 toks/s]

 61%|██████    | 169/279 [06:37<04:19,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.22 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.98 toks/s, output: 212.38 toks/s]

 61%|██████    | 170/279 [06:40<04:16,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.80s/it, est. speed input: 7.80 toks/s, output: 42.33 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.07s/it, est. speed input: 12.34 toks/s, output: 74.92 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 32.35 toks/s, output: 202.60 toks/s]

 61%|██████▏   | 171/279 [06:42<04:14,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.55 toks/s, output: 212.36 toks/s]

 62%|██████▏   | 172/279 [06:44<04:11,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.84s/it, est. speed input: 8.14 toks/s, output: 42.34 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.06s/it, est. speed input: 12.77 toks/s, output: 75.74 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 33.19 toks/s, output: 203.37 toks/s]

 62%|██████▏   | 173/279 [06:47<04:09,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.22 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.12 toks/s, output: 212.30 toks/s]

 62%|██████▏   | 174/279 [06:49<04:07,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.80 toks/s, output: 212.36 toks/s]

 63%|██████▎   | 175/279 [06:51<04:04,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.77s/it, est. speed input: 11.29 toks/s, output: 42.32 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.07s/it, est. speed input: 14.90 toks/s, output: 74.50 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 34.91 toks/s, output: 202.21 toks/s]

 63%|██████▎   | 176/279 [06:54<04:02,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.02 toks/s, output: 212.44 toks/s]

 63%|██████▎   | 177/279 [06:56<04:00,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.07 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.99 toks/s, output: 212.26 toks/s]

 64%|██████▍   | 178/279 [06:58<03:57,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.52 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.99 toks/s, output: 212.24 toks/s]

 64%|██████▍   | 179/279 [07:01<03:55,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.52 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.38 toks/s, output: 212.38 toks/s]

 65%|██████▍   | 180/279 [07:03<03:53,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 13.17 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 42.48 toks/s, output: 212.39 toks/s]

 65%|██████▍   | 181/279 [07:05<03:50,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.50 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 28.89 toks/s, output: 212.46 toks/s]

 65%|██████▌   | 182/279 [07:08<03:48,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 29.30 toks/s, output: 212.34 toks/s]

 66%|██████▌   | 183/279 [07:10<03:46,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.28 toks/s, output: 212.35 toks/s]

 66%|██████▌   | 184/279 [07:13<03:43,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.39 toks/s, output: 212.31 toks/s]

 66%|██████▋   | 185/279 [07:15<03:41,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.22 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.44 toks/s, output: 212.40 toks/s]

 67%|██████▋   | 186/279 [07:17<03:39,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.01 toks/s, output: 212.40 toks/s]

 67%|██████▋   | 187/279 [07:20<03:36,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.37 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.08 toks/s, output: 212.25 toks/s]

 67%|██████▋   | 188/279 [07:22<03:34,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.65 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.40 toks/s, output: 212.35 toks/s]

 68%|██████▊   | 189/279 [07:24<03:32,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.84 toks/s, output: 212.26 toks/s]

 68%|██████▊   | 190/279 [07:27<03:29,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.07 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.24 toks/s, output: 212.31 toks/s]

 68%|██████▊   | 191/279 [07:29<03:27,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.25 toks/s, output: 212.35 toks/s]

 69%|██████▉   | 192/279 [07:31<03:25,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.22 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.12 toks/s, output: 212.33 toks/s]

 69%|██████▉   | 193/279 [07:34<03:22,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 11.47 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.08 toks/s, output: 212.40 toks/s]

 70%|██████▉   | 194/279 [07:36<03:20,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.50 toks/s, output: 212.18 toks/s]

 70%|██████▉   | 195/279 [07:38<03:17,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.00s/it, est. speed input: 9.48 toks/s, output: 42.41 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.03s/it, est. speed input: 17.87 toks/s, output: 78.70 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 38.71 toks/s, output: 206.30 toks/s]

 70%|███████   | 196/279 [07:41<03:15,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 9.35 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.93 toks/s, output: 212.40 toks/s]

 71%|███████   | 197/279 [07:43<03:13,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.38 toks/s, output: 42.50 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.95 toks/s, output: 212.50 toks/s]

 71%|███████   | 198/279 [07:45<03:10,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.37 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 44.58 toks/s, output: 212.28 toks/s]

 71%|███████▏  | 199/279 [07:48<03:08,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.80 toks/s, output: 212.38 toks/s]

 72%|███████▏  | 200/279 [07:50<03:06,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:04,  1.17s/it, est. speed input: 14.56 toks/s, output: 41.97 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.17s/it, est. speed input: 14.08 toks/s, output: 63.57 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 33.27 toks/s, output: 191.54 toks/s]

 72%|███████▏  | 201/279 [07:53<03:03,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.41 toks/s, output: 212.40 toks/s]

 72%|███████▏  | 202/279 [07:55<03:01,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.28s/it, est. speed input: 7.44 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.15 toks/s, output: 211.21 toks/s]

 73%|███████▎  | 203/279 [07:57<02:58,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 9.77 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.80 toks/s, output: 212.37 toks/s]

 73%|███████▎  | 204/279 [08:00<02:56,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.64 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.65 toks/s, output: 212.21 toks/s]

 73%|███████▎  | 205/279 [08:02<02:54,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 9.77 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 41.63 toks/s, output: 212.38 toks/s]

 74%|███████▍  | 206/279 [08:04<02:51,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.77s/it, est. speed input: 8.47 toks/s, output: 42.33 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.07s/it, est. speed input: 12.35 toks/s, output: 74.51 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 31.93 toks/s, output: 202.22 toks/s]

 74%|███████▍  | 207/279 [08:07<02:49,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.64 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.28 toks/s, output: 212.34 toks/s]

 75%|███████▍  | 208/279 [08:09<02:47,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.52 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.56 toks/s, output: 212.40 toks/s]

 75%|███████▍  | 209/279 [08:11<02:44,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.59 toks/s, output: 212.40 toks/s]

 75%|███████▌  | 210/279 [08:14<02:42,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.27 toks/s, output: 212.32 toks/s]

 76%|███████▌  | 211/279 [08:16<02:40,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.68 toks/s, output: 212.35 toks/s]

 76%|███████▌  | 212/279 [08:18<02:37,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.52 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.14 toks/s, output: 212.44 toks/s]

 76%|███████▋  | 213/279 [08:21<02:35,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 10.62 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 38.66 toks/s, output: 212.42 toks/s]

 77%|███████▋  | 214/279 [08:23<02:33,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 9.35 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 43.33 toks/s, output: 212.39 toks/s]

 77%|███████▋  | 215/279 [08:26<02:30,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.43 toks/s, output: 212.34 toks/s]

 77%|███████▋  | 216/279 [08:28<02:28,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.08 toks/s, output: 212.36 toks/s]

 78%|███████▊  | 217/279 [08:30<02:26,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 10.20 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.42 toks/s, output: 212.45 toks/s]

 78%|███████▊  | 218/279 [08:33<02:23,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.50 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 42.49 toks/s, output: 212.47 toks/s]

 78%|███████▊  | 219/279 [08:35<02:21,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.37 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.92 toks/s, output: 212.20 toks/s]

 79%|███████▉  | 220/279 [08:37<02:19,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.33s/it, est. speed input: 6.86 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.66 toks/s, output: 211.85 toks/s]

 79%|███████▉  | 221/279 [08:40<02:16,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.37 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.09 toks/s, output: 212.27 toks/s]

 80%|███████▉  | 222/279 [08:42<02:14,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.44 toks/s, output: 212.41 toks/s]

 80%|███████▉  | 223/279 [08:44<02:11,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.56 toks/s, output: 212.39 toks/s]

 80%|████████  | 224/279 [08:47<02:09,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:04,  1.12s/it, est. speed input: 11.61 toks/s, output: 41.96 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.18s/it, est. speed input: 11.95 toks/s, output: 62.76 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 37.99 toks/s, output: 190.82 toks/s]

 81%|████████  | 225/279 [08:49<02:07,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.22 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.98 toks/s, output: 212.35 toks/s]

 81%|████████  | 226/279 [08:51<02:04,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.15 toks/s, output: 212.33 toks/s]

 81%|████████▏ | 227/279 [08:54<02:02,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:03,  1.02it/s, est. speed input: 16.32 toks/s, output: 41.82 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.20s/it, est. speed input: 13.67 toks/s, output: 60.22 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 34.59 toks/s, output: 188.33 toks/s]

 82%|████████▏ | 228/279 [08:56<01:59,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.71 toks/s, output: 212.38 toks/s]

 82%|████████▏ | 229/279 [08:58<01:57,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 9.35 toks/s, output: 42.50 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.52 toks/s, output: 212.47 toks/s]

 82%|████████▏ | 230/279 [09:01<01:55,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.11 toks/s, output: 212.39 toks/s]

 83%|████████▎ | 231/279 [09:03<01:52,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.64 toks/s, output: 212.14 toks/s]

 83%|████████▎ | 232/279 [09:06<01:50,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.52 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.13 toks/s, output: 212.35 toks/s]

 84%|████████▎ | 233/279 [09:08<01:48,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.92 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.10 toks/s, output: 212.37 toks/s]

 84%|████████▍ | 234/279 [09:10<01:46,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.96s/it, est. speed input: 7.15 toks/s, output: 42.39 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.04s/it, est. speed input: 13.19 toks/s, output: 77.85 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 31.90 toks/s, output: 205.44 toks/s]

 84%|████████▍ | 235/279 [09:13<01:43,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.86 toks/s, output: 212.41 toks/s]

 85%|████████▍ | 236/279 [09:15<01:41,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.77s/it, est. speed input: 7.89 toks/s, output: 42.29 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.07s/it, est. speed input: 11.91 toks/s, output: 74.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 30.21 toks/s, output: 202.08 toks/s]

 85%|████████▍ | 237/279 [09:17<01:38,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:04,  1.24s/it, est. speed input: 11.32 toks/s, output: 42.03 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.16s/it, est. speed input: 11.52 toks/s, output: 64.83 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 29.00 toks/s, output: 192.75 toks/s]

 85%|████████▌ | 238/279 [09:20<01:36,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.52 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.43 toks/s, output: 212.39 toks/s]

 86%|████████▌ | 239/279 [09:22<01:34,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.50 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.60 toks/s, output: 212.50 toks/s]

 86%|████████▌ | 240/279 [09:24<01:31,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.14s/it, est. speed input: 6.53 toks/s, output: 42.46 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.01s/it, est. speed input: 12.33 toks/s, output: 81.22 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 30.61 toks/s, output: 208.77 toks/s]

 86%|████████▋ | 241/279 [09:27<01:29,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.19s/it, est. speed input: 6.84 toks/s, output: 42.44 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:02,  1.00it/s, est. speed input: 12.32 toks/s, output: 82.02 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.59 toks/s, output: 209.48 toks/s]

 87%|████████▋ | 242/279 [09:29<01:27,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.51 toks/s, output: 212.28 toks/s]

 87%|████████▋ | 243/279 [09:31<01:24,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.58 toks/s, output: 212.35 toks/s]

 87%|████████▋ | 244/279 [09:34<01:22,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.52 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.86 toks/s, output: 212.43 toks/s]

 88%|████████▊ | 245/279 [09:36<01:20,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.14s/it, est. speed input: 6.06 toks/s, output: 42.45 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:02,  1.05it/s, est. speed input: 11.95 toks/s, output: 82.76 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 29.77 toks/s, output: 207.10 toks/s]

 88%|████████▊ | 246/279 [09:39<01:17,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.68s/it, est. speed input: 7.74 toks/s, output: 42.25 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.09s/it, est. speed input: 12.34 toks/s, output: 72.77 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 36.59 toks/s, output: 200.42 toks/s]

 89%|████████▊ | 247/279 [09:41<01:15,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.68s/it, est. speed input: 9.52 toks/s, output: 42.25 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.09s/it, est. speed input: 12.34 toks/s, output: 72.74 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 30.20 toks/s, output: 200.34 toks/s]

 89%|████████▉ | 248/279 [09:43<01:12,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.52 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 28.88 toks/s, output: 212.38 toks/s]

 89%|████████▉ | 249/279 [09:46<01:10,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.65 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.59 toks/s, output: 212.42 toks/s]

 90%|████████▉ | 250/279 [09:48<01:08,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.52 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 28.89 toks/s, output: 212.44 toks/s]

 90%|████████▉ | 251/279 [09:50<01:05,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.80s/it, est. speed input: 7.80 toks/s, output: 42.33 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.07s/it, est. speed input: 12.77 toks/s, output: 74.92 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 34.05 toks/s, output: 202.60 toks/s]

 90%|█████████ | 252/279 [09:53<01:03,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.45s/it, est. speed input: 9.68 toks/s, output: 42.17 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:01<00:02,  1.30it/s, est. speed input: 15.46 toks/s, output: 77.30 toks/s][A

Processed prompts:  60%|██████    | 3/5 [00:02<00:01,  1.73it/s, est. speed input: 20.09 toks/s, output: 107.14 toks/s][A

Processed prompts:  80%|████████  | 4/5 [00:02<00:00,  2.23it/s, est. speed input: 23.49 toks/s, output: 138.37 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 30.32 toks/s, output: 181.07 toks/s]

 91%|█████████ | 253/279 [09:55<01:01,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.24 toks/s, output: 212.11 toks/s]

 91%|█████████ | 254/279 [09:57<00:58,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.26s/it, est. speed input: 5.75 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 28.47 toks/s, output: 210.77 toks/s]

 91%|█████████▏| 255/279 [10:00<00:56,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.85 toks/s, output: 212.34 toks/s]

 92%|█████████▏| 256/279 [10:02<00:54,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.22 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 29.73 toks/s, output: 212.37 toks/s]

 92%|█████████▏| 257/279 [10:04<00:51,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.85 toks/s, output: 212.35 toks/s]

 92%|█████████▏| 258/279 [10:07<00:49,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.07 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.69 toks/s, output: 212.29 toks/s]

 93%|█████████▎| 259/279 [10:09<00:47,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.16 toks/s, output: 212.37 toks/s]

 93%|█████████▎| 260/279 [10:11<00:44,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.79 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.57 toks/s, output: 212.31 toks/s]

 94%|█████████▎| 261/279 [10:14<00:42,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.50 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.17 toks/s, output: 212.47 toks/s]

 94%|█████████▍| 262/279 [10:16<00:40,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.52 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.09 toks/s, output: 212.10 toks/s]

 94%|█████████▍| 263/279 [10:19<00:37,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.77s/it, est. speed input: 7.90 toks/s, output: 42.33 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.07s/it, est. speed input: 12.35 toks/s, output: 74.51 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 31.50 toks/s, output: 202.21 toks/s]

 95%|█████████▍| 264/279 [10:21<00:35,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.61s/it, est. speed input: 8.69 toks/s, output: 42.23 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.00s/it, est. speed input: 14.63 toks/s, output: 73.61 toks/s][A

Processed prompts:  60%|██████    | 3/5 [00:02<00:01,  1.62it/s, est. speed input: 19.17 toks/s, output: 111.16 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 30.24 toks/s, output: 196.33 toks/s]

 95%|█████████▍| 265/279 [10:23<00:32,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.49s/it, est. speed input: 6.42 toks/s, output: 38.14 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.09s/it, est. speed input: 11.51 toks/s, output: 74.82 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  1.92it/s, est. speed input: 28.01 toks/s, output: 189.91 toks/s]

 95%|█████████▌| 266/279 [10:26<00:31,  2.43s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.52 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.26 toks/s, output: 212.26 toks/s]

 96%|█████████▌| 267/279 [10:28<00:28,  2.41s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.54 toks/s, output: 212.06 toks/s]

 96%|█████████▌| 268/279 [10:31<00:26,  2.39s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.84s/it, est. speed input: 7.59 toks/s, output: 42.31 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.06s/it, est. speed input: 11.48 toks/s, output: 75.70 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 30.19 toks/s, output: 203.27 toks/s]

 96%|█████████▋| 269/279 [10:33<00:23,  2.38s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.99 toks/s, output: 212.29 toks/s]

 97%|█████████▋| 270/279 [10:35<00:21,  2.37s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.52 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.56 toks/s, output: 212.43 toks/s]

 97%|█████████▋| 271/279 [10:38<00:18,  2.37s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.50 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.02 toks/s, output: 212.48 toks/s]

 97%|█████████▋| 272/279 [10:40<00:16,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.52 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 28.45 toks/s, output: 212.28 toks/s]

 98%|█████████▊| 273/279 [10:42<00:14,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.52 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 29.30 toks/s, output: 212.30 toks/s]

 98%|█████████▊| 274/279 [10:45<00:11,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.93 toks/s, output: 212.25 toks/s]

 99%|█████████▊| 275/279 [10:47<00:09,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.64 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.52 toks/s, output: 212.33 toks/s]

 99%|█████████▉| 276/279 [10:49<00:07,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.22 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.53 toks/s, output: 212.40 toks/s]

 99%|█████████▉| 277/279 [10:52<00:04,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.50 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.92 toks/s, output: 212.36 toks/s]

100%|█████████▉| 278/279 [10:54<00:02,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.07 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.09 toks/s, output: 212.30 toks/s]

100%|██████████| 279/279 [10:56<00:00,  2.36s/it]
100%|██████████| 279/279 [10:56<00:00,  2.35s/it]

Processed prompts:   0%|          | 0/4 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s]
Processed prompts:  25%|██▌       | 1/4 [00:02<00:07,  2.33s/it, est. speed input: 7.29 toks/s, output: 42.85 toks/s]
Processed prompts: 100%|██████████| 4/4 [00:02<00:00,  1.71it/s, est. speed input: 29.99 toks/s, output: 171.40 toks/s]

Loading safetensors checkpoint shards:   0% Completed | 0/4 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:  25% Completed | 1/4 [00:00<00:01,  1.79it/s]

Loading safetensors checkpoint shards:  50% Completed | 2/4 [00:01<00:01,  1.33it/s]

Loading safetensors checkpoint shards:  75% Completed | 3/4 [00:02<00:00,  1.27it/s]

Loading safetensors checkpoint shards: 100% Completed | 4/4 [00:03<00:00,  1.19it/s]

Loading safetensors checkpoint shards: 100% Completed | 4/4 [00:03<00:00,  1.25it/s]


  0%|          | 0/279 [00:00<?, ?it/s]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.70 toks/s, output: 212.33 toks/s]

  0%|          | 1/279 [00:02<10:55,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.96 toks/s, output: 42.56 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 31.49 toks/s, output: 212.75 toks/s]

  1%|          | 2/279 [00:04<10:52,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.81 toks/s, output: 42.56 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 31.49 toks/s, output: 212.76 toks/s]

  1%|          | 3/279 [00:07<10:49,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.28s/it, est. speed input: 11.72 toks/s, output: 42.19 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.15s/it, est. speed input: 15.40 toks/s, output: 65.87 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 34.21 toks/s, output: 194.17 toks/s]

  1%|▏         | 4/279 [00:09<10:45,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.81 toks/s, output: 42.59 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 33.21 toks/s, output: 212.91 toks/s]

  2%|▏         | 5/279 [00:11<10:43,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.67 toks/s, output: 42.59 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 38.75 toks/s, output: 212.93 toks/s]

  2%|▏         | 6/279 [00:14<10:41,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.96 toks/s, output: 42.57 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 32.35 toks/s, output: 212.81 toks/s]

  3%|▎         | 7/279 [00:16<10:39,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.66 toks/s, output: 42.54 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 37.85 toks/s, output: 212.67 toks/s]

  3%|▎         | 8/279 [00:18<10:37,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.81 toks/s, output: 42.53 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 33.17 toks/s, output: 212.64 toks/s]

  3%|▎         | 9/279 [00:21<10:34,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.81 toks/s, output: 42.55 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 31.91 toks/s, output: 212.75 toks/s]

  4%|▎         | 10/279 [00:23<10:32,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.38 toks/s, output: 42.56 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 33.62 toks/s, output: 212.79 toks/s]

  4%|▍         | 11/279 [00:25<10:30,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.81 toks/s, output: 42.54 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 37.00 toks/s, output: 212.65 toks/s]

  4%|▍         | 12/279 [00:28<10:27,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.53 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 34.87 toks/s, output: 212.64 toks/s]

  5%|▍         | 13/279 [00:30<10:25,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.38 toks/s, output: 42.55 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 33.61 toks/s, output: 212.73 toks/s]

  5%|▌         | 14/279 [00:32<10:23,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.51 toks/s, output: 42.55 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 45.95 toks/s, output: 212.75 toks/s]

  5%|▌         | 15/279 [00:35<10:21,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.53 toks/s, output: 42.53 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 34.02 toks/s, output: 212.61 toks/s]

  6%|▌         | 16/279 [00:37<10:18,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.38 toks/s, output: 42.54 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 37.86 toks/s, output: 212.70 toks/s]

  6%|▌         | 17/279 [00:39<10:16,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.65 toks/s, output: 42.50 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.82 toks/s, output: 212.45 toks/s]

  6%|▋         | 18/279 [00:42<10:14,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.50 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.57 toks/s, output: 212.50 toks/s]

  7%|▋         | 19/279 [00:44<10:12,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.50 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.42 toks/s, output: 212.48 toks/s]

  7%|▋         | 20/279 [00:47<10:09,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.50 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.45 toks/s, output: 212.50 toks/s]

  8%|▊         | 21/279 [00:49<10:07,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.07 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.68 toks/s, output: 212.37 toks/s]

  8%|▊         | 22/279 [00:51<10:05,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:03,  1.22it/s, est. speed input: 18.32 toks/s, output: 41.52 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.23s/it, est. speed input: 14.52 toks/s, output: 57.24 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 35.88 toks/s, output: 185.37 toks/s]

  8%|▊         | 23/279 [00:54<10:01,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.63 toks/s, output: 42.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.90 toks/s, output: 212.04 toks/s]

  9%|▊         | 24/279 [00:56<10:00,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.52 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.09 toks/s, output: 212.13 toks/s]

  9%|▉         | 25/279 [00:58<09:58,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.90 toks/s, output: 212.10 toks/s]

  9%|▉         | 26/279 [01:01<09:56,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.75s/it, est. speed input: 11.42 toks/s, output: 42.26 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.07s/it, est. speed input: 16.15 toks/s, output: 73.95 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 42.07 toks/s, output: 201.44 toks/s]

 10%|▉         | 27/279 [01:03<09:53,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.78 toks/s, output: 42.40 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 41.55 toks/s, output: 211.98 toks/s]

 10%|█         | 28/279 [01:05<09:51,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.20 toks/s, output: 42.35 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.15 toks/s, output: 211.75 toks/s]

 10%|█         | 29/279 [01:08<09:49,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.35 toks/s, output: 42.37 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 46.17 toks/s, output: 211.80 toks/s]

 11%|█         | 30/279 [01:10<09:47,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.36 toks/s, output: 42.38 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.33 toks/s, output: 211.89 toks/s]

 11%|█         | 31/279 [01:12<09:45,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:03,  1.26it/s, est. speed input: 20.11 toks/s, output: 41.48 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.24s/it, est. speed input: 19.62 toks/s, output: 56.72 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 40.51 toks/s, output: 184.64 toks/s]

 11%|█▏        | 32/279 [01:15<09:42,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.05 toks/s, output: 42.36 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.39 toks/s, output: 211.80 toks/s]

 12%|█▏        | 33/279 [01:17<09:40,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 11.86 toks/s, output: 42.34 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 45.30 toks/s, output: 211.67 toks/s]

 12%|█▏        | 34/279 [01:20<09:38,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.78 toks/s, output: 42.35 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.42 toks/s, output: 211.75 toks/s]

 13%|█▎        | 35/279 [01:22<09:36,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.93 toks/s, output: 42.36 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.42 toks/s, output: 211.76 toks/s]

 13%|█▎        | 36/279 [01:24<09:33,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.78 toks/s, output: 42.38 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.06 toks/s, output: 211.89 toks/s]

 13%|█▎        | 37/279 [01:27<09:31,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:02,  1.33it/s, est. speed input: 21.36 toks/s, output: 41.38 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:00<00:01,  2.56it/s, est. speed input: 43.91 toks/s, output: 76.55 toks/s][A

Processed prompts:  60%|██████    | 3/5 [00:02<00:01,  1.14it/s, est. speed input: 23.53 toks/s, output: 71.88 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 38.50 toks/s, output: 157.44 toks/s]

 14%|█▎        | 38/279 [01:29<09:27,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:02,  1.52it/s, est. speed input: 22.87 toks/s, output: 41.16 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.26s/it, est. speed input: 13.64 toks/s, output: 54.14 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 42.63 toks/s, output: 182.02 toks/s]

 14%|█▍        | 39/279 [01:31<09:24,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.93 toks/s, output: 42.38 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 40.25 toks/s, output: 211.85 toks/s]

 14%|█▍        | 40/279 [01:34<09:22,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.36 toks/s, output: 42.40 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.31 toks/s, output: 211.96 toks/s]

 15%|█▍        | 41/279 [01:36<09:20,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.90 toks/s, output: 42.39 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 38.57 toks/s, output: 211.91 toks/s]

 15%|█▌        | 42/279 [01:38<09:18,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.78 toks/s, output: 42.38 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.17 toks/s, output: 211.86 toks/s]

 15%|█▌        | 43/279 [01:41<09:16,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.21 toks/s, output: 42.38 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 41.53 toks/s, output: 211.90 toks/s]

 16%|█▌        | 44/279 [01:43<09:14,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.78 toks/s, output: 42.35 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.72 toks/s, output: 211.73 toks/s]

 16%|█▌        | 45/279 [01:45<09:12,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.78 toks/s, output: 42.37 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 44.06 toks/s, output: 211.82 toks/s]

 16%|█▋        | 46/279 [01:48<09:10,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.78 toks/s, output: 42.36 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.43 toks/s, output: 211.77 toks/s]

 17%|█▋        | 47/279 [01:50<09:07,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.36 toks/s, output: 42.39 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.91 toks/s, output: 211.92 toks/s]

 17%|█▋        | 48/279 [01:53<09:05,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:02,  1.42it/s, est. speed input: 19.91 toks/s, output: 41.23 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.26s/it, est. speed input: 14.50 toks/s, output: 55.01 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 35.39 toks/s, output: 182.92 toks/s]

 18%|█▊        | 49/279 [01:55<09:02,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.47 toks/s, output: 42.37 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 44.48 toks/s, output: 211.82 toks/s]

 18%|█▊        | 50/279 [01:57<09:00,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.71s/it, est. speed input: 10.55 toks/s, output: 42.21 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:01<00:02,  1.16it/s, est. speed input: 23.70 toks/s, output: 78.65 toks/s][A

Processed prompts:  60%|██████    | 3/5 [00:02<00:01,  1.57it/s, est. speed input: 25.94 toks/s, output: 108.86 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 39.12 toks/s, output: 193.89 toks/s]

 18%|█▊        | 51/279 [02:00<08:57,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.78 toks/s, output: 42.39 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 38.15 toks/s, output: 211.95 toks/s]

 19%|█▊        | 52/279 [02:02<08:55,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.40s/it, est. speed input: 12.13 toks/s, output: 42.09 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.13s/it, est. speed input: 15.32 toks/s, output: 67.67 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 39.58 toks/s, output: 195.34 toks/s]

 19%|█▉        | 53/279 [02:04<08:52,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.21 toks/s, output: 42.39 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 40.27 toks/s, output: 211.94 toks/s]

 19%|█▉        | 54/279 [02:07<08:50,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.20 toks/s, output: 42.38 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.83 toks/s, output: 211.87 toks/s]

 20%|█▉        | 55/279 [02:09<08:48,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.20 toks/s, output: 42.37 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.32 toks/s, output: 211.85 toks/s]

 20%|██        | 56/279 [02:11<08:46,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.78 toks/s, output: 42.39 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.60 toks/s, output: 211.91 toks/s]

 20%|██        | 57/279 [02:14<08:43,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 11.87 toks/s, output: 42.39 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 42.81 toks/s, output: 211.93 toks/s]

 21%|██        | 58/279 [02:16<08:41,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.78 toks/s, output: 42.40 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 38.58 toks/s, output: 211.98 toks/s]

 21%|██        | 59/279 [02:19<08:39,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.82s/it, est. speed input: 7.69 toks/s, output: 42.27 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.06s/it, est. speed input: 15.71 toks/s, output: 75.17 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.79 toks/s, output: 202.55 toks/s]

 22%|██▏       | 60/279 [02:21<08:36,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.85s/it, est. speed input: 7.59 toks/s, output: 42.26 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.06s/it, est. speed input: 11.89 toks/s, output: 75.60 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 40.35 toks/s, output: 203.01 toks/s]

 22%|██▏       | 61/279 [02:23<08:34,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.84s/it, est. speed input: 7.05 toks/s, output: 42.28 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.06s/it, est. speed input: 15.29 toks/s, output: 75.62 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 41.21 toks/s, output: 203.06 toks/s]

 22%|██▏       | 62/279 [02:26<08:31,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.05 toks/s, output: 42.38 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.60 toks/s, output: 211.89 toks/s]

 23%|██▎       | 63/279 [02:28<08:29,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.63 toks/s, output: 42.40 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 43.66 toks/s, output: 211.96 toks/s]

 23%|██▎       | 64/279 [02:30<08:27,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.51 toks/s, output: 42.38 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.60 toks/s, output: 211.88 toks/s]

 23%|██▎       | 65/279 [02:33<08:25,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.90 toks/s, output: 42.38 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 41.95 toks/s, output: 211.86 toks/s]

 24%|██▎       | 66/279 [02:35<08:22,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.24s/it, est. speed input: 9.36 toks/s, output: 42.36 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:02,  1.01it/s, est. speed input: 16.54 toks/s, output: 82.68 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.89 toks/s, output: 209.88 toks/s]

 24%|██▍       | 67/279 [02:37<08:20,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.63 toks/s, output: 42.40 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 46.63 toks/s, output: 211.97 toks/s]

 24%|██▍       | 68/279 [02:40<08:18,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.52s/it, est. speed input: 17.12 toks/s, output: 42.15 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.11s/it, est. speed input: 22.54 toks/s, output: 69.76 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 45.08 toks/s, output: 197.35 toks/s]

 25%|██▍       | 69/279 [02:42<08:15,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.48 toks/s, output: 42.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.44 toks/s, output: 212.04 toks/s]

 25%|██▌       | 70/279 [02:44<08:12,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.93 toks/s, output: 42.37 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.85 toks/s, output: 211.80 toks/s]

 25%|██▌       | 71/279 [02:47<08:10,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 9.75 toks/s, output: 42.38 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 44.92 toks/s, output: 211.90 toks/s]

 26%|██▌       | 72/279 [02:49<08:08,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.09 toks/s, output: 42.39 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.88 toks/s, output: 211.93 toks/s]

 26%|██▌       | 73/279 [02:52<08:06,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.48 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.45 toks/s, output: 212.09 toks/s]

 27%|██▋       | 74/279 [02:54<08:03,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 9.33 toks/s, output: 42.40 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 42.40 toks/s, output: 212.00 toks/s]

 27%|██▋       | 75/279 [02:56<08:01,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.29s/it, est. speed input: 6.12 toks/s, output: 42.39 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.01 toks/s, output: 210.76 toks/s]

 27%|██▋       | 76/279 [02:59<07:59,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 11.45 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 44.54 toks/s, output: 212.10 toks/s]

 28%|██▊       | 77/279 [03:01<07:56,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 10.18 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 41.99 toks/s, output: 212.07 toks/s]

 28%|██▊       | 78/279 [03:03<07:54,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 10.18 toks/s, output: 42.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 45.80 toks/s, output: 212.05 toks/s]

 28%|██▊       | 79/279 [03:06<07:51,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 11.03 toks/s, output: 42.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.89 toks/s, output: 212.03 toks/s]

 29%|██▊       | 80/279 [03:08<07:49,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 9.75 toks/s, output: 42.40 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.43 toks/s, output: 211.99 toks/s]

 29%|██▉       | 81/279 [03:10<07:47,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.49 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 40.73 toks/s, output: 212.12 toks/s]

 29%|██▉       | 82/279 [03:13<07:44,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.21 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.79 toks/s, output: 212.11 toks/s]

 30%|██▉       | 83/279 [03:15<07:42,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.34 toks/s, output: 212.14 toks/s]

 30%|███       | 84/279 [03:18<07:40,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.21 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.52 toks/s, output: 212.14 toks/s]

 30%|███       | 85/279 [03:20<07:37,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 12.30 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 42.85 toks/s, output: 212.13 toks/s]

 31%|███       | 86/279 [03:22<07:35,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:03,  1.33it/s, est. speed input: 21.31 toks/s, output: 41.30 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.25s/it, est. speed input: 13.65 toks/s, output: 55.87 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 37.10 toks/s, output: 183.79 toks/s]

 31%|███       | 87/279 [03:25<07:32,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.06 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.36 toks/s, output: 212.10 toks/s]

 32%|███▏      | 88/279 [03:27<07:30,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:03,  1.12it/s, est. speed input: 22.49 toks/s, output: 41.61 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.22s/it, est. speed input: 15.36 toks/s, output: 58.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 35.83 toks/s, output: 186.42 toks/s]

 32%|███▏      | 89/279 [03:29<07:27,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.64 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.93 toks/s, output: 212.08 toks/s]

 32%|███▏      | 90/279 [03:32<07:25,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.98s/it, est. speed input: 12.10 toks/s, output: 42.34 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.03s/it, est. speed input: 17.00 toks/s, output: 78.18 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 41.21 toks/s, output: 205.62 toks/s]

 33%|███▎      | 91/279 [03:34<07:22,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.26s/it, est. speed input: 11.48 toks/s, output: 42.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.04 toks/s, output: 210.47 toks/s]

 33%|███▎      | 92/279 [03:36<07:20,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.06 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 38.60 toks/s, output: 212.09 toks/s]

 33%|███▎      | 93/279 [03:39<07:18,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.51 toks/s, output: 42.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.47 toks/s, output: 212.03 toks/s]

 34%|███▎      | 94/279 [03:41<07:16,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.22 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 42.03 toks/s, output: 212.27 toks/s]

 34%|███▍      | 95/279 [03:43<07:13,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.06 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 40.73 toks/s, output: 212.12 toks/s]

 34%|███▍      | 96/279 [03:46<07:11,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.33s/it, est. speed input: 11.27 toks/s, output: 42.06 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.15s/it, est. speed input: 15.33 toks/s, output: 66.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 36.62 toks/s, output: 194.18 toks/s]

 35%|███▍      | 97/279 [03:48<07:08,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 11.03 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 38.19 toks/s, output: 212.17 toks/s]

 35%|███▌      | 98/279 [03:51<07:06,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.37 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.79 toks/s, output: 212.16 toks/s]

 35%|███▌      | 99/279 [03:53<07:04,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.12s/it, est. speed input: 10.84 toks/s, output: 42.40 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.01s/it, est. speed input: 19.12 toks/s, output: 80.72 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 43.33 toks/s, output: 208.15 toks/s]

 36%|███▌      | 100/279 [03:55<07:01,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.28s/it, est. speed input: 17.91 toks/s, output: 42.05 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.15s/it, est. speed input: 15.34 toks/s, output: 65.64 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 37.93 toks/s, output: 193.49 toks/s]

 36%|███▌      | 101/279 [03:58<06:59,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 12.30 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.03 toks/s, output: 212.12 toks/s]

 37%|███▋      | 102/279 [04:00<06:56,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.03s/it, est. speed input: 9.36 toks/s, output: 42.36 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.03s/it, est. speed input: 13.17 toks/s, output: 79.02 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.83 toks/s, output: 206.46 toks/s]

 37%|███▋      | 103/279 [04:02<06:54,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.63s/it, est. speed input: 14.70 toks/s, output: 42.25 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.09s/it, est. speed input: 20.01 toks/s, output: 71.94 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 44.27 toks/s, output: 199.64 toks/s]

 37%|███▋      | 104/279 [04:05<06:52,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.07 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.37 toks/s, output: 212.31 toks/s]

 38%|███▊      | 105/279 [04:07<06:49,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.64 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.10 toks/s, output: 212.20 toks/s]

 38%|███▊      | 106/279 [04:09<06:47,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.98s/it, est. speed input: 6.05 toks/s, output: 42.38 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.03s/it, est. speed input: 13.61 toks/s, output: 78.24 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 40.82 toks/s, output: 205.79 toks/s]

 38%|███▊      | 107/279 [04:12<06:45,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:02,  1.42it/s, est. speed input: 31.26 toks/s, output: 41.20 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.25s/it, est. speed input: 15.79 toks/s, output: 55.06 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 36.27 toks/s, output: 183.07 toks/s]

 39%|███▊      | 108/279 [04:14<06:42,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.40s/it, est. speed input: 14.28 toks/s, output: 42.13 toks/s][A

Processed prompts:  60%|██████    | 3/5 [00:02<00:01,  1.40it/s, est. speed input: 24.33 toks/s, output: 94.76 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 40.12 toks/s, output: 180.12 toks/s]

 39%|███▉      | 109/279 [04:16<06:39,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.56s/it, est. speed input: 15.35 toks/s, output: 42.20 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.11s/it, est. speed input: 19.16 toks/s, output: 70.68 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 48.11 toks/s, output: 198.40 toks/s]

 39%|███▉      | 110/279 [04:19<06:37,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:03,  1.13it/s, est. speed input: 20.28 toks/s, output: 41.70 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:01<00:02,  1.39it/s, est. speed input: 28.24 toks/s, output: 67.24 toks/s][A

Processed prompts:  60%|██████    | 3/5 [00:02<00:01,  1.28it/s, est. speed input: 27.39 toks/s, output: 85.59 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 41.08 toks/s, output: 171.16 toks/s]

 40%|███▉      | 111/279 [04:21<06:34,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:03,  1.10it/s, est. speed input: 20.82 toks/s, output: 41.64 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.22s/it, est. speed input: 14.51 toks/s, output: 58.88 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 37.12 toks/s, output: 186.86 toks/s]

 40%|████      | 112/279 [04:23<06:31,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.09 toks/s, output: 212.12 toks/s]

 41%|████      | 113/279 [04:26<06:30,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.21 toks/s, output: 212.09 toks/s]

 41%|████      | 114/279 [04:28<06:28,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.06 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.22 toks/s, output: 212.18 toks/s]

 41%|████      | 115/279 [04:31<06:26,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.38 toks/s, output: 212.37 toks/s]

 42%|████▏     | 116/279 [04:33<06:23,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.22 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 40.75 toks/s, output: 212.26 toks/s]

 42%|████▏     | 117/279 [04:35<06:21,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.37 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.52 toks/s, output: 212.16 toks/s]

 42%|████▏     | 118/279 [04:38<06:19,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.48 toks/s, output: 42.40 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.89 toks/s, output: 212.00 toks/s]

 43%|████▎     | 119/279 [04:40<06:17,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.37 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.11 toks/s, output: 212.26 toks/s]

 43%|████▎     | 120/279 [04:42<06:14,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.19s/it, est. speed input: 9.58 toks/s, output: 42.44 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:02,  1.00it/s, est. speed input: 15.72 toks/s, output: 82.01 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 42.49 toks/s, output: 209.48 toks/s]

 43%|████▎     | 121/279 [04:45<06:12,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.35 toks/s, output: 212.23 toks/s]

 44%|████▎     | 122/279 [04:47<06:10,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.91 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.90 toks/s, output: 212.08 toks/s]

 44%|████▍     | 123/279 [04:49<06:07,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 14.00 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 44.98 toks/s, output: 212.15 toks/s]

 44%|████▍     | 124/279 [04:52<06:05,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.06 toks/s, output: 212.28 toks/s]

 45%|████▍     | 125/279 [04:54<06:03,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.51 toks/s, output: 212.42 toks/s]

 45%|████▌     | 126/279 [04:56<06:00,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 41.60 toks/s, output: 212.26 toks/s]

 46%|████▌     | 127/279 [04:59<05:58,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 10.61 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 43.30 toks/s, output: 212.27 toks/s]

 46%|████▌     | 128/279 [05:01<05:55,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 41.60 toks/s, output: 212.24 toks/s]

 46%|████▌     | 129/279 [05:04<05:53,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.80 toks/s, output: 212.19 toks/s]

 47%|████▋     | 130/279 [05:06<05:51,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.52 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.42 toks/s, output: 212.29 toks/s]

 47%|████▋     | 131/279 [05:08<05:48,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.52 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.50 toks/s, output: 212.36 toks/s]

 47%|████▋     | 132/279 [05:11<05:46,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.63s/it, est. speed input: 9.19 toks/s, output: 42.27 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.09s/it, est. speed input: 13.20 toks/s, output: 71.96 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 38.32 toks/s, output: 199.69 toks/s]

 48%|████▊     | 133/279 [05:13<05:43,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:03,  1.10it/s, est. speed input: 24.10 toks/s, output: 41.63 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.22s/it, est. speed input: 15.35 toks/s, output: 58.85 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 36.25 toks/s, output: 186.78 toks/s]

 48%|████▊     | 134/279 [05:15<05:41,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.82s/it, est. speed input: 9.34 toks/s, output: 42.32 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.06s/it, est. speed input: 15.31 toks/s, output: 75.30 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 39.13 toks/s, output: 202.90 toks/s]

 48%|████▊     | 135/279 [05:18<05:38,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:01,  3.03it/s, est. speed input: 48.43 toks/s, output: 39.35 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.32s/it, est. speed input: 14.55 toks/s, output: 48.35 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 36.37 toks/s, output: 176.69 toks/s]

 49%|████▊     | 136/279 [05:20<05:35,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:03,  1.19it/s, est. speed input: 28.55 toks/s, output: 41.63 toks/s][A

Processed prompts:  60%|██████    | 3/5 [00:02<00:01,  1.30it/s, est. speed input: 27.44 toks/s, output: 72.88 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 39.86 toks/s, output: 158.60 toks/s]

 49%|████▉     | 137/279 [05:22<05:32,  2.34s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.49 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.08 toks/s, output: 212.23 toks/s]

 49%|████▉     | 138/279 [05:25<05:31,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.45s/it, est. speed input: 14.52 toks/s, output: 42.18 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:01<00:02,  1.25it/s, est. speed input: 25.11 toks/s, output: 76.44 toks/s][A

Processed prompts:  60%|██████    | 3/5 [00:02<00:01,  1.46it/s, est. speed input: 25.59 toks/s, output: 101.08 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 43.07 toks/s, output: 186.36 toks/s]

 50%|████▉     | 139/279 [05:27<05:28,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.96s/it, est. speed input: 12.76 toks/s, output: 42.36 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.04s/it, est. speed input: 18.71 toks/s, output: 77.80 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 40.81 toks/s, output: 205.33 toks/s]

 50%|█████     | 140/279 [05:29<05:26,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.64 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.79 toks/s, output: 212.31 toks/s]

 51%|█████     | 141/279 [05:32<05:24,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.35s/it, est. speed input: 17.00 toks/s, output: 42.13 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.14s/it, est. speed input: 16.63 toks/s, output: 66.94 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 47.75 toks/s, output: 194.82 toks/s]

 51%|█████     | 142/279 [05:34<05:22,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 9.34 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 41.19 toks/s, output: 212.30 toks/s]

 51%|█████▏    | 143/279 [05:36<05:19,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.07 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 41.19 toks/s, output: 212.33 toks/s]

 52%|█████▏    | 144/279 [05:39<05:17,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.07 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 38.63 toks/s, output: 212.26 toks/s]

 52%|█████▏    | 145/279 [05:41<05:15,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.93 toks/s, output: 212.24 toks/s]

 52%|█████▏    | 146/279 [05:43<05:13,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 46.71 toks/s, output: 212.33 toks/s]

 53%|█████▎    | 147/279 [05:46<05:10,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.37 toks/s, output: 212.34 toks/s]

 53%|█████▎    | 148/279 [05:48<05:08,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 10.62 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 44.59 toks/s, output: 212.35 toks/s]

 53%|█████▎    | 149/279 [05:51<05:06,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.05s/it, est. speed input: 9.25 toks/s, output: 42.35 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.02s/it, est. speed input: 15.29 toks/s, output: 79.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.91 toks/s, output: 206.78 toks/s]

 54%|█████▍    | 150/279 [05:53<05:03,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.01s/it, est. speed input: 9.47 toks/s, output: 42.36 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.03s/it, est. speed input: 14.45 toks/s, output: 78.62 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.97 toks/s, output: 206.10 toks/s]

 54%|█████▍    | 151/279 [05:55<05:01,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 10.62 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 41.62 toks/s, output: 212.35 toks/s]

 54%|█████▍    | 152/279 [05:58<04:59,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.08 toks/s, output: 212.24 toks/s]

 55%|█████▍    | 153/279 [06:00<04:56,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.07 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.37 toks/s, output: 212.34 toks/s]

 55%|█████▌    | 154/279 [06:02<04:54,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.03s/it, est. speed input: 9.36 toks/s, output: 42.38 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.03s/it, est. speed input: 16.15 toks/s, output: 79.06 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 42.50 toks/s, output: 206.55 toks/s]

 56%|█████▌    | 155/279 [06:05<04:52,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.24s/it, est. speed input: 6.25 toks/s, output: 42.42 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:02,  1.01it/s, est. speed input: 11.89 toks/s, output: 82.80 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.09 toks/s, output: 210.17 toks/s]

 56%|█████▌    | 156/279 [06:07<04:49,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.64 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 43.31 toks/s, output: 212.33 toks/s]

 56%|█████▋    | 157/279 [06:09<04:47,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.94s/it, est. speed input: 6.72 toks/s, output: 42.37 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.04s/it, est. speed input: 14.89 toks/s, output: 77.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 39.99 toks/s, output: 205.03 toks/s]

 57%|█████▋    | 158/279 [06:12<04:44,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.07 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 42.47 toks/s, output: 212.37 toks/s]

 57%|█████▋    | 159/279 [06:14<04:42,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:03,  1.26it/s, est. speed input: 23.92 toks/s, output: 41.54 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.24s/it, est. speed input: 14.09 toks/s, output: 56.79 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 37.57 toks/s, output: 184.88 toks/s]

 57%|█████▋    | 160/279 [06:16<04:39,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 12.74 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 42.89 toks/s, output: 212.32 toks/s]

 58%|█████▊    | 161/279 [06:19<04:37,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.92 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 42.90 toks/s, output: 212.36 toks/s]

 58%|█████▊    | 162/279 [06:21<04:35,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.07 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.52 toks/s, output: 212.31 toks/s]

 58%|█████▊    | 163/279 [06:24<04:33,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.14s/it, est. speed input: 8.86 toks/s, output: 42.44 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.01s/it, est. speed input: 14.88 toks/s, output: 81.20 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 38.68 toks/s, output: 208.72 toks/s]

 59%|█████▉    | 164/279 [06:26<04:30,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.01s/it, est. speed input: 9.47 toks/s, output: 42.38 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.03s/it, est. speed input: 16.16 toks/s, output: 78.65 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 39.96 toks/s, output: 206.17 toks/s]

 59%|█████▉    | 165/279 [06:28<04:28,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.06 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.90 toks/s, output: 212.09 toks/s]

 59%|█████▉    | 166/279 [06:31<04:26,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.07 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 42.90 toks/s, output: 212.38 toks/s]

 60%|█████▉    | 167/279 [06:33<04:23,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.05s/it, est. speed input: 9.75 toks/s, output: 42.43 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.02s/it, est. speed input: 17.02 toks/s, output: 79.55 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 38.71 toks/s, output: 207.14 toks/s]

 60%|██████    | 168/279 [06:35<04:21,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.22 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.82 toks/s, output: 212.32 toks/s]

 61%|██████    | 169/279 [06:38<04:19,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.22 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 43.32 toks/s, output: 212.35 toks/s]

 61%|██████    | 170/279 [06:40<04:16,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 9.34 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 48.80 toks/s, output: 212.18 toks/s]

 61%|██████▏   | 171/279 [06:42<04:14,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 11.89 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 45.86 toks/s, output: 212.29 toks/s]

 62%|██████▏   | 172/279 [06:45<04:12,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 49.25 toks/s, output: 212.30 toks/s]

 62%|██████▏   | 173/279 [06:47<04:09,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.22 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 43.33 toks/s, output: 212.42 toks/s]

 62%|██████▏   | 174/279 [06:49<04:07,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 9.34 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 41.19 toks/s, output: 212.34 toks/s]

 63%|██████▎   | 175/279 [06:52<04:05,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 11.04 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 53.05 toks/s, output: 212.20 toks/s]

 63%|██████▎   | 176/279 [06:54<04:02,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.08s/it, est. speed input: 9.63 toks/s, output: 42.39 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:02,  1.01it/s, est. speed input: 14.74 toks/s, output: 80.62 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 41.22 toks/s, output: 206.51 toks/s]

 63%|██████▎   | 177/279 [06:57<04:00,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.82s/it, est. speed input: 10.45 toks/s, output: 42.35 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.06s/it, est. speed input: 14.47 toks/s, output: 75.34 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 39.58 toks/s, output: 203.01 toks/s]

 64%|██████▍   | 178/279 [06:59<03:57,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.52 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.92 toks/s, output: 212.34 toks/s]

 64%|██████▍   | 179/279 [07:01<03:55,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 9.34 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 46.72 toks/s, output: 212.36 toks/s]

 65%|██████▍   | 180/279 [07:04<03:53,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 13.16 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 43.71 toks/s, output: 212.16 toks/s]

 65%|██████▍   | 181/279 [07:06<03:50,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 11.04 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 46.27 toks/s, output: 212.27 toks/s]

 65%|██████▌   | 182/279 [07:08<03:48,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.97 toks/s, output: 212.34 toks/s]

 66%|██████▌   | 183/279 [07:11<03:46,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.28 toks/s, output: 212.35 toks/s]

 66%|██████▌   | 184/279 [07:13<03:43,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.85 toks/s, output: 212.30 toks/s]

 66%|██████▋   | 185/279 [07:15<03:41,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.83 toks/s, output: 212.19 toks/s]

 67%|██████▋   | 186/279 [07:18<03:39,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.99 toks/s, output: 212.23 toks/s]

 67%|██████▋   | 187/279 [07:20<03:36,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.89s/it, est. speed input: 8.47 toks/s, output: 42.34 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.05s/it, est. speed input: 13.18 toks/s, output: 76.55 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 36.15 toks/s, output: 204.12 toks/s]

 67%|██████▋   | 188/279 [07:22<03:34,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.64 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.39 toks/s, output: 212.30 toks/s]

 68%|██████▊   | 189/279 [07:25<03:32,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.65 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.56 toks/s, output: 212.38 toks/s]

 68%|██████▊   | 190/279 [07:27<03:29,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.06 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.22 toks/s, output: 212.19 toks/s]

 68%|██████▊   | 191/279 [07:30<03:27,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.37 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.22 toks/s, output: 212.18 toks/s]

 69%|██████▉   | 192/279 [07:32<03:25,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.22 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.12 toks/s, output: 212.33 toks/s]

 69%|██████▉   | 193/279 [07:34<03:22,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 11.47 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.08 toks/s, output: 212.38 toks/s]

 70%|██████▉   | 194/279 [07:37<03:20,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.51 toks/s, output: 212.27 toks/s]

 70%|██████▉   | 195/279 [07:39<03:17,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 9.77 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 38.66 toks/s, output: 212.42 toks/s]

 70%|███████   | 196/279 [07:41<03:15,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 9.34 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.90 toks/s, output: 212.25 toks/s]

 71%|███████   | 197/279 [07:44<03:13,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.33s/it, est. speed input: 6.43 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.91 toks/s, output: 211.87 toks/s]

 71%|███████   | 198/279 [07:46<03:10,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 44.60 toks/s, output: 212.39 toks/s]

 71%|███████▏  | 199/279 [07:48<03:08,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.82 toks/s, output: 212.45 toks/s]

 72%|███████▏  | 200/279 [07:51<03:06,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.33s/it, est. speed input: 6.43 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.12 toks/s, output: 211.87 toks/s]

 72%|███████▏  | 201/279 [07:53<03:03,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.50 toks/s, output: 212.22 toks/s]

 72%|███████▏  | 202/279 [07:55<03:01,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:02,  1.77it/s, est. speed input: 30.18 toks/s, output: 40.83 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.28s/it, est. speed input: 14.53 toks/s, output: 52.56 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 33.33 toks/s, output: 180.74 toks/s]

 73%|███████▎  | 203/279 [07:58<02:58,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 9.77 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.80 toks/s, output: 212.35 toks/s]

 73%|███████▎  | 204/279 [08:00<02:56,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.65 toks/s, output: 42.50 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 42.07 toks/s, output: 212.49 toks/s]

 73%|███████▎  | 205/279 [08:02<02:54,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 9.77 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 41.63 toks/s, output: 212.41 toks/s]

 74%|███████▍  | 206/279 [08:05<02:51,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.92 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.83 toks/s, output: 212.37 toks/s]

 74%|███████▍  | 207/279 [08:07<02:49,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.64 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.70 toks/s, output: 212.32 toks/s]

 75%|███████▍  | 208/279 [08:10<02:47,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.21s/it, est. speed input: 7.23 toks/s, output: 42.45 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:02,  1.01it/s, est. speed input: 12.32 toks/s, output: 82.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.57 toks/s, output: 209.91 toks/s]

 75%|███████▍  | 209/279 [08:12<02:44,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.58 toks/s, output: 212.34 toks/s]

 75%|███████▌  | 210/279 [08:14<02:42,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.81 toks/s, output: 212.26 toks/s]

 76%|███████▌  | 211/279 [08:17<02:40,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.79 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 42.03 toks/s, output: 212.29 toks/s]

 76%|███████▌  | 212/279 [08:19<02:37,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 11.04 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 38.63 toks/s, output: 212.26 toks/s]

 76%|███████▋  | 213/279 [08:21<02:35,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.89s/it, est. speed input: 13.23 toks/s, output: 42.35 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.05s/it, est. speed input: 19.14 toks/s, output: 76.58 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 41.26 toks/s, output: 204.18 toks/s]

 77%|███████▋  | 214/279 [08:24<02:33,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.45s/it, est. speed input: 12.44 toks/s, output: 42.16 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.13s/it, est. speed input: 17.05 toks/s, output: 68.61 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 43.46 toks/s, output: 196.43 toks/s]

 77%|███████▋  | 215/279 [08:26<02:30,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.90 toks/s, output: 212.09 toks/s]

 77%|███████▋  | 216/279 [08:28<02:28,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.66s/it, est. speed input: 10.26 toks/s, output: 42.27 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.09s/it, est. speed input: 14.05 toks/s, output: 72.38 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 44.70 toks/s, output: 200.10 toks/s]

 78%|███████▊  | 217/279 [08:31<02:25,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 13.16 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.89 toks/s, output: 212.18 toks/s]

 78%|███████▊  | 218/279 [08:33<02:23,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 42.47 toks/s, output: 212.34 toks/s]

 78%|███████▊  | 219/279 [08:35<02:21,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.94 toks/s, output: 212.31 toks/s]

 79%|███████▉  | 220/279 [08:38<02:19,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 41.20 toks/s, output: 212.39 toks/s]

 79%|███████▉  | 221/279 [08:40<02:16,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.10 toks/s, output: 212.33 toks/s]

 80%|███████▉  | 222/279 [08:43<02:14,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.37 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.42 toks/s, output: 212.27 toks/s]

 80%|███████▉  | 223/279 [08:45<02:11,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.52 toks/s, output: 212.15 toks/s]

 80%|████████  | 224/279 [08:47<02:09,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 43.32 toks/s, output: 212.35 toks/s]

 81%|████████  | 225/279 [08:50<02:07,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.22 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 41.20 toks/s, output: 212.39 toks/s]

 81%|████████  | 226/279 [08:52<02:04,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.15 toks/s, output: 212.35 toks/s]

 81%|████████▏ | 227/279 [08:54<02:02,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.81 toks/s, output: 212.26 toks/s]

 82%|████████▏ | 228/279 [08:57<02:00,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.68 toks/s, output: 212.21 toks/s]

 82%|████████▏ | 229/279 [08:59<01:57,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 9.34 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.49 toks/s, output: 212.33 toks/s]

 82%|████████▏ | 230/279 [09:01<01:55,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 38.65 toks/s, output: 212.39 toks/s]

 83%|████████▎ | 231/279 [09:04<01:53,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.67 toks/s, output: 212.29 toks/s]

 83%|████████▎ | 232/279 [09:06<01:50,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 11.89 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.49 toks/s, output: 212.34 toks/s]

 84%|████████▎ | 233/279 [09:08<01:48,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.19s/it, est. speed input: 7.75 toks/s, output: 42.41 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:02,  1.00it/s, est. speed input: 16.14 toks/s, output: 81.96 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.09 toks/s, output: 209.33 toks/s]

 84%|████████▍ | 234/279 [09:11<01:46,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.22 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.53 toks/s, output: 212.24 toks/s]

 84%|████████▍ | 235/279 [09:13<01:43,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.49 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.82 toks/s, output: 212.34 toks/s]

 85%|████████▍ | 236/279 [09:16<01:41,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.01 toks/s, output: 212.38 toks/s]

 85%|████████▍ | 237/279 [09:18<01:38,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.10 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.39 toks/s, output: 212.29 toks/s]

 85%|████████▌ | 238/279 [09:20<01:36,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.09 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.35 toks/s, output: 212.06 toks/s]

 86%|████████▌ | 239/279 [09:23<01:34,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 29.72 toks/s, output: 212.26 toks/s]

 86%|████████▌ | 240/279 [09:25<01:31,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.59 toks/s, output: 212.40 toks/s]

 86%|████████▋ | 241/279 [09:27<01:29,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.58 toks/s, output: 212.39 toks/s]

 87%|████████▋ | 242/279 [09:30<01:27,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.79 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.52 toks/s, output: 212.30 toks/s]

 87%|████████▋ | 243/279 [09:32<01:24,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 10.62 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.39 toks/s, output: 212.28 toks/s]

 87%|████████▋ | 244/279 [09:34<01:22,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.94 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.42 toks/s, output: 212.28 toks/s]

 88%|████████▊ | 245/279 [09:37<01:20,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 4.25 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.86 toks/s, output: 212.39 toks/s]

 88%|████████▊ | 246/279 [09:39<01:17,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:00<00:02,  1.38it/s, est. speed input: 17.91 toks/s, output: 41.32 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:01<00:02,  1.36it/s, est. speed input: 17.08 toks/s, output: 62.85 toks/s][A

Processed prompts:  60%|██████    | 3/5 [00:02<00:01,  1.26it/s, est. speed input: 17.56 toks/s, output: 82.21 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.14it/s, est. speed input: 36.39 toks/s, output: 167.84 toks/s]

 89%|████████▊ | 247/279 [09:41<01:15,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.49s/it, est. speed input: 10.71 toks/s, output: 42.19 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.12s/it, est. speed input: 15.34 toks/s, output: 69.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 36.21 toks/s, output: 197.23 toks/s]

 89%|████████▉ | 248/279 [09:44<01:12,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.82s/it, est. speed input: 7.70 toks/s, output: 42.33 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.06s/it, est. speed input: 12.77 toks/s, output: 75.32 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 33.61 toks/s, output: 202.96 toks/s]

 89%|████████▉ | 249/279 [09:46<01:10,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.64 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.69 toks/s, output: 212.27 toks/s]

 90%|████████▉ | 250/279 [09:49<01:08,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.64 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.78 toks/s, output: 212.27 toks/s]

 90%|████████▉ | 251/279 [09:51<01:05,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 6.79 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.66 toks/s, output: 212.27 toks/s]

 90%|█████████ | 252/279 [09:53<01:03,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.37 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 29.31 toks/s, output: 212.40 toks/s]

 91%|█████████ | 253/279 [09:56<01:01,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 31.87 toks/s, output: 212.45 toks/s]

 91%|█████████ | 254/279 [09:58<00:58,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.70s/it, est. speed input: 7.64 toks/s, output: 42.30 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.08s/it, est. speed input: 11.92 toks/s, output: 73.25 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 29.81 toks/s, output: 201.00 toks/s]

 91%|█████████▏| 255/279 [10:00<00:56,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.06 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.92 toks/s, output: 212.17 toks/s]

 92%|█████████▏| 256/279 [10:03<00:54,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 7.21 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 32.25 toks/s, output: 212.16 toks/s]

 92%|█████████▏| 257/279 [10:05<00:51,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:06,  1.66s/it, est. speed input: 7.25 toks/s, output: 42.28 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.09s/it, est. speed input: 11.93 toks/s, output: 72.41 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 31.52 toks/s, output: 200.19 toks/s]

 92%|█████████▏| 258/279 [10:07<00:49,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.08 toks/s, output: 42.50 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.40 toks/s, output: 212.50 toks/s]

 93%|█████████▎| 259/279 [10:10<00:47,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 6.80 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 30.59 toks/s, output: 212.41 toks/s]

 93%|█████████▎| 260/279 [10:12<00:44,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.31s/it, est. speed input: 6.93 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.50 toks/s, output: 211.38 toks/s]

 94%|█████████▎| 261/279 [10:14<00:42,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.08s/it, est. speed input: 6.74 toks/s, output: 42.38 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.02s/it, est. speed input: 14.02 toks/s, output: 79.87 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.38 toks/s, output: 207.30 toks/s]

 94%|█████████▍| 262/279 [10:17<00:40,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.52 toks/s, output: 42.45 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.50 toks/s, output: 212.23 toks/s]

 94%|█████████▍| 263/279 [10:19<00:37,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.50 toks/s, output: 42.50 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 38.24 toks/s, output: 212.46 toks/s]

 95%|█████████▍| 264/279 [10:21<00:35,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.26s/it, est. speed input: 10.17 toks/s, output: 42.46 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 33.99 toks/s, output: 210.75 toks/s]

 95%|█████████▍| 265/279 [10:24<00:32,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:07,  1.84s/it, est. speed input: 8.69 toks/s, output: 42.36 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.06s/it, est. speed input: 12.77 toks/s, output: 75.77 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 31.07 toks/s, output: 203.45 toks/s]

 95%|█████████▌| 266/279 [10:26<00:30,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.52 toks/s, output: 42.43 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 35.21 toks/s, output: 212.14 toks/s]

 96%|█████████▌| 267/279 [10:29<00:28,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.37 toks/s, output: 212.31 toks/s]

 96%|█████████▌| 268/279 [10:31<00:25,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 5.95 toks/s, output: 42.49 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.81 toks/s, output: 212.43 toks/s]

 96%|█████████▋| 269/279 [10:33<00:23,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.07 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.94 toks/s, output: 212.32 toks/s]

 97%|█████████▋| 270/279 [10:36<00:21,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.92 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 37.80 toks/s, output: 212.35 toks/s]

 97%|█████████▋| 271/279 [10:38<00:18,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:08,  2.24s/it, est. speed input: 5.36 toks/s, output: 42.43 toks/s][A

Processed prompts:  60%|██████    | 3/5 [00:02<00:01,  1.60it/s, est. speed input: 19.11 toks/s, output: 123.60 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 34.40 toks/s, output: 208.53 toks/s]

 97%|█████████▋| 272/279 [10:40<00:16,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 5.52 toks/s, output: 42.42 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.90 toks/s, output: 212.09 toks/s]

 98%|█████████▊| 273/279 [10:43<00:14,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.65 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 38.23 toks/s, output: 212.38 toks/s]

 98%|█████████▊| 274/279 [10:45<00:11,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:01<00:05,  1.31s/it, est. speed input: 12.23 toks/s, output: 42.05 toks/s][A

Processed prompts:  40%|████      | 2/5 [00:02<00:03,  1.15s/it, est. speed input: 13.64 toks/s, output: 66.07 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.13it/s, est. speed input: 37.08 toks/s, output: 193.91 toks/s]

 99%|█████████▊| 275/279 [10:47<00:09,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 7.65 toks/s, output: 42.48 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.07 toks/s, output: 212.36 toks/s]

 99%|█████████▉| 276/279 [10:50<00:07,  2.35s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.26s/it, est. speed input: 7.07 toks/s, output: 42.40 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.49 toks/s, output: 210.44 toks/s]

 99%|█████████▉| 277/279 [10:52<00:04,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.35s/it, est. speed input: 8.49 toks/s, output: 42.47 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 39.91 toks/s, output: 212.30 toks/s]

100%|█████████▉| 278/279 [10:54<00:02,  2.36s/it]

Processed prompts:   0%|          | 0/5 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s][A

Processed prompts:  20%|██        | 1/5 [00:02<00:09,  2.36s/it, est. speed input: 8.06 toks/s, output: 42.44 toks/s][A
Processed prompts: 100%|██████████| 5/5 [00:02<00:00,  2.12it/s, est. speed input: 36.07 toks/s, output: 212.15 toks/s]

100%|██████████| 279/279 [10:57<00:00,  2.36s/it]
100%|██████████| 279/279 [10:57<00:00,  2.36s/it]

Processed prompts:   0%|          | 0/4 [00:00<?, ?it/s, est. speed input: 0.00 toks/s, output: 0.00 toks/s]
Processed prompts:  25%|██▌       | 1/4 [00:02<00:07,  2.33s/it, est. speed input: 7.29 toks/s, output: 42.86 toks/s]
Processed prompts: 100%|██████████| 4/4 [00:02<00:00,  1.71it/s, est. speed input: 30.00 toks/s, output: 171.41 toks/s]
