==========================================
SLURM_JOB_ID = 81353
SLURM_NODELIST = gpunode01
==========================================
WARNING 05-03 19:01:57 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 19:02:11 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 19:02:13 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-hf...
INFO 05-03 19:02:13 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 19:02:21 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 05-03 19:02:23 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 19:02:23 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 19:02:26 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 19:02:26 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 19:02:43 model_runner.py:1523] Graph capturing finished in 17 secs.
WARNING 05-03 19:07:53 scheduler.py:895] Input prompt (5952 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:13:57 scheduler.py:895] Input prompt (4979 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:15:13 scheduler.py:895] Input prompt (4986 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:15:58 scheduler.py:895] Input prompt (5653 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:16:15 scheduler.py:895] Input prompt (6773 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:20:48 scheduler.py:895] Input prompt (5504 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:21:01 scheduler.py:895] Input prompt (5504 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:21:53 scheduler.py:895] Input prompt (4614 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:22:02 scheduler.py:895] Input prompt (6703 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:22:19 scheduler.py:895] Input prompt (5187 tokens) is too long and exceeds limit of 4096
overall result: 0.0
WARNING 05-03 19:23:31 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 19:23:42 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 19:23:44 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-hf...
INFO 05-03 19:23:44 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 19:24:26 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 05-03 19:24:27 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 19:24:27 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 19:24:31 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 19:24:31 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 19:24:43 model_runner.py:1523] Graph capturing finished in 12 secs.
WARNING 05-03 19:31:25 scheduler.py:895] Input prompt (6601 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:32:51 scheduler.py:895] Input prompt (4456 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:36:05 scheduler.py:895] Input prompt (4394 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:37:53 scheduler.py:895] Input prompt (4503 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:39:01 scheduler.py:895] Input prompt (6151 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:40:38 scheduler.py:895] Input prompt (6158 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:41:38 scheduler.py:895] Input prompt (6041 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:42:01 scheduler.py:895] Input prompt (7173 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:47:46 scheduler.py:895] Input prompt (6077 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:48:03 scheduler.py:895] Input prompt (6077 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:49:11 scheduler.py:895] Input prompt (5221 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:49:24 scheduler.py:895] Input prompt (7419 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:49:46 scheduler.py:895] Input prompt (5848 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:49:57 scheduler.py:895] Input prompt (4277 tokens) is too long and exceeds limit of 4096
overall result: 0.0
WARNING 05-03 19:51:11 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 19:51:20 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 19:51:21 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-chat-hf...
INFO 05-03 19:51:21 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 19:56:10 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 05-03 19:56:12 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 19:56:12 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 19:56:15 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 19:56:15 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 19:56:27 model_runner.py:1523] Graph capturing finished in 13 secs.
WARNING 05-03 20:00:10 scheduler.py:895] Input prompt (5952 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:03:25 scheduler.py:895] Input prompt (4979 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:03:58 scheduler.py:895] Input prompt (4986 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:04:33 scheduler.py:895] Input prompt (5653 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:04:45 scheduler.py:895] Input prompt (6773 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:06:49 scheduler.py:895] Input prompt (5504 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:06:55 scheduler.py:895] Input prompt (5504 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:07:27 scheduler.py:895] Input prompt (4614 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:07:37 scheduler.py:895] Input prompt (6703 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:07:47 scheduler.py:895] Input prompt (5187 tokens) is too long and exceeds limit of 4096
overall result: 0.0
WARNING 05-03 20:08:31 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 20:08:40 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 20:08:42 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-chat-hf...
INFO 05-03 20:08:42 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 20:09:17 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 05-03 20:09:19 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 20:09:19 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 20:09:22 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 20:09:22 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 20:09:35 model_runner.py:1523] Graph capturing finished in 13 secs.
WARNING 05-03 20:14:29 scheduler.py:895] Input prompt (6601 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:15:46 scheduler.py:895] Input prompt (4456 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:17:51 scheduler.py:895] Input prompt (4394 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:18:59 scheduler.py:895] Input prompt (4503 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:19:47 scheduler.py:895] Input prompt (6151 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:20:47 scheduler.py:895] Input prompt (6158 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:21:35 scheduler.py:895] Input prompt (6041 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:21:56 scheduler.py:895] Input prompt (7173 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:26:06 scheduler.py:895] Input prompt (6077 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:26:15 scheduler.py:895] Input prompt (6077 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:27:14 scheduler.py:895] Input prompt (5221 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:27:26 scheduler.py:895] Input prompt (7419 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:27:46 scheduler.py:895] Input prompt (5848 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:27:57 scheduler.py:895] Input prompt (4277 tokens) is too long and exceeds limit of 4096
overall result: 0.0
WARNING 05-03 20:28:48 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 20:28:58 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='Qwen/Qwen2.5-7B-Instruct', speculative_config=None, tokenizer='Qwen/Qwen2.5-7B-Instruct', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.bfloat16, max_seq_len=32768, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=Qwen/Qwen2.5-7B-Instruct, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 20:28:59 model_runner.py:1056] Starting to load model Qwen/Qwen2.5-7B-Instruct...
INFO 05-03 20:28:59 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 20:31:44 model_runner.py:1067] Loading model weights took 14.2487 GB
INFO 05-03 20:31:49 gpu_executor.py:122] # GPU blocks: 27715, # CPU blocks: 4681
INFO 05-03 20:31:49 gpu_executor.py:126] Maximum concurrency for 32768 tokens per request: 13.53x
INFO 05-03 20:31:53 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 20:31:53 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 20:32:03 model_runner.py:1523] Graph capturing finished in 10 secs.
overall result: 0.0
WARNING 05-03 20:44:05 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 20:44:15 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='Qwen/Qwen2.5-7B-Instruct', speculative_config=None, tokenizer='Qwen/Qwen2.5-7B-Instruct', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.bfloat16, max_seq_len=32768, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=Qwen/Qwen2.5-7B-Instruct, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 20:44:16 model_runner.py:1056] Starting to load model Qwen/Qwen2.5-7B-Instruct...
INFO 05-03 20:44:16 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 20:44:42 model_runner.py:1067] Loading model weights took 14.2487 GB
INFO 05-03 20:44:46 gpu_executor.py:122] # GPU blocks: 27715, # CPU blocks: 4681
INFO 05-03 20:44:46 gpu_executor.py:126] Maximum concurrency for 32768 tokens per request: 13.53x
INFO 05-03 20:44:50 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 20:44:50 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 20:45:00 model_runner.py:1523] Graph capturing finished in 10 secs.
overall result: 0.0
