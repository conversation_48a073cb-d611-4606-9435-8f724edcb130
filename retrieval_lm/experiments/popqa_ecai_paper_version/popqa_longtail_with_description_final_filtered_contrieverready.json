[{"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is the occupation of <PERSON>, the Canadian public figure?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s profession?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation as a Canadian?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What was <PERSON>'s (1830–1888) profession?"}, {"text": "What is <PERSON>'s occupation as a British?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What was the occupation of <PERSON>, the English Benedictine monk from the 17th century?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation as an Irishman?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What was <PERSON>'s (1883–1968) profession?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is the occupation of <PERSON>, the British national?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is the occupation of <PERSON><PERSON>w Swe, the Burmese?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What was the occupation of <PERSON>, Jr. (1762–1826)?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What was the occupation of <PERSON>, the German (1673–1731)?"}, {"text": "What is <PERSON>'s profession?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation, who was a French encyclopedist and military figure?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation as a Canadian?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What was <PERSON>'s (1901–1977) profession?"}, {"text": "What is Frits Castricum's occupation?"}, {"text": "What is <PERSON> <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is the occupation of <PERSON>, the French?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON> <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s profession?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is the occupation of <PERSON>?"}, {"text": "What is <PERSON><PERSON><PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is the occupation of <PERSON>, who is Uruguayan?"}, {"text": "What is <PERSON>'s occupation as an Italian?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is Sir <PERSON>, 3rd Baronet's occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON>z<PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON>, the Russian mathematician, born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON>, the British orienteer, born?"}, {"text": "In what city was <PERSON> (1919–1971), the Italian footballer, born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was Volodymyr <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON>, the English footballer born in 1958, born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON>, the Englishman (1878–1962), born?"}, {"text": "In what city was <PERSON>, the American composer (1929–1998), born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> (1891–1966), the English footballer, born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "\"In what city was <PERSON>, an Apache Indian scout, born?\""}, {"text": "In what city was <PERSON>, the Scottish footballer born in 1907?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON>, the Scottish (1948–2002), born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was Giovanni <PERSON>, the Italian, born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON>, the Scottish footballer born in 1977?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was the German <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON>, the Mexican footballer, born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON>, the amateur international cricketer, born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON>, the Montenegrin footballer, born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON>, the British racing driver, born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON>, an Ohio native from the United States born in 1880?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was Florence <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON>, the English cricketer born in 1974?"}, {"text": "In what city was <PERSON>zas<PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> Vilde born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON>, the French scholar, physician, and poet (1617–1687), born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON>, the New Zealand mathematician, public servant, and university administrator, born?"}, {"text": "In what city was the Australian politician <PERSON>'s birthplace?"}, {"text": "In what city was the Lithuanian musician <PERSON><PERSON><PERSON> born?"}, {"text": "\"In what city was <PERSON><PERSON> born?\""}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was the Canadian Paralympic athlete <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON>, the Chilean musician, born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON>, the Hungarian (1933–2023), born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON> Mohammed born?"}, {"text": "In what city was <PERSON>, the Canadian (1841-1889), born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "\"In what city was <PERSON>, the German, born?\""}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "\"In what city was <PERSON>, known for his involvement in ice sledge hockey, born?\""}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON>, the Spanish economist, born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON>, the Japanese player, born?"}, {"text": "In what city was <PERSON><PERSON><PERSON>, the Swedish coxswain, born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was the Swedish athlete <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON>, the Irish, born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON>, the English (1884–1947), born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was Franghís<PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "What genre is the single Drive On by Brother Beyond?"}, {"text": "What is the genre of the 1995 song Mother by Luna Sea?"}, {"text": "What genre is <PERSON> and My Friend?"}, {"text": "What is the genre of Unknown, the 1988 anthology?"}, {"text": "What genre is the <PERSON> album titled *Reach*?"}, {"text": "What genre is <PERSON>?"}, {"text": "What genre is <PERSON>?"}, {"text": "What genre is Operation Sabotage?"}, {"text": "What genre is the 2006 film, *The Gap*?"}, {"text": "What is the genre of Dark Matter, the book by <PERSON>?"}, {"text": "What genre does the book *Chaotic* by <PERSON> belong to?"}, {"text": "What is the genre of Flare, an album by <PERSON><PERSON>?"}, {"text": "What genre is Brain Slaves?"}, {"text": "What is the genre of The New World, the 2011 film directed by <PERSON><PERSON>?"}, {"text": "What genre is <PERSON><PERSON><PERSON><PERSON><PERSON>?"}, {"text": "What is the genre of Drill, the British band?"}, {"text": "What is the genre of Settle, the American musical group?"}, {"text": "What genre is Magic Music?"}, {"text": "What genre is Voyage, the 2011 debut studio album by The Sound of Arrows?"}, {"text": "What genre is <PERSON>?"}, {"text": "What genre is <PERSON><PERSON>?"}, {"text": "What is the genre of the single *To Mother*?"}, {"text": "What genre is the 2013 Indonesian television series Magic?"}, {"text": "What is the genre of The Harrowing, a book by <PERSON>?"}, {"text": "What is the genre of the song Yellow by <PERSON><PERSON>?"}, {"text": "What is the genre of Hara, a sculpture by <PERSON>?"}, {"text": "What genre is Nightdreamers?"}, {"text": "What genre is The Song of the Suburbs?"}, {"text": "What genre is The Club, a 2010 show?"}, {"text": "What genre is Eddie & the Gang with No Name?"}, {"text": "What genre is Koko ni Iruzee!?"}, {"text": "What genre is the 2014 extended play by <PERSON>?"}, {"text": "What genre is the album Stories by <PERSON><PERSON>?"}, {"text": "What genre is Most of Me?"}, {"text": "What genre is the 1952 film I Lost My Heart in Heidelberg by <PERSON>?"}, {"text": "What genre is the single VS by <PERSON><PERSON><PERSON>?"}, {"text": "What is the genre of Seven Veils, an album by <PERSON>?"}, {"text": "What is the genre of Bridge, the album by <PERSON>?"}, {"text": "What genre is Deivos?"}, {"text": "What genre is Martinez?"}, {"text": "What is the genre of Chariot Race, the 1983 video game?"}, {"text": "What genre is the album Progression by <PERSON>?"}, {"text": "What is the genre of The Take, the UK musical group?"}, {"text": "What is the genre of Conversations, the album by <PERSON>?"}, {"text": "What is the genre of Mars, the 1968 film by <PERSON>?"}, {"text": "What genre is Dimensions, the 1955 studio album by <PERSON>?"}, {"text": "What genre is associated with <PERSON><PERSON>, the British musician (1957–2021)?"}, {"text": "What genre is The Angel, the 2007 short film directed by <PERSON>?"}, {"text": "What genre is Tempting Danger?"}, {"text": "What genre is the 1987 single I Will Be There by <PERSON>?"}, {"text": "What genre is Detour for <PERSON>?"}, {"text": "What genre is the EP by <PERSON> titled *Drama*?"}, {"text": "What genre is <PERSON>?"}, {"text": "What genre is the 2001 album Gone by Entwine?"}, {"text": "What type of genre does the artwork Compass by <PERSON> belong to?"}, {"text": "What is the genre of Apollo, the American music group?"}, {"text": "What genre is <PERSON>?"}, {"text": "What genre is the song The Box by King Missile?"}, {"text": "What genre is the book In Deep by <PERSON>?"}, {"text": "What genre is the single Fantasy by <PERSON> Nine?"}, {"text": "What genre is Just a Matter of Time, a work by <PERSON>?"}, {"text": "What genre is the 2010 Peruvian film Reminiscences?"}, {"text": "What genre is My Way, the ninth Cantonese studio album by Hong Kong solo artist <PERSON> associated with?"}, {"text": "What is the genre of the single \"Our Time\" by Dream?"}, {"text": "What genre is El honorable Se<PERSON><PERSON>?"}, {"text": "What type of creative work is <PERSON><PERSON> associated with?"}, {"text": "What genre is Collaboration West?"}, {"text": "What is the genre of Thin Ice, the 2013 film?"}, {"text": "What genre is The Promoter?"}, {"text": "What genre is the song \"Shine\" by Luna Sea?"}, {"text": "What is the genre of Zones, the book by <PERSON>?"}, {"text": "What genre is the song \"The Gift\" by The McCarters?"}, {"text": "What genre is the novel Gene by <PERSON><PERSON>?"}, {"text": "What is the genre of the song \"Evil\" by <PERSON><PERSON><PERSON>?"}, {"text": "What genre is <PERSON>?"}, {"text": "What genre is Serving You?"}, {"text": "What genre is the 1988 song Neighbours by Camou<PERSON><PERSON>?"}, {"text": "What genre is the 1996 single In Silence by Luna Sea?"}, {"text": "What genre is A Winter of Cyclists?"}, {"text": "What is the genre of Back to Back, a single by <PERSON>?"}, {"text": "What genre of music does the band Strength from Japan perform?"}, {"text": "What genre is All the Years?"}, {"text": "What genre is the song Let It Go by Fe?"}, {"text": "What genre is Drôles de zèbres?"}, {"text": "What genre is The <PERSON> Story?"}, {"text": "What genre is the 1932 British film directed by <PERSON>, titled Betrayal?"}, {"text": "What genre is Tempting The Gods: The Selected Stories of <PERSON><PERSON>, Volume 1?"}, {"text": "What genre is Let It Be You?"}, {"text": "What genre is the 2004 single <PERSON><PERSON><PERSON> by TRAX?"}, {"text": "What is the genre of Right There, the 2013 short film directed by <PERSON>?"}, {"text": "What genre is El usurpador?"}, {"text": "What is the genre of Fire, the 1990 novel by <PERSON>?"}, {"text": "What is the genre of The Moment, the 2012 LP from <PERSON>?"}, {"text": "What genre is the novel *Strangers* by <PERSON>?"}, {"text": "What genre is Info?"}, {"text": "What is the genre of Theatre, the South African band?"}, {"text": "What genre is the 1973 film Background by <PERSON>?"}, {"text": "What is the genre of Node, the Italian musical group?"}, {"text": "What genre is the book *In Deep* by <PERSON>?"}, {"text": "What genre is the song *If I Ever* by Red Flag?"}, {"text": "What is the genre of More Love, the 1988 single by <PERSON><PERSON>?"}, {"text": "What genre is The Remarkable Exploits of <PERSON><PERSON> Biggs, Spaceman?"}, {"text": "What genre is My Husband?"}, {"text": "What is the genre of the song West?"}, {"text": "What genre is It Sounds Like?"}, {"text": "What genre is The Other Man, a 1916 production?"}, {"text": "What genre is the 2012 single Wake Up by ClariS?"}, {"text": "What genre is The Copper, the 1958 film directed by <PERSON><PERSON>?"}, {"text": "What genre is A Question and Answer Guide to Astronomy?"}, {"text": "What genre is Buono! 2?"}, {"text": "What genre is The Blue Aura?"}, {"text": "What genre is the 1999 album by Cosmic Baby?"}, {"text": "What genre is the 2000 video game Heist?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON>, the American-bred Thoroughbred?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON> of Capua?"}, {"text": "Who is the father of <PERSON>, the Chinese?"}, {"text": "Who is the father of <PERSON><PERSON><PERSON> of Spoleto?"}, {"text": "Who is the father of <PERSON><PERSON>?"}, {"text": "Who is the father of <PERSON><PERSON>?"}, {"text": "Who is the father of <PERSON><PERSON>?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON><PERSON>?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "\"Who is the father of <PERSON>, the son of <PERSON> of Georgia?\""}, {"text": "Who is the father of <PERSON><PERSON>?"}, {"text": "Who is the father of But Why Not?"}, {"text": "Who is the father of Match II?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON>, the co-king of Georgia who lived during the 15th century?"}, {"text": "Who is the father of Now What?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of Sir <PERSON><PERSON>, 3rd Baronet?"}, {"text": "Who is the father of <PERSON><PERSON>?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON>, the abbot of Saint-Denis in the 9th century?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON><PERSON>?"}, {"text": "In what country is Union State Bank, Wisconsin?"}, {"text": "In what country is Tina, a ghost town in Summers County?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Sar Giz?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Dell, a township in Benton County?"}, {"text": "In what country is Bandrakottai?"}, {"text": "In what country is Fairview Outdoor School?"}, {"text": "In what country is Kılıçlı Kavlaklı?"}, {"text": "In what country is Ago, a dissolved municipality in Shima district?"}, {"text": "In what country is Égligny?"}, {"text": "In what country is Bitchū-Kawamo Station?"}, {"text": "In what country is Borysławice?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Tartaczek?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Ahmadabad-e Razavi?"}, {"text": "In what country is Freedom, an unincorporated community in Utah?"}, {"text": "In what country is Ciepień?"}, {"text": "\"In what country is Blenheim, a human settlement in Gloucester Township?\""}, {"text": "In what country is Mary, a commune located in Saône-et-Loire?"}, {"text": "In what country is Gmina Lubsza?"}, {"text": "In what country is Tsutsui Station, a railway station located in Aomori Prefecture?"}, {"text": "In what country is Edmundston, a provincial electoral district located?"}, {"text": "In what country is the village of Rahzan found?"}, {"text": "In what country is Kozići?"}, {"text": "In what country is Valdearcos de la Vega found?"}, {"text": "In what country is Ciucurul Orbului River?"}, {"text": "In what country is Gaustadalléen found?"}, {"text": "In what country is Poręba-Kocęby?"}, {"text": "In what country is Dubicze Osoczne?"}, {"text": "In what country is Joys?"}, {"text": "In what country is Laxmipur, Mahakali?"}, {"text": "\"In what country is Wiesau located?\""}, {"text": "In what country is Lewałd Wielki?"}, {"text": "In what country is Kamioka Station, a railway station located in Saiki?"}, {"text": "In what country is Quebec Route 213?"}, {"text": "In what country is Al-Fajr Arabsalim?"}, {"text": "In what country is Dąbkowice, Łódź Voivodeship?"}, {"text": "In what country is Borzymy, Kolno County?"}, {"text": "\"In what country is Fontenay, a former commune in Manche located?\""}, {"text": "In what country is the Valea Seacă River located?"}, {"text": "In what country is Punghina, a commune located in Mehedinți County?"}, {"text": "In what country is Ormak, Isfahan?"}, {"text": "In what country is Vera, a human settlement in Appomattox County?"}, {"text": "In what country is Kuczynka?"}, {"text": "In what country is West Wyomissing?"}, {"text": "In what country is Tigra, a village in Haryana, located?"}, {"text": "In what country is Jauldes?"}, {"text": "In what country is Nowa Wieś Reszelska?"}, {"text": "In what country is Colonia Nueva Coneta?"}, {"text": "In what country is Aminabad, a village in Kermanshah Province, located?"}, {"text": "In what country is Tholuvankadu?"}, {"text": "In what country is Anaikudam?"}, {"text": "In what country is Society of Early Americanists?"}, {"text": "In what country is Denmark Hill Insect Bed?"}, {"text": "In what country is Mavjinjava?"}, {"text": "In what country is Arthur, a ghost town in Elko County?"}, {"text": "In what country is Pârâul Bogat?"}, {"text": "In what country is Têbo?"}, {"text": "In what country is Sholoktu?"}, {"text": "In what country is Goldasht, Sistan and Baluchestan?"}, {"text": "In what country is Eslamabad-e Mashayekh?"}, {"text": "\"In what country is Kalu, a village in East Azerbaijan located?\""}, {"text": "In what country is Pierce, a civil town in Kewaunee County?"}, {"text": "In what country is Chalhuacocha located?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Oscar, an unincorporated community in Greenbrier County?"}, {"text": "In what country is Cora, an unincorporated community in Logan County?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Oborín?"}, {"text": "In what country is Ježov, a village in Hodonín District of the South Moravian region located?"}, {"text": "In what country is the Drăgăneasa River found?"}, {"text": "In what country is Batsère?"}, {"text": "In what country is WZRU?"}, {"text": "In what country is Idlorpait?"}, {"text": "In what country is Barice, <PERSON><PERSON>?"}, {"text": "In what country is Habit?"}, {"text": "In what country is the municipality of Sabiote located?"}, {"text": "In what country is Kalateh-ye Safdarabad?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Tad, a village in Falavarjan County, located?"}, {"text": "In what country is Fântâneaua Rece River?"}, {"text": "In what country is Panaitoliko?"}, {"text": "In what country is Villalcampo, a municipality of Zamora Province, located?"}, {"text": "In what country is <PERSON><PERSON><PERSON><PERSON>, a stadtteil of Saarbrücken?"}, {"text": "In what country is Toronto Northwest, a federal electoral district located?"}, {"text": "In what country is <PERSON><PERSON><PERSON>?"}, {"text": "In what country is Hobbledehoy Record Co.?"}, {"text": "In what country is SWEAT?"}, {"text": "In what country is Dəhnəxəlil?"}, {"text": "In what country is Khvajeh So<PERSON>l?"}, {"text": "In what country is Zec Petawaga?"}, {"text": "In what country is Tapay District?"}, {"text": "In what country is Cổ Linh?"}, {"text": "In what country is Mahaboboka?"}, {"text": "In what country is Cześniki-Kolonia Górna?"}, {"text": "In what country is Awe, a township in Lewis County?"}, {"text": "In what country is Mrákotín, a village in Chrudim District of the Pardubice region located?"}, {"text": "In what country is Pichlice?"}, {"text": "In what country is pero?"}, {"text": "In what country is Khafr County?"}, {"text": "In what country is İnstitut?"}, {"text": "In what country is Karimu, a village in South Khorasan located?"}, {"text": "In what country is Graitschen bei Bürgel?"}, {"text": "In what country is Durrenentzen?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is United States Post Office and Courthouse–Billings?"}, {"text": "In what country is Eshkevar-e Sofla Rural District?"}, {"text": "In what country is Rizuiyeh?"}, {"text": "In what country is Veliko Korenovo?"}, {"text": "In what country is Gimenells i el Pla de la Font?"}, {"text": "In what country is La Roche-Clermault?"}, {"text": "In what country is Biały Kościół, Lower Silesian Voivodeship?"}, {"text": "In what country is Content, a house near Centreville in Queen Anne's County?"}, {"text": "In what country is Zhukiv, a village located in Berezhany Raion?"}, {"text": "In what country is Saint-Vincent-de-Salers?"}, {"text": "In what country is Weed, an unincorporated community in Poinsett County?"}, {"text": "In what country is Alder, a human settlement in Saguache County?"}, {"text": "In what country is Brizambourg?"}, {"text": "In what country is Călmuș River?"}, {"text": "In what country is Eschbronn?"}, {"text": "In what country is Kondh, Surendranagar?"}, {"text": "\"In what country can Rogers be found?\""}, {"text": "In what country is Cos, a commune in Ariège located?"}, {"text": "In what country is <PERSON><PERSON> Ursula Gakuen Junior College?"}, {"text": "In what country is Selkirk Generating Station?"}, {"text": "In what country is Devalan?"}, {"text": "In what country is Dârmocsa River?"}, {"text": "In what country is Gori <PERSON>?"}, {"text": "In what country is <PERSON><PERSON><PERSON>?"}, {"text": "\"In what country is Francis, a former district?\""}, {"text": "In what country is Łazy, Sierpc County?"}, {"text": "In what country is Khishig-Öndör?"}, {"text": "In what country is Gaffarlı?"}, {"text": "In what country is Crow Harbour, New Brunswick?"}, {"text": "In what country is Łodygowo, Pisz County?"}, {"text": "In what country is <PERSON>'s cabinet?"}, {"text": "In what country is canton of Baugy?"}, {"text": "In what country is Anjoma?"}, {"text": "In what country is Ittamalliyagoda?"}, {"text": "In what country is Abra, Ivory Coast?"}, {"text": "In what country is Okunakayama-Kōgen Station?"}, {"text": "In what country is DeWitt Township?"}, {"text": "In what country is Centre located?"}, {"text": "In what country is Asahi Station, a railway station located in Kochi prefecture?"}, {"text": "In what country is Stare Brzóski?"}, {"text": "In what country is Bud, an unincorporated community in the Town of Jefferson, Vernon County?"}, {"text": "In what country is Tangal-e Behdan?"}, {"text": "In what country is Seed 97.5 FM?"}, {"text": "In what country is Perth, a federal electoral district located?"}, {"text": "\"In what country is the settlement Ara located?\""}, {"text": "In what country is Wir, Masovian Voivodeship?"}, {"text": "In what country is Avarzaman?"}, {"text": "In what country is Anaran Rural District?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Otto-Selz-Institute of Applied Psychology?"}, {"text": "In what country is Jodłówka gas field?"}, {"text": "In what country is Tupper-Barnett House?"}, {"text": "In what country is Izvorul Morarului River?"}, {"text": "In what country is Mehran Kushk?"}, {"text": "In what country is Astrodomi Observatory?"}, {"text": "In what country is Muratdere?"}, {"text": "In what country is Miętkie-Kolonia?"}, {"text": "In what country is Szczecin Scientific Society?"}, {"text": "In what country is Märstetten?"}, {"text": "In what country is Riethnordhausen?"}, {"text": "In what country is Tervola Radio and TV-Mast?"}, {"text": "In what country is Abdul <PERSON> Stadium?"}, {"text": "In what country is Alu, a village in Pärnu County?"}, {"text": "In what country is Chotýčany, a village in the South Bohemian region?"}, {"text": "In what country is Asseek River?"}, {"text": "In what country is Gąsiorowo, Legionowo County?"}, {"text": "In what country is Jeqjeq-e Pain?"}, {"text": "In what country is Dragomirna River located?"}, {"text": "In what country is Mohammadabad-e Razzaqzadeh?"}, {"text": "In what country is Grant, a place in the Littoral region?"}, {"text": "In what country is Rubim do Norte River?"}, {"text": "In what country is Institute of Chemistry of Ireland?"}, {"text": "In what country is Lima, a community located in Wisconsin?"}, {"text": "In what country is KMEI-LP?"}, {"text": "In what country is Záblatí, a village in the Prachatice District of the South Bohemian region located?"}, {"text": "In what country is Ba Thín River?"}, {"text": "In what country is El Carmen Rivero Tórrez?"}, {"text": "In what country is Kawahigashi Station, located in Sukagawa, Fukushima prefecture?"}, {"text": "In what country is Los Santos mine?"}, {"text": "In what country is Whited Township?"}, {"text": "In what country is Asalem Rural District?"}, {"text": "In what country is Contest, a commune located?"}, {"text": "In what country is Genoa, an unincorporated community in Olmsted County?"}, {"text": "In which country is Normania Township, located in Yellow Medicine County, Minnesota?"}, {"text": "In what country is Chicche District?"}, {"text": "In what country is canton of Marseille-La Pomme?"}, {"text": "In which country is Devanur, a village in Tamil Nadu, located?"}, {"text": "In what country is Tegher, a village in Aragatsotn Province, located?"}, {"text": "In what country is Kodki?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is New England, a former state electoral district?"}, {"text": "In what country is Kowale, Lower Silesian Voivodeship?"}, {"text": "In what country is Obeakpu?"}, {"text": "In what country is La Couarde-sur-Mer, a commune in Charente-Maritime located?"}, {"text": "\"In what country is Riechheimer Berg, a municipal association in Thuringia located?\""}, {"text": "In what country is Alexeni River?"}, {"text": "In what country is Villers-sous-Foucarmont?"}, {"text": "\"In what country is North Lake, a lake located in Nova Scotia, situated?\""}, {"text": "In what country is 112th United States Colored Infantry?"}, {"text": "In what country is Storsteinnes Chapel?"}, {"text": "In what country is Ch'uch'u Apachita?"}, {"text": "In what country is the river Bārta found?"}, {"text": "\"In what country is Urge, a village in Kohila Rural Municipality?\""}, {"text": "In what country is Domašov, a village in the South Moravian region?"}, {"text": "In what country is Vaiea?"}, {"text": "In what country is Monitor House?"}, {"text": "In what country is Sagoni?"}, {"text": "In what country is Eeuwfeestkliniek?"}, {"text": "In what country is Łupiny, Masovian Voivodeship?"}, {"text": "In what country is Xaga?"}, {"text": "In what country is Babino, Haiti?"}, {"text": "In what country is Hatnagoda?"}, {"text": "In what country is Deodara?"}, {"text": "In what country is Puzdrowizna?"}, {"text": "In what country is Harisan?"}, {"text": "In what country is Ločenice, a village in the South Bohemian region?"}, {"text": "In what country is Aki, a dissolved municipality in Higashikunisaki district?"}, {"text": "In what country is Taia River?"}, {"text": "In what country is Sjösa?"}, {"text": "In what country is Morales de Campos found?"}, {"text": "\"In what country is the Dobra River located?\""}, {"text": "In what country is Karahasanlı, a neighborhood in Karaisalı?"}, {"text": "In what country is Ackerman-Dewsnap House?"}, {"text": "In what country is Wilcza Jama, Sokółka County?"}, {"text": "In what country is Givron?"}, {"text": "In what country is Humane Heritage Museum?"}, {"text": "\"In what country is Arlington, an unincorporated community in Harrison County?\""}, {"text": "In what country is Adams, an unincorporated community located?"}, {"text": "In what country is the municipality located?"}, {"text": "In what country is Tōhoku History Museum?"}, {"text": "In what country is Neal, an unincorporated community?"}, {"text": "In what country is Korean Magazine Museum?"}, {"text": "In what country is Francheville Aerodrome?"}, {"text": "In what country is Kijevac, a village located in the Pčinja District?"}, {"text": "In what country is Iron River (CDP), Wisconsin?"}, {"text": "In what country is Lätäseno?"}, {"text": "In what country is Mount Shinten?"}, {"text": "In what country is Dual Plover?"}, {"text": "In what country is Saint-Antonin, a commune in Gers located?"}, {"text": "In what country is Peterson, an unincorporated community in Adams County, located?"}, {"text": "In what country is Joy, an unincorporated community in White County?"}, {"text": "In what country is Valea Pleșii River located?"}, {"text": "In what country is Sutlepa?"}, {"text": "In what country is Movraž?"}, {"text": "In what country is Sarnowo, Chełmno County?"}, {"text": "In what country is Saint-Pierrevillers?"}, {"text": "In what country is Archipelago Museum?"}, {"text": "In what country is Revigliasco d'Asti located?"}, {"text": "In what country is Willow River located?"}, {"text": "In what country is Uñón District?"}, {"text": "In what country is Ban On, a subdistrict in Ngao district?"}, {"text": "In what country is Kanaküla, a village in Saarde Rural Municipality?"}, {"text": "Where is Breitenfelde located?"}, {"text": "In what country is Konjsko Brdo, a settlement in the Municipality of Perušić?"}, {"text": "In what country is New York State Route 157?"}, {"text": "In what country is Le Moustoir, a commune in Côtes-d'Armor located?"}, {"text": "In what country is Mackay Courthouse?"}, {"text": "In what country is <PERSON><PERSON><PERSON>?"}, {"text": "In what country is Robinson, an unincorporated community in St. Louis County?"}, {"text": "In what country is Lambertz Open by STAWAG?"}, {"text": "In what country is Goreme?"}, {"text": "In what country is Gawarzec Dolny?"}, {"text": "In what country is Studzianka, Podlaskie Voivodeship?"}, {"text": "In what country is Gare de Rosporden?"}, {"text": "In which country is Earl, an unincorporated community in Washburn County?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Rozsochatec, a village in the Havlíčkův Brod District of the Vysočina region located?"}, {"text": "Who was the producer of <PERSON>, Jr.?"}, {"text": "Who was the producer of O skliros andras?"}, {"text": "Who was responsible for producing the 1963 film, The Hunt?"}, {"text": "Who was the producer of the 1960 film, *The Accused*?"}, {"text": "Who was the producer of Just Like Us?"}, {"text": "Who was the producer of Today, an EP by Everlast?"}, {"text": "Who was the producer of The Pioneers?"}, {"text": "Who was the producer of the 1983 film?"}, {"text": "Who was the producer of the 2006 live album?"}, {"text": "Who was the producer of The Baby on the Barge?"}, {"text": "Who was the producer of the 1913 film?"}, {"text": "Who was the producer of The Hayseeds' Back-blocks Show?"}, {"text": "Who was the producer of the 1997 film?"}, {"text": "Who was the producer of the 2007 film From Now On by <PERSON><PERSON>?"}, {"text": "Who was the producer of <PERSON>'s Wife?"}, {"text": "Who was the producer of Italian Style?"}, {"text": "Who was the producer of Strand?"}, {"text": "Who was the producer of The Thing We Love?"}, {"text": "Who was the producer of One of Those?"}, {"text": "Who was the producer of the 1912 American film, The Lie?"}, {"text": "Who was responsible for producing the album Early Man?"}, {"text": "Who was the producer of The Garden of Weeds?"}, {"text": "Who was the producer of Maling Kutang?"}, {"text": "Who was the producer of the 1994 film directed by <PERSON>?"}, {"text": "Who was the producer of Saturday Morning?"}, {"text": "Who was the producer of the 1924 German silent drama film?"}, {"text": "Who was the producer of the episode *Revelations* from *Hell on Wheels* (Season 1, Episode 7)?"}, {"text": "Who was responsible for producing the album Home?"}, {"text": "Who was involved in the production of the 1935 film, The Test?"}, {"text": "Who was the producer of Me First?"}, {"text": "Who was the producer of the song Shine by <PERSON>?"}, {"text": "Who was the producer of Trains of Winnipeg?"}, {"text": "Who was involved in producing the 1971 film, *In the Family*?"}, {"text": "Who was involved in producing the 1917 film, *The Easiest Way*?"}, {"text": "Who was the producer of Hired!?"}, {"text": "Who was responsible for producing the song, <PERSON>?"}, {"text": "Who was the producer of De Laatste Dagen van een Eiland?"}, {"text": "Who was the director of City of Beautiful Nonsense?"}, {"text": "Who was the director of the 1914 film?"}, {"text": "Who directed the 1929 film, *Those Who Love*?"}, {"text": "Who was the director of Chi?"}, {"text": "Who was the director of the 1936 film, The Happy Family?"}, {"text": "Who was the director of The Only Woman?"}, {"text": "Who was the director of the 1916 short film?"}, {"text": "Who was the director of the 2010 film?"}, {"text": "Who was the director of the 1938 film?"}, {"text": "Who was the director of Me First?"}, {"text": "Who was the director of the pilot episode of *<PERSON>, the Teenage Witch*?"}, {"text": "Who was the director of La renzoni?"}, {"text": "Who was the director of the 1999 film?"}, {"text": "Who was the director of the episode \"Homecoming\" from Miss Guided?"}, {"text": "Who was the director of Thank You, <PERSON>?"}, {"text": "Who was the director of All the Way Up?"}, {"text": "Who was the director of Zonnetje?"}, {"text": "Who was the director of the 1984 film?"}, {"text": "Who was the director of Practical Jokers?"}, {"text": "Who was the director of the 1993 film?"}, {"text": "Who was the director of the 1916 film?"}, {"text": "Who was the director of Son contento?"}, {"text": "Who was the director of Taxi at Midnight?"}, {"text": "Who was the director of <PERSON>, an episode of The Following?"}, {"text": "Who directed the 1983 Bulgarian drama film, <PERSON><PERSON>?"}, {"text": "Who was the director of the 1916 film?"}, {"text": "Who was the director of the 1958 film?"}, {"text": "Who was the director of the 1935 British film?"}, {"text": "Who was the director of the 1920 film, The Night Riders?"}, {"text": "Who was the director of the 1938 film?"}, {"text": "Who was the director of La cruz?"}, {"text": "Who directed the 1933 film, The Love Nest?"}, {"text": "Who was the director of The Resolve?"}, {"text": "Who was the director of the 1957 short film?"}, {"text": "Who was the director of While There is Still Time?"}, {"text": "Who was the director of Den store gavtyv?"}, {"text": "Who was the director of the 1928 film?"}, {"text": "Who was the director of El Último perro?"}, {"text": "Who was the director of the 1917 film, *The Easiest Way*?"}, {"text": "Who was the director of the 1993 film?"}, {"text": "Who was responsible for directing the 1917 film, Sacrifice?"}, {"text": "Who directed the 1938 film, Women Who Work?"}, {"text": "Who was the director of Trail?"}, {"text": "Who was the director of Det var paa Rundetaarn?"}, {"text": "Who was the director of the 1937 film?"}, {"text": "Who was the director of the 2003 film?"}, {"text": "Who directed the 1925 film, Men and Women?"}, {"text": "Who was the director of the 1915 American silent drama film?"}, {"text": "Who was the director of the 1941 Bollywood film?"}, {"text": "Who was the director of the 1916 film?"}, {"text": "Who was the director of Broadway Jones?"}, {"text": "Who was the director of the 1973 film?"}, {"text": "Who was the director of Escape, the television series from the United States?"}, {"text": "Who was the director of These Children?"}, {"text": "Who was the director of the 1952 Norwegian film?"}, {"text": "Who was the director of the episode \"Pilot\" from Dirty Sexy Money (Season 1, Episode 1)?"}, {"text": "Who was the director of La Rival?"}, {"text": "Who was the director of the 1997 film?"}, {"text": "Who was the director of the 1919 film?"}, {"text": "Who directed the 2006 film, <PERSON>ck<PERSON>?"}, {"text": "Who was the director of the 1934 German drama film?"}, {"text": "Who was the director of The Pigskin Palooka?"}, {"text": "Who was responsible for directing the 1916 film, *Public Opinion*?"}, {"text": "Who was the director of the 1984 film?"}, {"text": "Who was the director of the 1961 film?"}, {"text": "Who was the director of the 1914 film?"}, {"text": "Who was the director of Le Guérisseur?"}, {"text": "Who was the director of The Photo?"}, {"text": "Who was the director of the 1974 film?"}, {"text": "Who was the director of Big Dreams Little Tokyo?"}, {"text": "Who was the director of A Rowboat Romance?"}, {"text": "Who was the director of the 1961 film?"}, {"text": "Who was the director of the 1916 silent film?"}, {"text": "Who was the director of Indizienbeweis?"}, {"text": "Who directed the 1976 film, Accident?"}, {"text": "Who was the director of the 1941 film?"}, {"text": "Who was the director of The Girl in Mourning?"}, {"text": "Who was the director of the 1984 film?"}, {"text": "Who was the director of the 1979 Croatian film?"}, {"text": "Who was the director of the 1935 film?"}, {"text": "Who was the director of the 1997 film?"}, {"text": "Who was the director of One of Those?"}, {"text": "Who was the director of the 1965 anthology film?"}, {"text": "Who was the director of the 1916 film?"}, {"text": "Who was the director of <PERSON><PERSON>?"}, {"text": "Who directed the 2014 film, The Valley?"}, {"text": "Who was the director of <PERSON>?"}, {"text": "Who was the director of The Loudwater Mystery?"}, {"text": "Who was the director of the episode \"Pilot\" from Life on a Stick (Season 1, Episode 1)?"}, {"text": "Who was the director of Hakeem's New Flame?"}, {"text": "Who directed the 2010 film, Just Like Us?"}, {"text": "Who was the director of A Helpful Sisterhood?"}, {"text": "\"Who was the director of the 1928 film?\""}, {"text": "Who was the director of the 1938 film?"}, {"text": "Who was the director of Not So Long Ago?"}, {"text": "What is Kluczewsko the capital of?"}, {"text": "What is <PERSON><PERSON><PERSON> the capital of?"}, {"text": "What is Bolsheustyikinskoye the capital of?"}, {"text": "What is <PERSON><PERSON><PERSON><PERSON> the capital of?"}, {"text": "What is I<PERSON> the capital of?"}, {"text": "Who was the screenwriter for Death of a Batman?"}, {"text": "Who was the screenwriter for Fear No More?"}, {"text": "Who was the screenwriter for the 1927 film?"}, {"text": "Who was the screenwriter for <PERSON><PERSON><PERSON> pintadas?"}, {"text": "Who was the screenwriter for the pilot episode of *<PERSON>, the Teenage Witch* directed by <PERSON><PERSON>?"}, {"text": "Who was the writer for the 1995 film, Goodbye?"}, {"text": "Who was responsible for writing the screenplay for the 1994 film, Party?"}, {"text": "Who was the screenwriter for the episode \"<PERSON>\" from Falling Skies (Season 1, Episode 4)?"}, {"text": "Who was the screenwriter for the 1997 film?"}, {"text": "Who was the screenwriter for By og land hand i hand?"}, {"text": "Who was responsible for writing the screenplay for the 1960 film, *The Accused*?"}, {"text": "Who was the screenwriter for Exit the Vamp?"}, {"text": "Who was the screenwriter for <PERSON><PERSON><PERSON> <PERSON><PERSON>?"}, {"text": "Who was the screenwriter for the 1918 film?"}, {"text": "Who was the screenwriter for Revelations, an episode of Hell on Wheels (Season 1)?"}, {"text": "Who was the screenwriter for Ending It?"}, {"text": "Who was the screenwriter for <PERSON><PERSON>?"}, {"text": "Who was the screenwriter for the episode \"Guilty\" from the series *Awake* (Season 1, Episode 3)?"}, {"text": "Who was the screenwriter for the 2008 Australian film?"}, {"text": "Who was responsible for writing the screenplay for the 1973 film, *The Last Word*?"}, {"text": "Who was the screenwriter for <PERSON><PERSON><PERSON><PERSON>?"}, {"text": "Who was the screenwriter for White Gold, the 2003 film directed by <PERSON>?"}, {"text": "Who was the screenwriter for The Bride’s Journey?"}, {"text": "Who was the screenwriter for the first episode of <PERSON> & Bash (S1 E1)?"}, {"text": "Who was the screenwriter for These Children?"}, {"text": "Who was the screenwriter for Prototype, the pilot episode of the ABC sitcom?"}, {"text": "Who was the screenwriter for <PERSON>'s <PERSON> Boarder?"}, {"text": "Who was the screenwriter for Le Fils d'Amr est mort?"}, {"text": "Who was the screenwriter for <PERSON><PERSON>?"}, {"text": "Who was the screenwriter for The Worst Years of Our Lives?"}, {"text": "Who contributed as the screenwriter for the 1916 film, *The City*?"}, {"text": "Who was the screenwriter for <PERSON>: My Life... Your Fault?"}, {"text": "Who was the screenwriter for Three Loves in Rio?"}, {"text": "Who was the screenwriter for the episode \"Guilty\" from the show *Awake* (Season 1, Episode 3)?"}, {"text": "Who was responsible for writing the screenplay for the 1979 Croatian film, *The Return*?"}, {"text": "Who was the screenwriter for Oregon?"}, {"text": "Who was the screenwriter for the 1974 film?"}, {"text": "Who was the screenwriter for Impossible?"}, {"text": "Who was responsible for writing the screenplay for the 1960 film, The Accused?"}, {"text": "Who was the screenwriter for the 1918 American silent drama film?"}, {"text": "Who was the screenwriter for <PERSON><PERSON><PERSON><PERSON>, mas<PERSON><PERSON>?"}, {"text": "Who was the screenwriter for <PERSON>?"}, {"text": "Who was the composer of the chamber opera titled One?"}, {"text": "Who was the creator of the 1995 single, Hello?"}, {"text": "Who was the composer for the 1997 film, Ghost?"}, {"text": "Who created the composition titled <PERSON>?"}, {"text": "Who was the composer of the 1937 film *To Live*, directed by <PERSON>?"}, {"text": "Who was the composer of <PERSON><PERSON>?"}, {"text": "Who was the composer of To The West?"}, {"text": "Who was the composer of the ballet titled *The Witch*?"}, {"text": "Who was the composer of the ballet Images by <PERSON>?"}, {"text": "Who was the composer of <PERSON><PERSON><PERSON>?"}, {"text": "Who was the composer of the song originally performed by <PERSON><PERSON> at Melodifestivalen 2011?"}, {"text": "Who was the composer of Prelude in F major, Op. 49, No. 2?"}, {"text": "Who created the Piano Concerto?"}, {"text": "Who was the composer of <PERSON><PERSON><PERSON><PERSON> euch <PERSON>, bedr<PERSON><PERSON><PERSON>, BWV 224?"}, {"text": "Who was the creator of the EP, Homecoming?"}, {"text": "Who was the composer of The Greater Good, or the Passion of <PERSON><PERSON> de Suif?"}, {"text": "Who was the composer of G<PERSON>la!?"}, {"text": "Who was the composer of The Giants?"}, {"text": "Who was the composer of the song To the Sky by <PERSON><PERSON><PERSON>?"}, {"text": "Who was the composer of Say When, the American musical with lyrics by <PERSON> and a book by <PERSON>?"}, {"text": "Who was the composer of the 2011 album titled Alone?"}, {"text": "Who created the 2010 song, Famous?"}, {"text": "Who was the composer of Signal?"}, {"text": "Who was the composer of Miss You?"}, {"text": "Who was the composer of Living with You?"}, {"text": "Who was the composer of <PERSON>?"}, {"text": "Who was the composer of Images, an album of piano pieces performed by <PERSON>?"}, {"text": "Who was the composer of The Hope?"}, {"text": "Who created the musical work titled Time Machine?"}, {"text": "Who was the composer of Porch?"}, {"text": "Who created the song <PERSON>?"}, {"text": "Who was the composer of Nozze istriane?"}, {"text": "Who was the composer of Overture in G major?"}, {"text": "Who was the composer of Tea for One?"}, {"text": "Who was the composer of <PERSON><PERSON>?"}, {"text": "Who created String Quartet No. 3?"}, {"text": "Who was the composer of That's Right?"}, {"text": "Who created Symphony No. 33?"}, {"text": "Who created Symphony No. 8?"}, {"text": "Who was the composer of Discipline?"}, {"text": "Who was the composer of Cue Ball Cat?"}, {"text": "Who was the composer of the 2010 song?"}, {"text": "Who was the composer of Big Foot?"}, {"text": "Who was the composer of Sometime?"}, {"text": "Who was the composer of Prelude for Clarinet?"}, {"text": "Who was the composer of The Moment's Energy?"}, {"text": "Who was the composer of <PERSON>?"}, {"text": "Who was the composer of Miss You?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of Pierre-Antoine <PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of Guadalupe Missionaries?"}, {"text": "What is the religion of <PERSON>, the British bishop?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of Pedro <PERSON>í<PERSON> de Jesús Vílchez Vílchez?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of Ecclesiastical Statistics?"}, {"text": "What is the religion of <PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON>?"}, {"text": "What sport does 2012 Georgetown Hoyas men's soccer team play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does 2006 Korea Open Badminton Championships play?"}, {"text": "What sport does Sol Ky-Ong play?"}, {"text": "What sport does 2006–07 Primera B Nacional play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does 1994 Swedish Open play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does 2004 Legg Mason Tennis Classic play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 1988–89 FA Cup Qualifying Rounds play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 1989–90 1. Slovenská národná hokejová liga season play?"}, {"text": "What sport does 1923 in Brazilian football play?"}, {"text": "What sport does Ye Zhibin play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Imbi Hoop play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does 2014 Powiat Poznański Open play?"}, {"text": "What sport does 1997 Conference USA Baseball Tournament play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport is associated with <PERSON><PERSON><PERSON>, a Slovak athlete?"}, {"text": "What sport does 2002 Euro Beach Soccer Cup play?"}, {"text": "What sport does Sebastián Morquio play?"}, {"text": "What sport does Cho Ke<PERSON>-yeon play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 2013 Torneo di Viareggio play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does Diego Díaz Garrido play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does Koumiba Djossouvi play?"}, {"text": "What sport does 2010–11 South West Peninsula League play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport is associated with <PERSON>, the Austrian athlete (1927–2014)?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 1990–91 British Basketball League season play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does Zanzibar national under-20 football team play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 2001–02 Division 1 season play?"}, {"text": "What sport does <PERSON>, an American professional athlete, participate in?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Israel Andrade play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport is associated with <PERSON>?"}, {"text": "What sport does Indonesia Education League play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does Daigo Watanabe play?"}, {"text": "What sport does WTA South Orange play?"}, {"text": "What sport does Shuto Suzuki play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Masahito Noto play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON>u play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON>, the Canadian athlete, participate in?"}, {"text": "What sport does Oscar <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does 1994–95 FIBA Women's European Champions Cup play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> (1926–2000) play?"}, {"text": "What sport does <PERSON>, an Australian, play?"}, {"text": "What sport does <PERSON>, the Scottish athlete born in 1934, participate in?"}, {"text": "What sport does Momo Wall Blamo play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 2011–12 Elon Phoenix men's basketball team play?"}, {"text": "What is the sport played by <PERSON>, the Mexican athlete?"}, {"text": "What sport does FIBT World Championships 1939 play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 1973 Virginia Slims of Fort Lauderdale play?"}, {"text": "What sport does <PERSON><PERSON><PERSON>har <PERSON> play?"}, {"text": "What sport does 1949 France rugby union tour of Argentina play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does All-Ireland Senior Club Camogie Championship 1970 play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON>, an English athlete born in 1900, participate in?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON> Andrade play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON>, the Argentinian born in 1984, play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does 2008–09 National Indoor Soccer League season play?"}, {"text": "What sport is played during the 1994–95 season of the women's Fußball-Bundesliga?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does Granada Lions play?"}, {"text": "What is the sport played by <PERSON>, a Spanish athlete?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Afyonkarahisarspor play?"}, {"text": "What sport does canoeing at the 2014 Asian Games – women's K-4 500 metres play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does 1995 Cook Islands Round Cup play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON>, the Scottish athlete born in 1984, participate in?"}, {"text": "What sport does Sofia Anker-Kofoed play?"}, {"text": "What sport does Kiribati men's national basketball team play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does Turkish Seniors Open play?"}, {"text": "What sport does Njurunda SK play?"}, {"text": "What sport does 2009 Ukrainian Cup Final play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Marc Santo-Roman play?"}, {"text": "What sport does Sandar IL play?"}, {"text": "What sport does Francesco Reda play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does E Sour El Ghozlane play?"}, {"text": "What sport does Lek Kcira play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 1998–99 Slovenian Basketball League play?"}, {"text": "What sport does <PERSON>, the Welsh athlete born in 1950, participate in?"}, {"text": "What sport does list of Azerbaijan football transfers winter 2012 play?"}, {"text": "What sport does Abdulhadi Khalaf play?"}, {"text": "What sport does VOKO-Irodion play?"}, {"text": "What sport does <PERSON>, the English athlete born in 1951, participate in?"}, {"text": "What sport does Guyana women's national field hockey team play?"}, {"text": "What sport is associated with <PERSON><PERSON><PERSON>, the Bulgarian?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does Lobos BUAP Premier play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Luka Glavaš play?"}, {"text": "What sport does Guo <PERSON> play?"}, {"text": "What sport does Mehmet Gürkan Öztürk play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport is associated with <PERSON>?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON>u play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does Colombian Cycling Federation play?"}, {"text": "What sport does 1920–21 Northern Football League play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Université Nationale du Bénin FC play?"}, {"text": "What sport does 2012 Uzbekistan First League play?"}, {"text": "What sport does Nevio de <PERSON>o play?"}, {"text": "What sport does W<PERSON>jciech Jarmuż play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON>, the Portuguese athlete, participate in?"}, {"text": "What sport does Cassiá play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON>ecido <PERSON> play?"}, {"text": "What sport is associated with <PERSON>, the English athlete (1893–1979)?"}, {"text": "What sport does Ernest Street play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 2011 Chatham Cup play?"}, {"text": "What sport does Maltese Women's Cup play?"}, {"text": "What sport does 2009 Atlantic Coast Conference Baseball Tournament play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does Mutanda Kwesele play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "Who is the author of Afternoon?"}, {"text": "Who is the author of Bed?"}, {"text": "Who is the author of the book, *Watchers at the Strait Gate*?"}, {"text": "Who wrote the novel <PERSON>?"}, {"text": "Who is the author of the short story titled *Only Human*?"}, {"text": "Who wrote the book *Out of the Dark*?"}, {"text": "Who is the author of The National Dream?"}, {"text": "Who is the author of Saints of Big Harbour?"}, {"text": "Who is the author of Endpeace?"}, {"text": "Who is the author of Turning On?"}, {"text": "Who is the author of Something More?"}, {"text": "Who wrote the book, *The Romantic*?"}, {"text": "Who is the author of Buried Thunder?"}, {"text": "Who is the author of Time Enough?"}, {"text": "Who wrote the play Operator?"}, {"text": "Who wrote the book Sail?"}, {"text": "Who is the author of the 1990 novel?"}, {"text": "Who is the author of the young adult fantasy novel *Carnival of Souls*?"}, {"text": "Who is the author of Mannfolk?"}, {"text": "Who wrote the novel Rage?"}, {"text": "Who wrote the book <PERSON>?"}, {"text": "Who is the author of It's Not an All Night Fair?"}, {"text": "Who is the author of the novel, <PERSON>?"}, {"text": "Who is the author of the novel, *<PERSON> the Valiant*?"}, {"text": "Who is the author of Darkvision?"}, {"text": "Who wrote the book Regeneration?"}, {"text": "Who is the author of The Latimers?"}, {"text": "Who wrote the book Saint?"}, {"text": "Who is the author of Nevis Mountain Dew?"}, {"text": "Who is the author of World of Wonder?"}, {"text": "Who is the author of Dancing on Coral?"}, {"text": "Who is the author of New Keywords?"}, {"text": "Who is the author of Getting Free?"}, {"text": "Who is the author of Shooting Sean?"}, {"text": "Who is the author of Looking Forward?"}, {"text": "Who is the author of The World Before?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of The End of the Soul?"}, {"text": "Who is the author of Western?"}, {"text": "Who is the author of The Warriors of Spider?"}, {"text": "Who wrote the short story Homecoming?"}, {"text": "Who is the author of The Amazon?"}, {"text": "Who is the author of O dia das calças roladas?"}, {"text": "Who is the author of Visionseeker: Shared Wisdom from the Place of Refuge?"}, {"text": "Who is the author of Out of This World, a novel in The Worlds of Shadow trilogy?"}, {"text": "Who is the author of Stand By Your Screen?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of The Interior?"}, {"text": "Who wrote the short story Memory?"}, {"text": "\"Who wrote the book, <PERSON>?\""}, {"text": "Who is the author of School for Coquettes?"}, {"text": "Who wrote the book Trust Me?"}, {"text": "Who wrote the book Recursion?"}, {"text": "Who is the author of The Bishop's Heir?"}, {"text": "Who wrote Talent?"}, {"text": "Who is the author of This Is It?"}, {"text": "Who is the author of A Survey?"}, {"text": "Who is the author of Skyscraper?"}, {"text": "Who is the author of the Star Trek: Section 31 novel?"}, {"text": "Who is the author of This?"}, {"text": "Who is the author of <PERSON>, My Friend?"}, {"text": "Who is the author of The Great World and the Small: More Tales of the Ominous and Magical?"}, {"text": "Who wrote the book Robots?"}, {"text": "Who is the author of The Outdoor Survival Handbook?"}, {"text": "Who is the author of Millennial Rites?"}, {"text": "Who is the author of <PERSON><PERSON>, a novel by the Swedish writer?"}, {"text": "Who wrote the book, *The Burning*?"}, {"text": "Who is the author of Second Generation?"}, {"text": "Who is the author of the novel titled *The Guard*?"}, {"text": "Who wrote the short story, *West*?"}, {"text": "Who is the author of Nuclear Alert?"}, {"text": "Who is the author of Malvaloca?"}, {"text": "Who is the author of <PERSON><PERSON>?"}, {"text": "Who is the author of the Star Trek: Section 31 novel?"}, {"text": "Who is the author of The Museum of Abandoned Secrets?"}, {"text": "Who wrote the book Responsibility?"}, {"text": "Who is the author of Villa Amalia?"}, {"text": "Who is the author of Zones?"}, {"text": "Who is the author of the 2004 novel?"}, {"text": "Who is the author of the book titled *Beyond*?"}, {"text": "Who wrote the book, *The Other Place?*"}, {"text": "Who is the author of A Positive?"}, {"text": "Who is the author of Down, the four-issue American comic book series?"}, {"text": "Who is the author of the children's book series titled <PERSON>?"}, {"text": "Who is the author of What You Make It?"}, {"text": "Who is the author of Great Short Novels of Adult Fantasy I?"}, {"text": "Who wrote the poem, The Voice?"}, {"text": "Who is the author of Follow The Music?"}, {"text": "Who is the author of the 1985 novel?"}, {"text": "Who is the author of Across Many Mountains?"}, {"text": "Who is the author of Small Changes?"}, {"text": "Who wrote the book <PERSON>?"}, {"text": "Who wrote the novel Skin?"}, {"text": "Who is the author of The Techniques of Democracy?"}, {"text": "Who is the author of Death in Five Boxes?"}, {"text": "Who is the author of The Wizard in Wonderland?"}, {"text": "Who wrote the book Transcension?"}, {"text": "Who is the author of With Women?"}, {"text": "Who is the author of Come On Over?"}, {"text": "Who is the author of For a Living?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of the novel titled <PERSON><PERSON>?"}, {"text": "Who is the author of With?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of the play titled *The Burning*?"}, {"text": "Who is the author of The Sword of Shibito?"}, {"text": "Who is the author of <PERSON><PERSON><PERSON><PERSON><PERSON>?"}, {"text": "Who wrote the 2003 novel, *The Aware*?"}, {"text": "Who is the author of Pen?"}, {"text": "Who is the author of Science-Fantasy Quintette?"}, {"text": "Who wrote the novel *Sin*?"}, {"text": "Who wrote the novel Weekend?"}, {"text": "Who is the author of the collection of short stories titled Empire?"}, {"text": "Who is the author of The Empire?"}, {"text": "Who is the author of One of the Family?"}, {"text": "Who is the author of The Culture of Collaboration?"}, {"text": "Who is the author of Old Money?"}, {"text": "Who wrote the play <PERSON>?"}, {"text": "Who is the author of Señor <PERSON>?"}, {"text": "Who is the author of Het uur tussen hond en wolf?"}, {"text": "Who is the author of the novel, <PERSON>?"}, {"text": "Who wrote the book, *The Valley*?"}, {"text": "Who is the author of Facing the Future?"}, {"text": "Who is the author of the children's fairy tale, The Squirrel Wife?"}, {"text": "Who wrote the book, *Moving Day*?"}, {"text": "Who is the author of Close to Home?"}, {"text": "Who is the author of The Chaos Code?"}, {"text": "\"Who wrote the novel August?\""}, {"text": "Who wrote the novel <PERSON><PERSON>?"}, {"text": "Who is the author of America's Secret War?"}, {"text": "Who wrote the play, <PERSON>?"}, {"text": "Who is the author of the 1904 novel?"}, {"text": "Who is the author of the collection of short stories titled *Darkness*?"}, {"text": "Who is the author of Chelsea on the Edge?"}, {"text": "Who wrote the 1890 play Men and Women?"}, {"text": "Who wrote the book One More Time?"}, {"text": "Who is the author of Unknown?"}, {"text": "Who is the author of the 1973 novel, <PERSON>, written by the Danish writer?"}, {"text": "Who is the author of Time to Come?"}, {"text": "Who is the author of Template?"}, {"text": "Who is the author of American Dream, Global Nightmare?"}, {"text": "Who wrote the 1998 play, <PERSON><PERSON>?"}, {"text": "Who is the author of Neglected Aspects of Sufi Study?"}, {"text": "Who is the author of the novel titled Smoke?"}, {"text": "Who is the author of the novel, *The Great Perhaps*?"}, {"text": "Who is the author of The Universe Around Us?"}, {"text": "Who wrote the book *Against the Odds*?"}, {"text": "Who is the author of Branches?"}, {"text": "Who is the author of the 1976 book titled *New York*?"}, {"text": "Who wrote the book Challenge?"}, {"text": "Who wrote the novella Dreams?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of Nice People?"}, {"text": "Who is the author of *Falling*, the novel by the Flemish individual?"}, {"text": "Who is the author of Love All?"}, {"text": "Who wrote the poem, The Hero?"}, {"text": "Who is the author of The Sun Chemist?"}, {"text": "Who is the author of <PERSON><PERSON>'s Gift?"}, {"text": "Who is the author of <PERSON><PERSON><PERSON>?"}, {"text": "Who is the author of Always?"}, {"text": "Who is the author of the book, *The Economics and Ethics of Private Property*?"}, {"text": "Who is the author of The Every Boy?"}, {"text": "Who wrote the book, *The Middle Years*?"}, {"text": "Who is the author of Chaotic?"}, {"text": "Who is the author of <PERSON><PERSON><PERSON>?"}, {"text": "Who is the author of Resistance, the comic book series published by Wildstorm?"}, {"text": "Who is the author of Into the Woods?"}, {"text": "Who is the author of the 1978 radio play?"}, {"text": "Who is the author of Just a Matter of Time?"}, {"text": "Who is the author of Fruits?"}, {"text": "Who wrote the book Shift?"}, {"text": "Who is the author of the collection of short stories titled *Federation*?"}, {"text": "Who is the author of the novel Therapy, published in 2004?"}, {"text": "Who is the author of <PERSON><PERSON><PERSON> Plays Chicken?"}, {"text": "Who wrote the 2006 play, <PERSON>?"}, {"text": "Who is the author of Jää<PERSON><PERSON>?"}, {"text": "Who is the author of the 2000 novel titled *Prime Time*?"}, {"text": "Who is the author of Trust Territory?"}, {"text": "Who wrote the book Balance of Power?"}, {"text": "Who is the writer of the 1935 play, Panic?"}, {"text": "Who wrote the book, *The Lie*?"}, {"text": "Who is the author of <PERSON>'s Rules?"}, {"text": "Who is the author of V<PERSON>t katten har djuren själ!?"}, {"text": "Who is the author of Let's Not?"}, {"text": "Who is the author of the novel titled *Pursuit*?"}, {"text": "Who wrote the play Incoming?"}, {"text": "Who wrote the novel <PERSON>, Lord of the Black River?"}, {"text": "Who is the author of the young adult novel titled <PERSON>?"}, {"text": "Who wrote the book Corridor?"}, {"text": "Who is the author of Non?"}, {"text": "Who is the author of the 1997 spoken word album and audiobook titled *Everything*?"}, {"text": "Who is the author of the memoir titled *Find Me*?"}, {"text": "Who is the author of Partner?"}, {"text": "Who is the author of The Ball?"}, {"text": "Who is the author of the book *Suicide*?"}, {"text": "Who is the author of Witt?"}, {"text": "Who wrote the book On the Road?"}, {"text": "Who wrote the short story, *The Outing*?"}, {"text": "Who is the mother of <PERSON>, the abbot of Saint-Denis in the 9th century?"}, {"text": "What is the capital of Ungheni County?"}, {"text": "What is the capital of Gmina Secemin?"}, {"text": "What is the capital of Yunguyo Province?"}, {"text": "What is the capital of canton of Saint-Doulchard?"}, {"text": "What is the capital of arrondissement of Castellane?"}, {"text": "What is the capital of Sánchez Carrión Province?"}, {"text": "What is the capital of Chiprovtsi Municipality?"}, {"text": "What is the capital of canton of Antibes-Biot?"}, {"text": "What is the capital of canton of Harnes?"}, {"text": "What is the capital of Sal, a municipality in Cape Verde?"}, {"text": "What is the capital of Kareličy District?"}, {"text": "What is the capital of Kambarsky District?"}, {"text": "What is the capital of Gmina Brzeszcze?"}, {"text": "What is the capital of Tarussky District?"}, {"text": "What is the capital of Gmina Czorsztyn?"}, {"text": "What is the capital of Verbandsgemeinde Bad Ems?"}, {"text": "What is the capital of canton of Gordes?"}, {"text": "What is the capital of Gmina Andrespol?"}, {"text": "What is the capital of Vozhegodsky District?"}, {"text": "What is the capital of arrondissement of Nogent-le-Rotrou?"}, {"text": "What is the capital of arrondissement of Lannion?"}, {"text": "What is the capital of canton of Mirambeau?"}, {"text": "What is the capital of Saanen District?"}, {"text": "What is the capital of Plaza?"}, {"text": "What is the capital of arrondissement of Florac?"}, {"text": "What is the capital of Yalutorovsky District?"}, {"text": "What is the capital of Gmina Osiecznica?"}, {"text": "What is the capital of Gmina Radomsko?"}, {"text": "What is the capital of Gmina Babice?"}]