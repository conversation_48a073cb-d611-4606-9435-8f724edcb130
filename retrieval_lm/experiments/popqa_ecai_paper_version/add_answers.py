import csv
import json

# File paths
csv_path = '/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_with_description.csv'
json_path = '/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_disambiguated_retrieve20_contrievermsm.json'
output_json_path = '/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_disambiguated_retrieve20_contrievermsm_with_answers.json'

# Step 1: Read CSV answers in order
with open(csv_path, 'r', encoding='utf-8') as csv_file:
    csv_reader = csv.DictReader(csv_file)
    # Change this line to create the answers list
    answers = [row['possible_answers'] for row in csv_reader]  # List of answers

# Step 2: Load JSON and modify keys
with open(json_path, 'r', encoding='utf-8') as json_file:
    json_data = json.load(json_file)

# Step 3: Rename "query" → "question" and add "answers"
for i, entry in enumerate(json_data):
    # Rename key (if "query" exists)
    if "query" in entry:
        entry["question"] = entry.pop("query")  # Rename key
    # Add answer (if CSV has enough rows)
    # Change this line to use "answers" as the key
    entry["answers"] = [answers[i]] if i < len(answers) else []

# Step 4: Save the augmented JSON
with open(output_json_path, 'w', encoding='utf-8') as out_file:
    json.dump(json_data, out_file, indent=2, ensure_ascii=False)

print(f"✅ Augmented JSON saved to {output_json_path}")