import pandas as pd

# Input and output file paths
input_file = '/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_with_description_final_filtered_merged.csv'
output_file = '/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_ambiguous_subset.csv'

# Read the CSV file
df = pd.read_csv(input_file)

df = df[df['property_count']>1]

# Save to new CSV file
df.to_csv(output_file, index=False)

print(f"Subset CSV file saved to {output_file}")