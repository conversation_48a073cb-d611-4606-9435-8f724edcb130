[{"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>, Jr.'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is Frits Castricum's occupation?"}, {"text": "What is <PERSON> <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON> <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON><PERSON><PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is <PERSON><PERSON>'s occupation?"}, {"text": "What is Sir <PERSON>, 3rd Baronet's occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "What is <PERSON>'s occupation?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON>z<PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was Volodymyr <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was Aleksan<PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was Florence <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON>zas<PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> Vilde born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was Gyö<PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON> Mohammed born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was Franghís<PERSON> born?"}, {"text": "In what city was <PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON><PERSON><PERSON> born?"}, {"text": "In what city was <PERSON> born?"}, {"text": "What genre is Drive On?"}, {"text": "What genre is Mother?"}, {"text": "What genre is <PERSON> and My Friend?"}, {"text": "What genre is Unknown?"}, {"text": "What genre is Reach?"}, {"text": "What genre is <PERSON>?"}, {"text": "What genre is <PERSON>?"}, {"text": "What genre is Operation Sabotage?"}, {"text": "What genre is The Gap?"}, {"text": "What genre is Dark Matter?"}, {"text": "What genre is Chaotic?"}, {"text": "What genre is Flare?"}, {"text": "What genre is Brain Slaves?"}, {"text": "What genre is The New World?"}, {"text": "What genre is <PERSON><PERSON><PERSON><PERSON><PERSON>?"}, {"text": "What genre is Drill?"}, {"text": "What genre is Settle?"}, {"text": "What genre is Magic Music?"}, {"text": "What genre is Voyage?"}, {"text": "What genre is <PERSON>?"}, {"text": "What genre is <PERSON><PERSON>?"}, {"text": "What genre is To Mother?"}, {"text": "What genre is Magic?"}, {"text": "What genre is The Harrowing?"}, {"text": "What genre is Yellow?"}, {"text": "What genre is Hara?"}, {"text": "What genre is Nightdreamers?"}, {"text": "What genre is The Song of the Suburbs?"}, {"text": "What genre is The Club?"}, {"text": "What genre is Eddie & the Gang with No Name?"}, {"text": "What genre is Koko ni Iruzee!?"}, {"text": "What genre is Cut?"}, {"text": "What genre is Stories?"}, {"text": "What genre is Most of Me?"}, {"text": "What genre is I Lost My Heart in Heidelberg?"}, {"text": "What genre is VS?"}, {"text": "What genre is Seven Veils?"}, {"text": "What genre is Bridge?"}, {"text": "What genre is Deivos?"}, {"text": "What genre is Martinez?"}, {"text": "What genre is Chariot Race?"}, {"text": "What genre is Progression?"}, {"text": "What genre is The Take?"}, {"text": "What genre is Conversations?"}, {"text": "What genre is Mars?"}, {"text": "What genre is Dimensions?"}, {"text": "What genre is Astro?"}, {"text": "What genre is The Angel?"}, {"text": "What genre is Tempting Danger?"}, {"text": "What genre is I Will Be There?"}, {"text": "What genre is Detour for <PERSON>?"}, {"text": "What genre is Drama?"}, {"text": "What genre is <PERSON>?"}, {"text": "What genre is Gone?"}, {"text": "What genre is Compass?"}, {"text": "What genre is Apollo?"}, {"text": "What genre is <PERSON>?"}, {"text": "What genre is The Box?"}, {"text": "What genre is In Deep?"}, {"text": "What genre is Fantasy?"}, {"text": "What genre is Just a Matter of Time?"}, {"text": "What genre is Reminiscences?"}, {"text": "What genre is My Way?"}, {"text": "What genre is Our Time?"}, {"text": "What genre is El honorable Se<PERSON><PERSON>?"}, {"text": "What genre is Piel?"}, {"text": "What genre is Collaboration West?"}, {"text": "What genre is Thin Ice?"}, {"text": "What genre is The Promoter?"}, {"text": "What genre is Shine?"}, {"text": "What genre is Zones?"}, {"text": "What genre is The Gift?"}, {"text": "What genre is Gene?"}, {"text": "What genre is Evil?"}, {"text": "What genre is <PERSON>?"}, {"text": "What genre is Serving You?"}, {"text": "What genre is Neighbours?"}, {"text": "What genre is In Silence?"}, {"text": "What genre is A Winter of Cyclists?"}, {"text": "What genre is Back to Back?"}, {"text": "What genre is Strength?"}, {"text": "What genre is All the Years?"}, {"text": "What genre is Let It Go?"}, {"text": "What genre is Drôles de zèbres?"}, {"text": "What genre is The <PERSON> Story?"}, {"text": "What genre is Betrayal?"}, {"text": "What genre is Tempting The Gods: The Selected Stories of <PERSON><PERSON>, Volume 1?"}, {"text": "What genre is Let It Be You?"}, {"text": "What genre is Scorpio?"}, {"text": "What genre is Right There?"}, {"text": "What genre is El usurpador?"}, {"text": "What genre is Fire?"}, {"text": "What genre is The Moment?"}, {"text": "What genre is Strangers?"}, {"text": "What genre is Info?"}, {"text": "What genre is Theatre?"}, {"text": "What genre is Background?"}, {"text": "What genre is Node?"}, {"text": "What genre is In Deep?"}, {"text": "What genre is If I Ever?"}, {"text": "What genre is More Love?"}, {"text": "What genre is The Remarkable Exploits of <PERSON><PERSON> Biggs, Spaceman?"}, {"text": "What genre is My Husband?"}, {"text": "What genre is West?"}, {"text": "What genre is It Sounds Like?"}, {"text": "What genre is The Other Man?"}, {"text": "What genre is Wake Up?"}, {"text": "What genre is The Copper?"}, {"text": "What genre is A Question and Answer Guide to Astronomy?"}, {"text": "What genre is Buono! 2?"}, {"text": "What genre is The Blue Aura?"}, {"text": "What genre is Heaven?"}, {"text": "What genre is Heist?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON> of Capua?"}, {"text": "Who is the father of <PERSON><PERSON>?"}, {"text": "Who is the father of <PERSON><PERSON><PERSON> of Spoleto?"}, {"text": "Who is the father of <PERSON><PERSON>?"}, {"text": "Who is the father of <PERSON><PERSON>?"}, {"text": "Who is the father of <PERSON><PERSON>?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON><PERSON>?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON><PERSON>?"}, {"text": "Who is the father of But Why Not?"}, {"text": "Who is the father of Match II?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of Now What?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of Sir <PERSON><PERSON>, 3rd Baronet?"}, {"text": "Who is the father of <PERSON><PERSON>?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON>?"}, {"text": "Who is the father of <PERSON><PERSON>?"}, {"text": "In what country is Union State Bank, Wisconsin?"}, {"text": "In what country is <PERSON>?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Sar Giz?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Dell?"}, {"text": "In what country is Bandrakottai?"}, {"text": "In what country is Fairview Outdoor School?"}, {"text": "In what country is Kılıçlı Kavlaklı?"}, {"text": "In what country is Ago?"}, {"text": "In what country is Égligny?"}, {"text": "In what country is Bitchū-Kawamo Station?"}, {"text": "In what country is Borysławice?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Tartaczek?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Ahmadabad-e Razavi?"}, {"text": "In what country is Freedom?"}, {"text": "In what country is Ciepień?"}, {"text": "In what country is Blenheim?"}, {"text": "In what country is <PERSON>?"}, {"text": "In what country is Gmina Lubsza?"}, {"text": "In what country is Tsutsui Station?"}, {"text": "In what country is Edmundston?"}, {"text": "In what country is Rahzan?"}, {"text": "In what country is Kozići?"}, {"text": "In what country is Valdearcos de la Vega?"}, {"text": "In what country is Ciucurul Orbului River?"}, {"text": "In what country is Gaustadalléen?"}, {"text": "In what country is Poręba-Kocęby?"}, {"text": "In what country is Dubicze Osoczne?"}, {"text": "In what country is Joys?"}, {"text": "In what country is Laxmipur, Mahakali?"}, {"text": "In what country is <PERSON><PERSON><PERSON>?"}, {"text": "In what country is Lewałd Wielki?"}, {"text": "In what country is Kamioka Station?"}, {"text": "In what country is Quebec Route 213?"}, {"text": "In what country is Al-Fajr Arabsalim?"}, {"text": "In what country is Dąbkowice, Łódź Voivodeship?"}, {"text": "In what country is Borzymy, Kolno County?"}, {"text": "In what country is Fontenay?"}, {"text": "In what country is Valea Seacă River?"}, {"text": "In what country is Punghina?"}, {"text": "In what country is Ormak, Isfahan?"}, {"text": "In what country is Vera?"}, {"text": "In what country is Kuczynka?"}, {"text": "In what country is West Wyomissing?"}, {"text": "In what country is Tigra?"}, {"text": "In what country is Jauldes?"}, {"text": "In what country is Nowa Wieś Reszelska?"}, {"text": "In what country is Colonia Nueva Coneta?"}, {"text": "In what country is Aminabad?"}, {"text": "In what country is Tholuvankadu?"}, {"text": "In what country is Anaikudam?"}, {"text": "In what country is Society of Early Americanists?"}, {"text": "In what country is Denmark Hill Insect Bed?"}, {"text": "In what country is Mavjinjava?"}, {"text": "In what country is <PERSON>?"}, {"text": "In what country is Pârâul Bogat?"}, {"text": "In what country is Têbo?"}, {"text": "In what country is Sholoktu?"}, {"text": "In what country is Goldasht, Sistan and Baluchestan?"}, {"text": "In what country is Eslamabad-e Mashayekh?"}, {"text": "In what country is Kalu?"}, {"text": "In what country is Pierce?"}, {"text": "In what country is Chalhuacocha?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Oscar?"}, {"text": "In what country is Cora?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Oborín?"}, {"text": "In what country is Ježov?"}, {"text": "In what country is Drăgăneasa River?"}, {"text": "In what country is Batsère?"}, {"text": "In what country is WZRU?"}, {"text": "In what country is Idlorpait?"}, {"text": "In what country is Barice, <PERSON><PERSON>?"}, {"text": "In what country is Habit?"}, {"text": "In what country is Sabiote?"}, {"text": "In what country is Kalateh-ye Safdarabad?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Tad?"}, {"text": "In what country is Fântâneaua Rece River?"}, {"text": "In what country is Panaitoliko?"}, {"text": "In what country is Villalcampo?"}, {"text": "In what country is <PERSON><PERSON><PERSON><PERSON>?"}, {"text": "In what country is Toronto Northwest?"}, {"text": "In what country is <PERSON><PERSON><PERSON>?"}, {"text": "In what country is Hobbledehoy Record Co.?"}, {"text": "In what country is SWEAT?"}, {"text": "In what country is Dəhnəxəlil?"}, {"text": "In what country is Khvajeh So<PERSON>l?"}, {"text": "In what country is Zec Petawaga?"}, {"text": "In what country is Tapay District?"}, {"text": "In what country is Cổ Linh?"}, {"text": "In what country is Mahaboboka?"}, {"text": "In what country is Cześniki-Kolonia Górna?"}, {"text": "In what country is Awe?"}, {"text": "In what country is Mrákotín?"}, {"text": "In what country is Pichlice?"}, {"text": "In what country is pero?"}, {"text": "In what country is Khafr County?"}, {"text": "In what country is İnstitut?"}, {"text": "In what country is Karimu?"}, {"text": "In what country is Graitschen bei Bürgel?"}, {"text": "In what country is Durrenentzen?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is United States Post Office and Courthouse–Billings?"}, {"text": "In what country is Eshkevar-e Sofla Rural District?"}, {"text": "In what country is Rizuiyeh?"}, {"text": "In what country is Veliko Korenovo?"}, {"text": "In what country is Gimenells i el Pla de la Font?"}, {"text": "In what country is La Roche-Clermault?"}, {"text": "In what country is Biały Kościół, Lower Silesian Voivodeship?"}, {"text": "In what country is Content?"}, {"text": "In what country is Zhukiv?"}, {"text": "In what country is Saint-Vincent-de-Salers?"}, {"text": "In what country is Weed?"}, {"text": "In what country is Alder?"}, {"text": "In what country is Brizambourg?"}, {"text": "In what country is Călmuș River?"}, {"text": "In what country is Eschbronn?"}, {"text": "In what country is Kondh, Surendranagar?"}, {"text": "In what country is Rogers?"}, {"text": "In what country is Cos?"}, {"text": "In what country is <PERSON><PERSON> Ursula Gakuen Junior College?"}, {"text": "In what country is Selkirk Generating Station?"}, {"text": "In what country is Devalan?"}, {"text": "In what country is Dârmocsa River?"}, {"text": "In what country is Gori <PERSON>?"}, {"text": "In what country is <PERSON><PERSON><PERSON>?"}, {"text": "In what country is Francis?"}, {"text": "In what country is Łazy, Sierpc County?"}, {"text": "In what country is Khishig-Öndör?"}, {"text": "In what country is Gaffarlı?"}, {"text": "In what country is Crow Harbour, New Brunswick?"}, {"text": "In what country is Łodygowo, Pisz County?"}, {"text": "In what country is <PERSON>'s cabinet?"}, {"text": "In what country is canton of Baugy?"}, {"text": "In what country is Anjoma?"}, {"text": "In what country is Ittamalliyagoda?"}, {"text": "In what country is Abra, Ivory Coast?"}, {"text": "In what country is Okunakayama-Kōgen Station?"}, {"text": "In what country is DeWitt Township?"}, {"text": "In what country is Centre?"}, {"text": "In what country is Asahi Station?"}, {"text": "In what country is Stare Brzóski?"}, {"text": "In what country is Bud?"}, {"text": "In what country is Tangal-e Behdan?"}, {"text": "In what country is Seed 97.5 FM?"}, {"text": "In what country is Perth?"}, {"text": "In what country is Ara?"}, {"text": "In what country is Wir, Masovian Voivodeship?"}, {"text": "In what country is Avarzaman?"}, {"text": "In what country is Anaran Rural District?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Otto-Selz-Institute of Applied Psychology?"}, {"text": "In what country is Jodłówka gas field?"}, {"text": "In what country is Tupper-Barnett House?"}, {"text": "In what country is Izvorul Morarului River?"}, {"text": "In what country is Mehran Kushk?"}, {"text": "In what country is Astrodomi Observatory?"}, {"text": "In what country is Muratdere?"}, {"text": "In what country is Miętkie-Kolonia?"}, {"text": "In what country is Szczecin Scientific Society?"}, {"text": "In what country is Märstetten?"}, {"text": "In what country is Riethnordhausen?"}, {"text": "In what country is Tervola Radio and TV-Mast?"}, {"text": "In what country is Abdul <PERSON> Stadium?"}, {"text": "In what country is Alu?"}, {"text": "In what country is Chotýčany?"}, {"text": "In what country is Asseek River?"}, {"text": "In what country is Gąsiorowo, Legionowo County?"}, {"text": "In what country is Jeqjeq-e Pain?"}, {"text": "In what country is Dragomirna River?"}, {"text": "In what country is Mohammadabad-e Razzaqzadeh?"}, {"text": "In what country is Grant?"}, {"text": "In what country is Rubim do Norte River?"}, {"text": "In what country is Institute of Chemistry of Ireland?"}, {"text": "In what country is Lima?"}, {"text": "In what country is KMEI-LP?"}, {"text": "In what country is Záblatí?"}, {"text": "In what country is Ba Thín River?"}, {"text": "In what country is El Carmen Rivero Tórrez?"}, {"text": "In what country is Kawahigashi Station?"}, {"text": "In what country is Los Santos mine?"}, {"text": "In what country is Whited Township?"}, {"text": "In what country is Asalem Rural District?"}, {"text": "In what country is Contest?"}, {"text": "In what country is Genoa?"}, {"text": "In what country is Normania Township?"}, {"text": "In what country is Chicche District?"}, {"text": "In what country is canton of Marseille-La Pomme?"}, {"text": "In what country is Devanur?"}, {"text": "In what country is Tegher?"}, {"text": "In what country is Kodki?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is New England?"}, {"text": "In what country is Kowale, Lower Silesian Voivodeship?"}, {"text": "In what country is Obeakpu?"}, {"text": "In what country is La Couarde-sur-Mer?"}, {"text": "In what country is <PERSON><PERSON><PERSON>heimer Berg?"}, {"text": "In what country is Alexeni River?"}, {"text": "In what country is Villers-sous-Foucarmont?"}, {"text": "In what country is North Lake?"}, {"text": "In what country is 112th United States Colored Infantry?"}, {"text": "In what country is Storsteinnes Chapel?"}, {"text": "In what country is Ch'uch'u Apachita?"}, {"text": "In what country is Bārta?"}, {"text": "In what country is Urge?"}, {"text": "In what country is <PERSON><PERSON><PERSON><PERSON>?"}, {"text": "In what country is Vaiea?"}, {"text": "In what country is Monitor House?"}, {"text": "In what country is Sagoni?"}, {"text": "In what country is Eeuwfeestkliniek?"}, {"text": "In what country is Łupiny, Masovian Voivodeship?"}, {"text": "In what country is Xaga?"}, {"text": "In what country is Babino, Haiti?"}, {"text": "In what country is Hatnagoda?"}, {"text": "In what country is Deodara?"}, {"text": "In what country is Puzdrowizna?"}, {"text": "In what country is Harisan?"}, {"text": "In what country is Ločenice?"}, {"text": "In what country is Aki?"}, {"text": "In what country is Taia River?"}, {"text": "In what country is Sjösa?"}, {"text": "In what country is Morales de Campos?"}, {"text": "In what country is Dobra River?"}, {"text": "In what country is Karahasanlı?"}, {"text": "In what country is Ackerman-Dewsnap House?"}, {"text": "In what country is Wilcza Jama, Sokółka County?"}, {"text": "In what country is Givron?"}, {"text": "In what country is Humane Heritage Museum?"}, {"text": "In what country is Arlington?"}, {"text": "In what country is Adams?"}, {"text": "In what country is Pira?"}, {"text": "In what country is Tōhoku History Museum?"}, {"text": "In what country is <PERSON>?"}, {"text": "In what country is Korean Magazine Museum?"}, {"text": "In what country is Francheville Aerodrome?"}, {"text": "In what country is Kijevac?"}, {"text": "In what country is Iron River (CDP), Wisconsin?"}, {"text": "In what country is Lätäseno?"}, {"text": "In what country is Mount Shinten?"}, {"text": "In what country is Dual Plover?"}, {"text": "In what country is Saint-Antonin?"}, {"text": "In what country is <PERSON>?"}, {"text": "In what country is Joy?"}, {"text": "In what country is Valea Pleșii River?"}, {"text": "In what country is Sutlepa?"}, {"text": "In what country is Movraž?"}, {"text": "In what country is Sarnowo, Chełmno County?"}, {"text": "In what country is Saint-Pierrevillers?"}, {"text": "In what country is Archipelago Museum?"}, {"text": "In what country is <PERSON><PERSON><PERSON><PERSON>?"}, {"text": "In what country is Willow River?"}, {"text": "In what country is Uñón District?"}, {"text": "In what country is Ban On?"}, {"text": "In what country is Kanaküla?"}, {"text": "In what country is Breitenfelde?"}, {"text": "In what country is Konjsko Brdo?"}, {"text": "In what country is New York State Route 157?"}, {"text": "In what country is Le Moustoir?"}, {"text": "In what country is Mackay Courthouse?"}, {"text": "In what country is <PERSON><PERSON><PERSON>?"}, {"text": "In what country is Robinson?"}, {"text": "In what country is Lambertz Open by STAWAG?"}, {"text": "In what country is Goreme?"}, {"text": "In what country is Gawarzec Dolny?"}, {"text": "In what country is Studzianka, Podlaskie Voivodeship?"}, {"text": "In what country is Gare de Rosporden?"}, {"text": "In what country is <PERSON>?"}, {"text": "In what country is <PERSON><PERSON>?"}, {"text": "In what country is Rozsochatec?"}, {"text": "Who was the producer of <PERSON>, Jr.?"}, {"text": "Who was the producer of O skliros andras?"}, {"text": "Who was the producer of The Hunt?"}, {"text": "Who was the producer of The Accused?"}, {"text": "Who was the producer of Just Like Us?"}, {"text": "Who was the producer of Today?"}, {"text": "Who was the producer of The Pioneers?"}, {"text": "Who was the producer of The Deal?"}, {"text": "Who was the producer of On Tour?"}, {"text": "Who was the producer of The Baby on the Barge?"}, {"text": "Who was the producer of The Trap?"}, {"text": "Who was the producer of The Hayseeds' Back-blocks Show?"}, {"text": "Who was the producer of <PERSON>?"}, {"text": "Who was the producer of From Now On?"}, {"text": "Who was the producer of <PERSON>'s Wife?"}, {"text": "Who was the producer of Italian Style?"}, {"text": "Who was the producer of Strand?"}, {"text": "Who was the producer of The Thing We Love?"}, {"text": "Who was the producer of One of Those?"}, {"text": "Who was the producer of The Lie?"}, {"text": "Who was the producer of Early Man?"}, {"text": "Who was the producer of The Garden of Weeds?"}, {"text": "Who was the producer of Maling Kutang?"}, {"text": "Who was the producer of Party?"}, {"text": "Who was the producer of Saturday Morning?"}, {"text": "Who was the producer of <PERSON> and Child?"}, {"text": "Who was the producer of Revelations?"}, {"text": "Who was the producer of Home?"}, {"text": "Who was the producer of The Test?"}, {"text": "Who was the producer of Me First?"}, {"text": "Who was the producer of <PERSON>?"}, {"text": "Who was the producer of Trains of Winnipeg?"}, {"text": "Who was the producer of In the Family?"}, {"text": "Who was the producer of The Easiest Way?"}, {"text": "Who was the producer of Hired!?"}, {"text": "Who was the producer of <PERSON>?"}, {"text": "Who was the producer of De Laatste Dagen van een Eiland?"}, {"text": "Who was the director of City of Beautiful Nonsense?"}, {"text": "Who was the director of The Sisters?"}, {"text": "Who was the director of Those Who Love?"}, {"text": "Who was the director of Chi?"}, {"text": "Who was the director of The Happy Family?"}, {"text": "Who was the director of The Only Woman?"}, {"text": "Who was the director of The Gamble?"}, {"text": "Who was the director of Senior Year?"}, {"text": "Who was the director of Victory?"}, {"text": "Who was the director of Me First?"}, {"text": "Who was the director of Pilot?"}, {"text": "Who was the director of La renzoni?"}, {"text": "Who was the director of <PERSON>?"}, {"text": "Who was the director of Homecoming?"}, {"text": "Who was the director of Thank You, <PERSON>?"}, {"text": "Who was the director of All the Way Up?"}, {"text": "Who was the director of Zonnetje?"}, {"text": "Who was the director of College?"}, {"text": "Who was the director of Practical Jokers?"}, {"text": "Who was the director of The Tree?"}, {"text": "Who was the director of Driven?"}, {"text": "Who was the director of Son contento?"}, {"text": "Who was the director of Taxi at Midnight?"}, {"text": "Who was the director of Freedom?"}, {"text": "Who was the director of Balance?"}, {"text": "Who was the director of Faith?"}, {"text": "Who was the director of On the Run?"}, {"text": "Who was the director of Variety?"}, {"text": "Who was the director of The Night Riders?"}, {"text": "Who was the director of <PERSON> and <PERSON>?"}, {"text": "Who was the director of La cruz?"}, {"text": "Who was the director of The Love Nest?"}, {"text": "Who was the director of The Resolve?"}, {"text": "Who was the director of Out?"}, {"text": "Who was the director of While There is Still Time?"}, {"text": "Who was the director of Den store gavtyv?"}, {"text": "Who was the director of The Physician?"}, {"text": "Who was the director of El Último perro?"}, {"text": "Who was the director of The Easiest Way?"}, {"text": "Who was the director of The Betrayed?"}, {"text": "Who was the director of Sacrifice?"}, {"text": "Who was the director of Women Who Work?"}, {"text": "Who was the director of Trail?"}, {"text": "Who was the director of Det var paa Rundetaarn?"}, {"text": "Who was the director of The Barrier?"}, {"text": "Who was the director of Genius?"}, {"text": "Who was the director of Men and Women?"}, {"text": "Who was the director of Sold?"}, {"text": "Who was the director of The Saint?"}, {"text": "Who was the director of The Pioneers?"}, {"text": "Who was the director of Broadway Jones?"}, {"text": "Who was the director of The Last Word?"}, {"text": "Who was the director of Escape?"}, {"text": "Who was the director of These Children?"}, {"text": "Who was the director of Emergency Landing?"}, {"text": "Who was the director of Pilot?"}, {"text": "Who was the director of La Rival?"}, {"text": "Who was the director of Echo?"}, {"text": "Who was the director of The Trap?"}, {"text": "Who was the director of Cocktail?"}, {"text": "Who was the director of Mother and Child?"}, {"text": "Who was the director of The Pigskin Palooka?"}, {"text": "Who was the director of Public Opinion?"}, {"text": "Who was the director of College?"}, {"text": "Who was the director of Day by Day?"}, {"text": "Who was the director of The Day?"}, {"text": "Who was the director of Le Guérisseur?"}, {"text": "Who was the director of The Photo?"}, {"text": "Who was the director of Bingo?"}, {"text": "Who was the director of Big Dreams Little Tokyo?"}, {"text": "Who was the director of A Rowboat Romance?"}, {"text": "Who was the director of Young People?"}, {"text": "Who was the director of The Kiss?"}, {"text": "Who was the director of Indizienbeweis?"}, {"text": "Who was the director of Accident?"}, {"text": "Who was the director of Fingers?"}, {"text": "Who was the director of The Girl in Mourning?"}, {"text": "Who was the director of September?"}, {"text": "Who was the director of The Return?"}, {"text": "Who was the director of Vanity?"}, {"text": "Who was the director of Ghost?"}, {"text": "Who was the director of One of Those?"}, {"text": "Who was the director of The Key?"}, {"text": "Who was the director of The Wolf?"}, {"text": "Who was the director of <PERSON><PERSON>?"}, {"text": "Who was the director of The Valley?"}, {"text": "Who was the director of <PERSON>?"}, {"text": "Who was the director of The Loudwater Mystery?"}, {"text": "Who was the director of Pilot?"}, {"text": "Who was the director of Hakeem's New Flame?"}, {"text": "Who was the director of Just Like Us?"}, {"text": "Who was the director of A Helpful Sisterhood?"}, {"text": "Who was the director of Panic?"}, {"text": "Who was the director of Victory?"}, {"text": "Who was the director of Not So Long Ago?"}, {"text": "What is Kluczewsko the capital of?"}, {"text": "What is <PERSON><PERSON><PERSON> the capital of?"}, {"text": "What is Bolsheustyikinskoye the capital of?"}, {"text": "What is <PERSON><PERSON><PERSON><PERSON> the capital of?"}, {"text": "What is I<PERSON> the capital of?"}, {"text": "Who was the screenwriter for Death of a Batman?"}, {"text": "Who was the screenwriter for Fear No More?"}, {"text": "Who was the screenwriter for The Fake?"}, {"text": "Who was the screenwriter for <PERSON><PERSON><PERSON> pintadas?"}, {"text": "Who was the screenwriter for <PERSON>?"}, {"text": "Who was the screenwriter for <PERSON>?"}, {"text": "Who was the screenwriter for Party?"}, {"text": "Who was the screenwriter for <PERSON>?"}, {"text": "Who was the screenwriter for <PERSON>?"}, {"text": "Who was the screenwriter for By og land hand i hand?"}, {"text": "Who was the screenwriter for The Accused?"}, {"text": "Who was the screenwriter for Exit the Vamp?"}, {"text": "Who was the screenwriter for <PERSON><PERSON><PERSON> <PERSON><PERSON>?"}, {"text": "Who was the screenwriter for Democracy?"}, {"text": "Who was the screenwriter for Revelations?"}, {"text": "Who was the screenwriter for Ending It?"}, {"text": "Who was the screenwriter for <PERSON><PERSON>?"}, {"text": "Who was the screenwriter for <PERSON><PERSON><PERSON>?"}, {"text": "Who was the screenwriter for Salvation?"}, {"text": "Who was the screenwriter for The Last Word?"}, {"text": "Who was the screenwriter for <PERSON><PERSON><PERSON><PERSON>?"}, {"text": "Who was the screenwriter for White Gold?"}, {"text": "Who was the screenwriter for The Bride’s Journey?"}, {"text": "Who was the screenwriter for <PERSON>?"}, {"text": "Who was the screenwriter for These Children?"}, {"text": "Who was the screenwriter for Prototype?"}, {"text": "Who was the screenwriter for <PERSON>'s <PERSON> Boarder?"}, {"text": "Who was the screenwriter for Le Fils d'Amr est mort?"}, {"text": "Who was the screenwriter for <PERSON><PERSON>?"}, {"text": "Who was the screenwriter for The Worst Years of Our Lives?"}, {"text": "Who was the screenwriter for The City?"}, {"text": "Who was the screenwriter for <PERSON>: My Life... Your Fault?"}, {"text": "Who was the screenwriter for Three Loves in Rio?"}, {"text": "Who was the screenwriter for <PERSON><PERSON><PERSON>?"}, {"text": "Who was the screenwriter for The Return?"}, {"text": "Who was the screenwriter for Oregon?"}, {"text": "Who was the screenwriter for <PERSON><PERSON>?"}, {"text": "Who was the screenwriter for Impossible?"}, {"text": "Who was the screenwriter for The Accused?"}, {"text": "Who was the screenwriter for Daybreak?"}, {"text": "Who was the screenwriter for <PERSON><PERSON><PERSON><PERSON>, mas<PERSON><PERSON>?"}, {"text": "Who was the screenwriter for <PERSON>?"}, {"text": "Who was the composer of One?"}, {"text": "Who was the composer of Hello?"}, {"text": "Who was the composer of <PERSON>?"}, {"text": "Who was the composer of Solo?"}, {"text": "Who was the composer of To Live?"}, {"text": "Who was the composer of <PERSON><PERSON>?"}, {"text": "Who was the composer of To The West?"}, {"text": "Who was the composer of The Witch?"}, {"text": "Who was the composer of Images?"}, {"text": "Who was the composer of <PERSON><PERSON><PERSON>?"}, {"text": "Who was the composer of I'm in Love?"}, {"text": "Who was the composer of Prelude in F major, Op. 49, No. 2?"}, {"text": "Who was the composer of Piano Concerto?"}, {"text": "Who was the composer of <PERSON><PERSON><PERSON><PERSON> euch <PERSON>, bedr<PERSON><PERSON><PERSON>, BWV 224?"}, {"text": "Who was the composer of Homecoming?"}, {"text": "Who was the composer of The Greater Good, or the Passion of <PERSON><PERSON> de Suif?"}, {"text": "Who was the composer of G<PERSON>la!?"}, {"text": "Who was the composer of The Giants?"}, {"text": "Who was the composer of To the Sky?"}, {"text": "Who was the composer of Say When?"}, {"text": "Who was the composer of Alone?"}, {"text": "Who was the composer of Famous?"}, {"text": "Who was the composer of Signal?"}, {"text": "Who was the composer of Miss You?"}, {"text": "Who was the composer of Living with You?"}, {"text": "Who was the composer of <PERSON>?"}, {"text": "Who was the composer of Images?"}, {"text": "Who was the composer of The Hope?"}, {"text": "Who was the composer of Time Machine?"}, {"text": "Who was the composer of Porch?"}, {"text": "Who was the composer of <PERSON>?"}, {"text": "Who was the composer of Nozze istriane?"}, {"text": "Who was the composer of Overture in G major?"}, {"text": "Who was the composer of Tea for One?"}, {"text": "Who was the composer of <PERSON><PERSON>?"}, {"text": "Who was the composer of String Quartet No. 3?"}, {"text": "Who was the composer of That's Right?"}, {"text": "Who was the composer of Symphony No. 33?"}, {"text": "Who was the composer of Symphony No. 8?"}, {"text": "Who was the composer of Discipline?"}, {"text": "Who was the composer of Cue Ball Cat?"}, {"text": "Who was the composer of One More Time?"}, {"text": "Who was the composer of Big Foot?"}, {"text": "Who was the composer of Sometime?"}, {"text": "Who was the composer of Prelude for Clarinet?"}, {"text": "Who was the composer of The Moment's Energy?"}, {"text": "Who was the composer of <PERSON>?"}, {"text": "Who was the composer of Miss You?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of Pierre-Antoine <PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of Guadalupe Missionaries?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of Pedro <PERSON>í<PERSON> de Jesús Vílchez Vílchez?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of Ecclesiastical Statistics?"}, {"text": "What is the religion of <PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON>?"}, {"text": "What is the religion of <PERSON>?"}, {"text": "What is the religion of <PERSON><PERSON><PERSON>?"}, {"text": "What sport does 2012 Georgetown Hoyas men's soccer team play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does 2006 Korea Open Badminton Championships play?"}, {"text": "What sport does Sol Ky-Ong play?"}, {"text": "What sport does 2006–07 Primera B Nacional play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does 1994 Swedish Open play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does 2004 Legg Mason Tennis Classic play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 1988–89 FA Cup Qualifying Rounds play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 1989–90 1. Slovenská národná hokejová liga season play?"}, {"text": "What sport does 1923 in Brazilian football play?"}, {"text": "What sport does Ye Zhibin play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Imbi Hoop play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does 2014 Powiat Poznański Open play?"}, {"text": "What sport does 1997 Conference USA Baseball Tournament play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does 2002 Euro Beach Soccer Cup play?"}, {"text": "What sport does Sebastián Morquio play?"}, {"text": "What sport does Cho Ke<PERSON>-yeon play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 2013 Torneo di Viareggio play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does Diego Díaz Garrido play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does Koumiba Djossouvi play?"}, {"text": "What sport does 2010–11 South West Peninsula League play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 1990–91 British Basketball League season play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does Zanzibar national under-20 football team play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 2001–02 Division 1 season play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Israel Andrade play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Indonesia Education League play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does Daigo Watanabe play?"}, {"text": "What sport does WTA South Orange play?"}, {"text": "What sport does Shuto Suzuki play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Masahito Noto play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON>u play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Oscar <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does 1994–95 FIBA Women's European Champions Cup play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Momo Wall Blamo play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 2011–12 Elon Phoenix men's basketball team play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does FIBT World Championships 1939 play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 1973 Virginia Slims of Fort Lauderdale play?"}, {"text": "What sport does <PERSON><PERSON><PERSON>har <PERSON> play?"}, {"text": "What sport does 1949 France rugby union tour of Argentina play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does All-Ireland Senior Club Camogie Championship 1970 play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON> Andrade play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does 2008–09 National Indoor Soccer League season play?"}, {"text": "What sport does 1994–95 Fußball-Bundesliga play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does Granada Lions play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Afyonkarahisarspor play?"}, {"text": "What sport does canoeing at the 2014 Asian Games – women's K-4 500 metres play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does 1995 Cook Islands Round Cup play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Sofia Anker-Kofoed play?"}, {"text": "What sport does Kiribati men's national basketball team play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does Turkish Seniors Open play?"}, {"text": "What sport does Njurunda SK play?"}, {"text": "What sport does 2009 Ukrainian Cup Final play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Marc Santo-Roman play?"}, {"text": "What sport does Sandar IL play?"}, {"text": "What sport does Francesco Reda play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does E Sour El Ghozlane play?"}, {"text": "What sport does Lek Kcira play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 1998–99 Slovenian Basketball League play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does list of Azerbaijan football transfers winter 2012 play?"}, {"text": "What sport does Abdulhadi Khalaf play?"}, {"text": "What sport does VOKO-Irodion play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Guyana women's national field hockey team play?"}, {"text": "What sport does <PERSON><PERSON><PERSON>anasov play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does Lobos BUAP Premier play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Luka Glavaš play?"}, {"text": "What sport does Guo <PERSON> play?"}, {"text": "What sport does Mehmet Gürkan Öztürk play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON>u play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does Colombian Cycling Federation play?"}, {"text": "What sport does 1920–21 Northern Football League play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Université Nationale du Bénin FC play?"}, {"text": "What sport does 2012 Uzbekistan First League play?"}, {"text": "What sport does Nevio de <PERSON>o play?"}, {"text": "What sport does W<PERSON>jciech Jarmuż play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON> play?"}, {"text": "What sport does Best play?"}, {"text": "What sport does Cassiá play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON>ecido <PERSON> play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does Ernest Street play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does 2011 Chatham Cup play?"}, {"text": "What sport does Maltese Women's Cup play?"}, {"text": "What sport does 2009 Atlantic Coast Conference Baseball Tournament play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does Mutanda Kwesele play?"}, {"text": "What sport does <PERSON> play?"}, {"text": "What sport does <PERSON><PERSON> play?"}, {"text": "What sport does <PERSON><PERSON><PERSON><PERSON> play?"}, {"text": "Who is the author of Afternoon?"}, {"text": "Who is the author of Bed?"}, {"text": "Who is the author of Watchers at the Strait Gate?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of Only Human?"}, {"text": "Who is the author of Out of the Dark?"}, {"text": "Who is the author of The National Dream?"}, {"text": "Who is the author of Saints of Big Harbour?"}, {"text": "Who is the author of Endpeace?"}, {"text": "Who is the author of Turning On?"}, {"text": "Who is the author of Something More?"}, {"text": "Who is the author of The Romantic?"}, {"text": "Who is the author of Buried Thunder?"}, {"text": "Who is the author of Time Enough?"}, {"text": "Who is the author of Operator?"}, {"text": "Who is the author of Sail?"}, {"text": "Who is the author of Fire?"}, {"text": "Who is the author of Carnival of Souls?"}, {"text": "Who is the author of Mannfolk?"}, {"text": "Who is the author of Rage?"}, {"text": "Who is the author of Kid?"}, {"text": "Who is the author of It's Not an All Night Fair?"}, {"text": "Who is the author of Heaven?"}, {"text": "Who is the author of <PERSON> the Valiant?"}, {"text": "Who is the author of Darkvision?"}, {"text": "Who is the author of Regeneration?"}, {"text": "Who is the author of The Latimers?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of Nevis Mountain Dew?"}, {"text": "Who is the author of World of Wonder?"}, {"text": "Who is the author of Dancing on Coral?"}, {"text": "Who is the author of New Keywords?"}, {"text": "Who is the author of Getting Free?"}, {"text": "Who is the author of Shooting Sean?"}, {"text": "Who is the author of Looking Forward?"}, {"text": "Who is the author of The World Before?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of The End of the Soul?"}, {"text": "Who is the author of Western?"}, {"text": "Who is the author of The Warriors of Spider?"}, {"text": "Who is the author of Homecoming?"}, {"text": "Who is the author of The Amazon?"}, {"text": "Who is the author of O dia das calças roladas?"}, {"text": "Who is the author of Visionseeker: Shared Wisdom from the Place of Refuge?"}, {"text": "Who is the author of Out of This World?"}, {"text": "Who is the author of Stand By Your Screen?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of The Interior?"}, {"text": "Who is the author of Memory?"}, {"text": "Who is the author of Stations?"}, {"text": "Who is the author of School for Coquettes?"}, {"text": "Who is the author of Trust Me?"}, {"text": "Who is the author of Recursion?"}, {"text": "Who is the author of The Bishop's Heir?"}, {"text": "Who is the author of Talent?"}, {"text": "Who is the author of This Is It?"}, {"text": "Who is the author of A Survey?"}, {"text": "Who is the author of Skyscraper?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of This?"}, {"text": "Who is the author of <PERSON>, My Friend?"}, {"text": "Who is the author of The Great World and the Small: More Tales of the Ominous and Magical?"}, {"text": "Who is the author of Robots?"}, {"text": "Who is the author of The Outdoor Survival Handbook?"}, {"text": "Who is the author of Millennial Rites?"}, {"text": "Who is the author of Shame?"}, {"text": "Who is the author of The Burning?"}, {"text": "Who is the author of Second Generation?"}, {"text": "Who is the author of The Guard?"}, {"text": "Who is the author of West?"}, {"text": "Who is the author of Nuclear Alert?"}, {"text": "Who is the author of Malvaloca?"}, {"text": "Who is the author of <PERSON><PERSON>?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of The Museum of Abandoned Secrets?"}, {"text": "Who is the author of Responsibility?"}, {"text": "Who is the author of Villa Amalia?"}, {"text": "Who is the author of Zones?"}, {"text": "Who is the author of Warrior?"}, {"text": "Who is the author of Beyond?"}, {"text": "Who is the author of The Other Place?"}, {"text": "Who is the author of A Positive?"}, {"text": "Who is the author of Down?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of What You Make It?"}, {"text": "Who is the author of Great Short Novels of Adult Fantasy I?"}, {"text": "Who is the author of The Voice?"}, {"text": "Who is the author of Follow The Music?"}, {"text": "Who is the author of Time After Time?"}, {"text": "Who is the author of Across Many Mountains?"}, {"text": "Who is the author of Small Changes?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of Skin?"}, {"text": "Who is the author of The Techniques of Democracy?"}, {"text": "Who is the author of Death in Five Boxes?"}, {"text": "Who is the author of The Wizard in Wonderland?"}, {"text": "Who is the author of Transcens<PERSON>?"}, {"text": "Who is the author of With Women?"}, {"text": "Who is the author of Come On Over?"}, {"text": "Who is the author of For a Living?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of <PERSON><PERSON>?"}, {"text": "Who is the author of With?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of The Burning?"}, {"text": "Who is the author of The Sword of Shibito?"}, {"text": "Who is the author of <PERSON><PERSON><PERSON><PERSON><PERSON>?"}, {"text": "Who is the author of The Aware?"}, {"text": "Who is the author of Pen?"}, {"text": "Who is the author of Science-Fantasy Quintette?"}, {"text": "Who is the author of Sin?"}, {"text": "Who is the author of Weekend?"}, {"text": "Who is the author of Empire?"}, {"text": "Who is the author of The Empire?"}, {"text": "Who is the author of One of the Family?"}, {"text": "Who is the author of The Culture of Collaboration?"}, {"text": "Who is the author of Old Money?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of Señor <PERSON>?"}, {"text": "Who is the author of Het uur tussen hond en wolf?"}, {"text": "Who is the author of Eclipse?"}, {"text": "Who is the author of The Valley?"}, {"text": "Who is the author of Facing the Future?"}, {"text": "Who is the author of The Squirrel Wife?"}, {"text": "Who is the author of Moving Day?"}, {"text": "Who is the author of Close to Home?"}, {"text": "Who is the author of The Chaos Code?"}, {"text": "Who is the author of August?"}, {"text": "Who is the author of <PERSON><PERSON>?"}, {"text": "Who is the author of America's Secret War?"}, {"text": "Who is the author of Black?"}, {"text": "Who is the author of The Test?"}, {"text": "Who is the author of Darkness?"}, {"text": "Who is the author of Chelsea on the Edge?"}, {"text": "Who is the author of Men and Women?"}, {"text": "Who is the author of One More Time?"}, {"text": "Who is the author of Unknown?"}, {"text": "Who is the author of Baby?"}, {"text": "Who is the author of Time to Come?"}, {"text": "Who is the author of Template?"}, {"text": "Who is the author of American Dream, Global Nightmare?"}, {"text": "Who is the author of Patience?"}, {"text": "Who is the author of Neglected Aspects of Sufi Study?"}, {"text": "Who is the author of Smoke?"}, {"text": "Who is the author of The Great Perhaps?"}, {"text": "Who is the author of The Universe Around Us?"}, {"text": "Who is the author of Against the Odds?"}, {"text": "Who is the author of Branches?"}, {"text": "Who is the author of New York?"}, {"text": "Who is the author of Challenge?"}, {"text": "Who is the author of Dreams?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of Nice People?"}, {"text": "Who is the author of Falling?"}, {"text": "Who is the author of Love All?"}, {"text": "Who is the author of The Hero?"}, {"text": "Who is the author of The Sun Chemist?"}, {"text": "Who is the author of <PERSON><PERSON>'s Gift?"}, {"text": "Who is the author of <PERSON><PERSON><PERSON>?"}, {"text": "Who is the author of Always?"}, {"text": "Who is the author of The Economics and Ethics of Private Property?"}, {"text": "Who is the author of The Every Boy?"}, {"text": "Who is the author of The Middle Years?"}, {"text": "Who is the author of Chaotic?"}, {"text": "Who is the author of <PERSON><PERSON><PERSON>?"}, {"text": "Who is the author of Resistance?"}, {"text": "Who is the author of Into the Woods?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of Just a Matter of Time?"}, {"text": "Who is the author of Fruits?"}, {"text": "Who is the author of Shift?"}, {"text": "Who is the author of Federation?"}, {"text": "Who is the author of Therapy?"}, {"text": "Who is the author of <PERSON><PERSON><PERSON> Plays Chicken?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of Jää<PERSON><PERSON>?"}, {"text": "Who is the author of Prime Time?"}, {"text": "Who is the author of Trust Territory?"}, {"text": "Who is the author of Balance of Power?"}, {"text": "Who is the author of Panic?"}, {"text": "Who is the author of The Lie?"}, {"text": "Who is the author of <PERSON>'s Rules?"}, {"text": "Who is the author of V<PERSON>t katten har djuren själ!?"}, {"text": "Who is the author of Let's Not?"}, {"text": "Who is the author of Pursuit?"}, {"text": "Who is the author of Incoming?"}, {"text": "Who is the author of <PERSON>, Lord of the Black River?"}, {"text": "Who is the author of <PERSON>?"}, {"text": "Who is the author of Corridor?"}, {"text": "Who is the author of Non?"}, {"text": "Who is the author of Everything?"}, {"text": "Who is the author of Find Me?"}, {"text": "Who is the author of Partner?"}, {"text": "Who is the author of The Ball?"}, {"text": "Who is the author of Suicide?"}, {"text": "Who is the author of Witt?"}, {"text": "Who is the author of On the Road?"}, {"text": "Who is the author of The Outing?"}, {"text": "Who is the mother of <PERSON>?"}, {"text": "What is the capital of Ungheni County?"}, {"text": "What is the capital of Gmina Secemin?"}, {"text": "What is the capital of Yunguyo Province?"}, {"text": "What is the capital of canton of Saint-Doulchard?"}, {"text": "What is the capital of arrondissement of Castellane?"}, {"text": "What is the capital of Sánchez Carrión Province?"}, {"text": "What is the capital of Chiprovtsi Municipality?"}, {"text": "What is the capital of canton of Antibes-Biot?"}, {"text": "What is the capital of canton of Harnes?"}, {"text": "What is the capital of Sal?"}, {"text": "What is the capital of Kareličy District?"}, {"text": "What is the capital of Kambarsky District?"}, {"text": "What is the capital of Gmina Brzeszcze?"}, {"text": "What is the capital of Tarussky District?"}, {"text": "What is the capital of Gmina Czorsztyn?"}, {"text": "What is the capital of Verbandsgemeinde Bad Ems?"}, {"text": "What is the capital of canton of Gordes?"}, {"text": "What is the capital of Gmina Andrespol?"}, {"text": "What is the capital of Vozhegodsky District?"}, {"text": "What is the capital of arrondissement of Nogent-le-Rotrou?"}, {"text": "What is the capital of arrondissement of Lannion?"}, {"text": "What is the capital of canton of Mirambeau?"}, {"text": "What is the capital of Saanen District?"}, {"text": "What is the capital of Plaza?"}, {"text": "What is the capital of arrondissement of Florac?"}, {"text": "What is the capital of Yalutorovsky District?"}, {"text": "What is the capital of Gmina Osiecznica?"}, {"text": "What is the capital of Gmina Radomsko?"}, {"text": "What is the capital of Gmina Babice?"}]