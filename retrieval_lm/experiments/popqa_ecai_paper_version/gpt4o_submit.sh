#!/bin/bash
#SBATCH --job-name=popqa_gpt4o
#SBATCH --output=popqa_gpt4o_%j.out
#SBATCH --error=popqa_gpt4o_%j.err
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --partition=ampere
#SBATCH --nodes=1
#SBATCH --gpus-per-node=1
#SBATCH --time=48:00:00

# Activate conda environment if needed
source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag



# Run the GPT-4o script
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/gpt4o_popqa_original.py