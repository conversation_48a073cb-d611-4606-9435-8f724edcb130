import pandas as pd
import json
import ast # Import ast for safely evaluating string representations of lists

def load_json_lines(file_path):
    """Loads data from a JSON Lines file."""
    data = []
    with open(file_path, "r") as f:
        for line in f:
            if line.strip():  # Skip empty lines
                try:
                    data.append(json.loads(line))
                except json.JSONDecodeError as e:
                    print(f"Skipping invalid JSON line in {file_path}: {line.strip()} - Error: {e}")
    return data

def calculate_metrics(csv_file_path, json_file_path, correctness_column_name="correctness"):
    """
    Calculates metrics by comparing predictions from a JSON file against a CSV file.

    Args:
        csv_file_path (str): Path to the CSV file with entity data and possible answers.
        json_file_path (str): Path to the JSON file (standard JSON or JSON Lines) with predictions.
        correctness_column_name (str): Temporary name for correctness column in memory.

    Returns:
        dict: A dictionary containing the calculated metrics. Returns None if file processing fails.
    """
    try:
        # Load the CSV file
        df = pd.read_csv(csv_file_path)
        csv_length = len(df)
        print(f"\nAnalyzing files:")
        print(f"CSV file: {csv_file_path}")
        print(f"JSON file: {json_file_path}")
        print(f"Number of rows in CSV: {csv_length}")

        # Load the JSON file - Try standard JSON first, then JSON Lines
        predictions_data = []
        try:
            with open(json_file_path, 'r') as f:
                content = f.read()
                # Check if it looks like JSON lines or a single JSON object (list/dict)
                if content.strip().startswith('[') and content.strip().endswith(']'):
                     # Assume it's a single JSON list object
                     predictions_data = json.loads(content)
                elif content.strip().startswith('{') and content.strip().endswith('}'):
                     # Assume it's a single JSON dictionary object
                     # Check if it contains the 'preds' key directly
                     loaded_json = json.loads(content)
                     if "preds" in loaded_json and isinstance(loaded_json["preds"], list):
                         # If the structure is {"preds": [...]}, extract the list
                         # This handles the case where the file might be a dict with a 'preds' list
                         # But the user's example file is a list of dicts, so this might not be needed often
                         # We need the individual predictions, not the list itself here.
                         # Let's prioritize the list-of-dicts structure from the example.
                         # Revert to trying JSON Lines if direct 'preds' key isn't the primary structure needed.
                         print(f"Warning: JSON file {json_file_path} is a dictionary. Attempting to process as JSON Lines instead, as individual predictions are expected.")
                         predictions_data = load_json_lines(json_file_path)
                     else:
                         # If it's a dictionary but not the expected structure, try JSON Lines
                         print(f"Warning: JSON file {json_file_path} is a dictionary without a 'preds' list. Attempting to process as JSON Lines.")
                         predictions_data = load_json_lines(json_file_path)

                else:
                    # Assume JSON Lines format if it doesn't look like a standard list/dict
                    predictions_data = load_json_lines(json_file_path)

        except json.JSONDecodeError as e:
            print(f"Failed to decode {json_file_path} as standard JSON: {e}. Trying JSON Lines format.")
            predictions_data = load_json_lines(json_file_path)
        except Exception as e:
            print(f"An unexpected error occurred loading {json_file_path}: {e}")
            return None # Cannot proceed if JSON loading fails

        # --- FIX: Extract predictions correctly ---
        # Check if predictions_data is a list of dictionaries, each having a 'preds' key
        if not isinstance(predictions_data, list):
             print(f"Error: Expected predictions_data from {json_file_path} to be a list, but got {type(predictions_data)}")
             return None
        if not all(isinstance(item, dict) and 'preds' in item for item in predictions_data):
             print(f"Error: Not all items in {json_file_path} are dictionaries with a 'preds' key.")
             # Print first few problematic items for debugging
             for i, item in enumerate(predictions_data[:5]):
                 if not isinstance(item, dict):
                     print(f"  Item {i} is not a dict: {item}")
                 elif 'preds' not in item:
                     print(f"  Item {i} is missing 'preds' key: {item}")
             return None

        # Extract the 'preds' value from each dictionary in the list
        predictions = [item["preds"] for item in predictions_data]
        # --- End of FIX ---

        print(f"Number of predictions loaded: {len(predictions)}")

        # Ensure prediction list length equals number of rows in CSV
        if len(predictions) != csv_length:
            print(f"Error: Mismatch in lengths: CSV has {csv_length} rows but found {len(predictions)} predictions in {json_file_path}")
            # Optional: Add more debugging info here if needed
            # print("First few CSV rows (indices):", df.index[:5].tolist())
            # print("First few predictions:", predictions[:5])
            return None # Stop processing this file if lengths don't match

        # Add correctness in memory without modifying the CSV
        correctness_list = []
        for i, row in df.iterrows():
            try:
                # Safely evaluate the 'possible_answers' string into a Python list
                possible_answers = ast.literal_eval(row["possible_answers"])
                if not isinstance(possible_answers, list):
                    print(f"Warning: 'possible_answers' in row {i} is not a list: {row['possible_answers']}")
                    correctness_list.append(False)
                    continue

                # Ensure prediction is a string before lowercasing
                prediction_text = predictions[i]
                if not isinstance(prediction_text, str):
                     print(f"Warning: Prediction at index {i} is not a string: {prediction_text}. Treating as incorrect.")
                     correctness_list.append(False)
                     continue

                # Check if any answer is in the prediction (case-insensitive)
                is_correct = any(str(answer).lower() in prediction_text.lower() for answer in possible_answers)
                correctness_list.append(is_correct)

            except (SyntaxError, ValueError) as e:
                print(f"Warning: Could not evaluate 'possible_answers' in row {i}: {row['possible_answers']} - Error: {e}")
                correctness_list.append(False) # Mark as incorrect if answers can't be parsed
            except IndexError:
                print(f"Error: Trying to access prediction index {i}, but only {len(predictions)} predictions exist.")
                # This should ideally be caught by the length check earlier, but added as safety
                correctness_list.append(False)
            except Exception as e:
                print(f"An unexpected error occurred processing row {i}: {e}")
                correctness_list.append(False)


        # Check if correctness_list length matches df length before assigning
        if len(correctness_list) == len(df):
            df[correctness_column_name] = correctness_list
        else:
            print(f"Error: Length mismatch between DataFrame ({len(df)}) and calculated correctness ({len(correctness_list)}). Cannot calculate metrics for {json_file_path}.")
            return None


        # Calculate metrics
        metrics = {}

        # Overall correctness
        if not df[correctness_column_name].empty:
            metrics["Overall Correctness"] = df[correctness_column_name].mean()
        else:
            metrics["Overall Correctness"] = 0.0 # Or handle as NaN or error

        # Relevant entity unambiguous questions
        df_relevant_entity_unambiguous = df[df["relevant_entity_count"] <= 1]
        if not df_relevant_entity_unambiguous.empty and correctness_column_name in df_relevant_entity_unambiguous:
             metrics["Relevant Entity Unambiguous Correctness"] = df_relevant_entity_unambiguous[correctness_column_name].mean()
        else:
             metrics["Relevant Entity Unambiguous Correctness"] = 0.0 # Or handle appropriately

        # Relevant entity ambiguous questions
        df_relevant_entity_ambiguous = df[df["relevant_entity_count"] > 1]
        if not df_relevant_entity_ambiguous.empty and correctness_column_name in df_relevant_entity_ambiguous:
            metrics["Relevant Entity Ambiguous Correctness"] = df_relevant_entity_ambiguous[correctness_column_name].mean()
        else:
            metrics["Relevant Entity Ambiguous Correctness"] = 0.0 # Or handle appropriately

        # Add checks for the commented-out metrics if you re-enable them
        # # Completely unambiguous questions
        # df_completely_unambiguous = df[df["entity_name_occurrences"] <= 1]
        # if not df_completely_unambiguous.empty and correctness_column_name in df_completely_unambiguous:
        #     metrics["Unambiguous Correctness"] = df_completely_unambiguous[correctness_column_name].mean()
        # else:
        #     metrics["Unambiguous Correctness"] = 0.0

        # # Name ambiguous questions
        # df_name_ambiguous = df[df["entity_name_occurrences"] > 1]
        # if not df_name_ambiguous.empty and correctness_column_name in df_name_ambiguous:
        #     metrics["Name Ambiguous Correctness"] = df_name_ambiguous[correctness_column_name].mean()
        # else:
        #     metrics["Name Ambiguous Correctness"] = 0.0


        return metrics

    except FileNotFoundError as e:
        print(f"Error: File not found - {e}")
        return None
    except pd.errors.EmptyDataError:
        print(f"Error: CSV file {csv_file_path} is empty.")
        return None
    except KeyError as e:
        print(f"Error: Missing expected column in CSV or incorrect structure in JSON - {e}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred in calculate_metrics for {json_file_path}: {e}")
        return None


def batch_process_metrics(csv_file_path, prediction_files):
    """
    Processes multiple prediction files against a single CSV file and computes metrics for each.

    Args:
        csv_file_path (str): Path to the CSV file with entity data.
        prediction_files (list): List of paths to JSON prediction files.

    Returns:
        dict: A dictionary where each key is a JSON file path, and the value is a dictionary of metrics.
              Files that cause errors during processing will be skipped.
    """
    results = {}

    for json_file in prediction_files:
        # Call the calculate_metrics function for each prediction file
        metrics = calculate_metrics(csv_file_path, json_file)

        # Store the metrics ONLY if calculation was successful
        if metrics is not None:
            results[json_file] = metrics
        else:
            print(f"Skipping metrics for {json_file} due to errors during calculation.")


    return results

# --- Main execution part ---
# csv_file_path = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_original_ecai.csv"
# csv_file_path = '/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_ambiguous_subset.csv'
csv_file_path = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_ambiguous_subset.csv"

# Example using the single file from your error trace
prediction_files = [
    "popqa_original_gpt4o_ambiguous_only_no_retrieval_results.jsonl"
]

# # Example using multiple files (uncomment if needed)
# prediction_files = [
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_original_gpt4o_no_retrieval_results.jsonl",
#     # Add other .jsonl files here if they follow the same list-of-dictionaries structure
#     # "/path/to/another/prediction_file.jsonl",
# ]


# Call batch_process_metrics to compute metrics for all prediction files
results = batch_process_metrics(csv_file_path, prediction_files)

# Print results
print("\n--- Final Results ---")
if results:
    for json_file, metrics in results.items():
        print(f"\nMetrics for {json_file}:")
        for metric_name, value in metrics.items():
             # Format as percentage, handle potential None or non-numeric values gracefully
            try:
                print(f"  {metric_name}: {value:.1%}")
            except (TypeError, ValueError):
                 print(f"  {metric_name}: {value} (Could not format as percentage)")
else:
    print("No metrics were successfully calculated.")

