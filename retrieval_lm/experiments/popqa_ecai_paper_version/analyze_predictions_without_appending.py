import pandas as pd
import json

def load_json_lines(file_path):
    data = []
    with open(file_path, "r") as f:
        for line in f:
            if line.strip():  # Skip empty lines
                data.append(json.loads(line))
    return data

def calculate_metrics(csv_file_path, json_file_path, correctness_column_name="correctness"):
    """
    Calculates the five metrics directly without appending correctness to the CSV.
    
    Args:
        csv_file_path (str): Path to the CSV file with entity data.
        json_file_path (str): Path to the JSON file with predictions.
        correctness_column_name (str): Temporary name for correctness column in memory.

    Returns:
        dict: A dictionary containing the five calculated metrics.
    """
    # Load the CSV file
    df = pd.read_csv(csv_file_path)
    csv_length = len(df)
    print(f"\nAnalyzing files:")
    print(f"CSV file: {csv_file_path}")
    print(f"JSON file: {json_file_path}")
    print(f"Number of rows in CSV: {csv_length}")

    # Load the JSON file
    with open(json_file_path, 'r') as f:
        predictions_data = json.load(f)
    predictions = predictions_data["preds"]
    print(f"Number of predictions: {len(predictions)}")

    # Ensure prediction list length equals number of rows in CSV
    if len(predictions) != csv_length:
        raise ValueError(f"Mismatch in lengths: CSV has {csv_length} rows but found {len(predictions)} predictions")
    
    # Add correctness in memory without modifying the CSV
    # Add correctness in memory without modifying the CSV
    df[correctness_column_name] = [
        any(answer.lower() in predictions[i].lower() for answer in eval(row["possible_answers"]))
        for i, row in df.iterrows()
    ]

    # Calculate metrics
    metrics = {}

    # Overall correctness
    overall_correct = df[correctness_column_name].mean()
    metrics["Overall Correctness"] = overall_correct

    # # Completely unambiguous questions
    # df_completely_unambiguous = df[df["entity_name_occurrences"] <= 1]
    # unambiguous_correct = df_completely_unambiguous[correctness_column_name].mean()
    # metrics["Unambiguous Correctness"] = unambiguous_correct

    # # Name ambiguous questions
    # df_name_ambiguous = df[df["entity_name_occurrences"] > 1]
    # name_ambiguous_correct = df_name_ambiguous[correctness_column_name].mean()
    # metrics["Name Ambiguous Correctness"] = name_ambiguous_correct

    # Relevant entity unambiguous questions
    df_relevant_entity_unambiguous = df[df["relevant_entity_count"] <= 1]
    relevant_unambiguous_correct = df_relevant_entity_unambiguous[correctness_column_name].mean()
    metrics["Relevant Entity Unambiguous Correctness"] = relevant_unambiguous_correct

    # Relevant entity ambiguous questions
    df_relevant_entity_ambiguous = df[df["relevant_entity_count"] > 1]
    relevant_ambiguous_correct = df_relevant_entity_ambiguous[correctness_column_name].mean()
    metrics["Relevant Entity Ambiguous Correctness"] = relevant_ambiguous_correct

    return metrics


def batch_process_metrics(csv_file_path, prediction_files):
    """
    Processes multiple prediction files against a single CSV file and computes metrics for each.

    Args:
        csv_file_path (str): Path to the CSV file with entity data.
        prediction_files (list): List of paths to JSON prediction files.

    Returns:
        dict: A dictionary where each key is a JSON file path, and the value is a dictionary of metrics.
    """
    results = {}

    for json_file in prediction_files:
        # Call the calculate_metrics function for each prediction file
        metrics = calculate_metrics(csv_file_path, json_file)

        # Store the metrics in the results dictionary with the file name as the key
        results[json_file] = metrics

    return results

csv_file_path = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_original_ecai.csv"

prediction_files = [
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_original_gpt4o_no_retrieval_results.jsonl"
    # # Llama2-7B with different ndocs (original)
    # "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve5_contrievermsm_llama2_7b_results.jsonl",
    # "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve10_contrievermsm_llama2_7b_results.jsonl",
    # "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve15_contrievermsm_llama2_7b_results.jsonl",
    # "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve20_contrievermsm_llama2_7b_results.jsonl",
    
    # # Llama2-13B with different ndocs (original)
    # "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve5_contrievermsm_llama2_13b_results.jsonl",
    # "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve10_contrievermsm_llama2_13b_results.jsonl",
    # "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve15_contrievermsm_llama2_13b_results.jsonl",
    # "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_original_retrieve20_contrievermsm_llama2_13b_results.jsonl"
]

# prediction_files = [
#     # Llama2-7B with different ndocs (disambiguated)
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_disambiguated_retrieve5_contrievermsm_llama2_7b_results.jsonl",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_disambiguated_retrieve10_contrievermsm_llama2_7b_results.jsonl",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_disambiguated_retrieve15_contrievermsm_llama2_7b_results.jsonl",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_disambiguated_retrieve20_contrievermsm_llama2_7b_results.jsonl",
    
#     # Llama2-13B with different ndocs (disambiguated)
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_disambiguated_retrieve5_contrievermsm_llama2_13b_results.jsonl",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_disambiguated_retrieve10_contrievermsm_llama2_13b_results.jsonl",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_disambiguated_retrieve15_contrievermsm_llama2_13b_results.jsonl",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/popqa_longtail_disambiguated_retrieve20_contrievermsm_llama2_13b_results.jsonl"
# ]


prediction_files = [
    # Llama2-13B with different ndocs
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/pqa_disamb_gold_retrieve1_llama2_13b_results.jsonl",
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/pqa_disamb_gold_retrieve6_llama2_13b_results.jsonl",
    
    # Llama2-13B-Instruct with different ndocs
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/pqa_disamb_gold_retrieve1_llama2_13b_instruct_results.jsonl",
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/pqa_disamb_gold_retrieve6_llama2_13b_instruct_results.jsonl",
    
    # SelfRAG-13B with different ndocs
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/pqa_disamb_gold_retrieve1_selfrag13b_results.jsonl",
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/pqa_disamb_gold_retrieve6_selfrag13b_results.jsonl",
    
    # Qwen2.5-7B-Instruct with different ndocs
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/pqa_disamb_gold_retrieve1_qwen2_7b_instruct_results.jsonl",
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/popqa_ecai_paper_version/pqa_disamb_gold_retrieve6_qwen2_7b_instruct_results.jsonl",
    
   ]
# Call batch_process_metrics to compute metrics for all prediction files
results = batch_process_metrics(csv_file_path, prediction_files)

# Print results
for json_file, metrics in results.items():
    print(f"Metrics for {json_file}:")
    for metric_name, value in metrics.items():
        print(f"  {metric_name}: {value:.1%}")

