import pandas as pd
import json
import ast # Keep ast in case needed for future modifications, though not used currently for answers

def load_json_lines(file_path):
    """Loads data from a JSON Lines file."""
    data = []
    with open(file_path, "r") as f:
        for line in f:
            if line.strip():  # Skip empty lines
                try:
                    data.append(json.loads(line))
                except json.JSONDecodeError as e:
                    print(f"Skipping invalid JSON line in {file_path}: {line.strip()} - Error: {e}")
    return data

def calculate_metrics(csv_file_path, json_file_path, prediction_key_name="preds", correctness_column_name="correctness"):
    """
    Calculates metrics by comparing predictions from a JSON file against a CSV file.

    Args:
        csv_file_path (str): Path to the CSV file with entity data and answers.
        json_file_path (str): Path to the JSON file (standard JSON or JSON Lines) with predictions.
        prediction_key_name (str): The key in each JSON object that holds the prediction string. Defaults to "preds".
        correctness_column_name (str): Temporary name for correctness column in memory.

    Returns:
        dict: A dictionary containing the calculated metrics. Returns None if file processing fails.
    """
    try:
        # Load the CSV file
        df = pd.read_csv(csv_file_path)
        csv_length = len(df)
        print(f"\nAnalyzing files:")
        print(f"CSV file: {csv_file_path}")
        print(f"JSON file: {json_file_path}")
        print(f"Expected prediction key in JSON: '{prediction_key_name}'") # Added info
        print(f"Number of rows in CSV: {csv_length}")

        # Load the JSON file - Try standard JSON first, then JSON Lines
        predictions_data = []
        try:
            with open(json_file_path, 'r') as f:
                content = f.read()
                # Check if it looks like JSON lines or a single JSON object (list/dict)
                if content.strip().startswith('[') and content.strip().endswith(']'):
                     # Assume it's a single JSON list object
                     predictions_data = json.loads(content)
                elif content.strip().startswith('{') and content.strip().endswith('}'):
                     # Assume it's a single JSON dictionary object
                     loaded_json = json.loads(content)
                     # Check if it contains the specified prediction key directly (less common for batch results)
                     if prediction_key_name in loaded_json and isinstance(loaded_json[prediction_key_name], list):
                         print(f"Warning: JSON file {json_file_path} is a dictionary. Attempting to process as JSON Lines instead, as individual predictions are expected.")
                         predictions_data = load_json_lines(json_file_path)
                     else:
                         print(f"Warning: JSON file {json_file_path} is a dictionary without a '{prediction_key_name}' list. Attempting to process as JSON Lines.")
                         predictions_data = load_json_lines(json_file_path)
                else:
                    # Assume JSON Lines format if it doesn't look like a standard list/dict
                    predictions_data = load_json_lines(json_file_path)

        except json.JSONDecodeError as e:
            print(f"Failed to decode {json_file_path} as standard JSON: {e}. Trying JSON Lines format.")
            predictions_data = load_json_lines(json_file_path)
        except Exception as e:
            print(f"An unexpected error occurred loading {json_file_path}: {e}")
            return None # Cannot proceed if JSON loading fails

        # --- MODIFIED: Extract predictions using the specified key ---
        if not isinstance(predictions_data, list):
             print(f"Error: Expected predictions_data from {json_file_path} to be a list, but got {type(predictions_data)}")
             return None

        # Check if all items are dictionaries AND have the specified prediction key
        if not all(isinstance(item, dict) and prediction_key_name in item for item in predictions_data):
             print(f"Error: Not all items in {json_file_path} are dictionaries with the key '{prediction_key_name}'.")
             # Print first few problematic items for debugging
             for i, item in enumerate(predictions_data[:5]):
                 if not isinstance(item, dict):
                     print(f"  Item {i} is not a dict: {item}")
                 elif prediction_key_name not in item:
                     print(f"  Item {i} is missing '{prediction_key_name}' key: {item}")
             return None

        # Extract the prediction value using the specified key
        try:
            predictions = [item[prediction_key_name] for item in predictions_data]
        except KeyError:
             # This should be caught by the check above, but as a safeguard
             print(f"Error: The key '{prediction_key_name}' was not found in at least one JSON object during extraction.")
             return None
        except Exception as e:
             print(f"Error extracting predictions using key '{prediction_key_name}': {e}")
             return None
        # --- End of MODIFICATION ---

        print(f"Number of predictions loaded: {len(predictions)}")

        # Ensure prediction list length equals number of rows in CSV
        if len(predictions) != csv_length:
            print(f"Error: Mismatch in lengths: CSV has {csv_length} rows but found {len(predictions)} predictions in {json_file_path}")
            return None # Stop processing this file if lengths don't match

        # Add correctness in memory without modifying the CSV
        correctness_list = []
        for i, row in df.iterrows():
            try:
                # Get the ground truth answer
                answer = str(row["answer"]) # Ensure answer is string
                if pd.isna(row["answer"]): # Check original value for NaN before converting to string
                    print(f"Warning: Answer in row {i} is NaN or missing.")
                    correctness_list.append(False)
                    continue

                # Ensure prediction is a string before lowercasing
                prediction_text = predictions[i]
                if prediction_text is None: # Handle None predictions
                     print(f"Warning: Prediction at index {i} is None. Treating as incorrect.")
                     correctness_list.append(False)
                     continue
                if not isinstance(prediction_text, str):
                     # Attempt to convert to string if possible, otherwise mark incorrect
                     try:
                         prediction_text = str(prediction_text)
                         print(f"Warning: Prediction at index {i} was not a string ({type(predictions[i])}), converted to: '{prediction_text}'.")
                     except Exception:
                         print(f"Warning: Prediction at index {i} is not a string and could not be converted: {predictions[i]}. Treating as incorrect.")
                         correctness_list.append(False)
                         continue


                # Check if the answer string is present in the prediction string (case-insensitive)
                is_correct = answer.lower() in prediction_text.lower()
                correctness_list.append(is_correct)

            except KeyError as e:
                print(f"Error: Missing expected column 'answer' in CSV row {i}: {e}")
                correctness_list.append(False)
            except IndexError:
                print(f"Error: Trying to access prediction index {i}, but only {len(predictions)} predictions exist.")
                correctness_list.append(False)
            except Exception as e:
                print(f"An unexpected error occurred processing row {i}: {e}")
                correctness_list.append(False)


        # Check if correctness_list length matches df length before assigning
        if len(correctness_list) == len(df):
            # Create a temporary DataFrame or Series for calculations to avoid modifying original df
            temp_df = df.copy()
            temp_df[correctness_column_name] = correctness_list
        else:
            print(f"Error: Length mismatch between DataFrame ({len(df)}) and calculated correctness ({len(correctness_list)}). Cannot calculate metrics for {json_file_path}.")
            return None


        # Calculate metrics using the temporary DataFrame
        metrics = {}

        # Overall correctness
        if not temp_df[correctness_column_name].empty:
            metrics["Overall Correctness"] = temp_df[correctness_column_name].mean()
        else:
            metrics["Overall Correctness"] = 0.0 # Or handle as NaN or error

        # Check if 'relevant_entity_count' column exists before using it
        if "relevant_entity_count" in temp_df.columns:
            # Relevant entity unambiguous questions
            df_relevant_entity_unambiguous = temp_df[temp_df["relevant_entity_count"] <= 1]
            if not df_relevant_entity_unambiguous.empty and correctness_column_name in df_relevant_entity_unambiguous:
                 metrics["Relevant Entity Unambiguous Correctness"] = df_relevant_entity_unambiguous[correctness_column_name].mean()
            else:
                 metrics["Relevant Entity Unambiguous Correctness"] = 0.0 # Or handle appropriately if no such rows exist

            # Relevant entity ambiguous questions
            df_relevant_entity_ambiguous = temp_df[temp_df["relevant_entity_count"] > 1]
            if not df_relevant_entity_ambiguous.empty and correctness_column_name in df_relevant_entity_ambiguous:
                metrics["Relevant Entity Ambiguous Correctness"] = df_relevant_entity_ambiguous[correctness_column_name].mean()
            else:
                metrics["Relevant Entity Ambiguous Correctness"] = 0.0 # Or handle appropriately
        else:
            print(f"Warning: Column 'relevant_entity_count' not found in {csv_file_path}. Skipping related metrics.")
            metrics["Relevant Entity Unambiguous Correctness"] = "N/A (Missing Column)"
            metrics["Relevant Entity Ambiguous Correctness"] = "N/A (Missing Column)"


        # Add checks for other columns if you re-enable those metrics
        # Example:
        # if "entity_name_occurrences" in temp_df.columns:
        #    # ... calculate unambiguous/name ambiguous correctness ...
        # else:
        #    print(f"Warning: Column 'entity_name_occurrences' not found...")
        #    metrics["Unambiguous Correctness"] = "N/A (Missing Column)"
        #    metrics["Name Ambiguous Correctness"] = "N/A (Missing Column)"


        return metrics

    except FileNotFoundError as e:
        print(f"Error: File not found - {e}")
        return None
    except pd.errors.EmptyDataError:
        print(f"Error: CSV file {csv_file_path} is empty.")
        return None
    except KeyError as e:
        # This might catch errors if essential columns like 'answer' are missing from the CSV itself
        print(f"Error: Missing expected column in CSV or incorrect structure - {e}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred in calculate_metrics for {json_file_path}: {e}")
        import traceback
        traceback.print_exc() # Print detailed traceback for unexpected errors
        return None


def batch_process_metrics(csv_file_path, prediction_files, prediction_key_name="preds"):
    """
    Processes multiple prediction files against a single CSV file and computes metrics for each.

    Args:
        csv_file_path (str): Path to the CSV file with entity data.
        prediction_files (list): List of paths to JSON prediction files.
        prediction_key_name (str): The key in each JSON object that holds the prediction string.

    Returns:
        dict: A dictionary where each key is a JSON file path, and the value is a dictionary of metrics.
              Files that cause errors during processing will be skipped.
    """
    results = {}

    for json_file in prediction_files:
        # Call the calculate_metrics function for each prediction file, passing the key name
        metrics = calculate_metrics(csv_file_path, json_file, prediction_key_name=prediction_key_name)

        # Store the metrics ONLY if calculation was successful
        if metrics is not None:
            results[json_file] = metrics
        else:
            print(f"Skipping metrics for {json_file} due to errors during calculation.")


    return results

# --- Main execution part ---

# --- !!! IMPORTANT: SET THIS VARIABLE !!! ---
# Inspect your JSON file(s) and find the key that holds the actual prediction string.
# Replace "preds" with the correct key name. Examples: "prediction", "output", "result", etc.
PREDICTION_KEY = "preds" # <-- CHANGE THIS IF YOUR KEY IS DIFFERENT

# --- File Paths ---
# Set the path to your CSV file
csv_file_path = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_long_tail_disambiguated_filtered.csv"
# csv_file_path = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_long_tail.csv"



# List of prediction files to process
prediction_files = [
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_disambig_gpt4o_no_retrieval_results.jsonl"
    #'/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_long_tail_disambiguated_filtered_retrieve20_contrievermsm.json'
    # Add more files here if needed
]


# --- Run the analysis ---
print(f"Starting batch processing...")
print(f"Using prediction key: '{PREDICTION_KEY}'")

# Call batch_process_metrics, passing the configured prediction key
results = batch_process_metrics(csv_file_path, prediction_files, prediction_key_name=PREDICTION_KEY)

# --- Print results ---
print("\n--- Final Results ---")
if results:
    for json_file, metrics in results.items():
        print(f"\nMetrics for {json_file}:")
        for metric_name, value in metrics.items():
             # Format as percentage, handle potential None, non-numeric, or "N/A" values gracefully
            if isinstance(value, (int, float)):
                try:
                    print(f"  {metric_name}: {value:.1%}")
                except (TypeError, ValueError):
                     print(f"  {metric_name}: {value} (Could not format as percentage)")
            else:
                 print(f"  {metric_name}: {value}") # Print non-numeric values directly
else:
    print("No metrics were successfully calculated.")

print("\nProcessing complete.")
