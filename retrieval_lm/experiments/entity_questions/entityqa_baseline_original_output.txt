==========================================
SLURM_JOB_ID = 79316
SLURM_NODELIST = gpunode02
==========================================
INFO 04-20 15:08:45 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-7b-hf', tokenizer='meta-llama/Llama-2-7b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 04-20 15:09:17 llm_engine.py:223] # GPU blocks: 2806, # CPU blocks: 512
INFO 04-20 15:09:20 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-20 15:09:26 model_runner.py:437] Graph capturing finished in 6 secs.
overall result: 0.0013879250520471894
INFO 04-20 15:26:05 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-7b-hf', tokenizer='meta-llama/Llama-2-7b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 04-20 15:26:11 llm_engine.py:223] # GPU blocks: 2806, # CPU blocks: 512
INFO 04-20 15:26:14 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-20 15:26:19 model_runner.py:437] Graph capturing finished in 5 secs.
overall result: 0.0013879250520471894
INFO 04-20 15:47:27 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-7b-hf', tokenizer='meta-llama/Llama-2-7b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 04-20 15:47:33 llm_engine.py:223] # GPU blocks: 2806, # CPU blocks: 512
INFO 04-20 15:47:36 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-20 15:47:42 model_runner.py:437] Graph capturing finished in 6 secs.
WARNING 04-20 15:51:47 scheduler.py:147] Input prompt (4605 tokens) is too long and exceeds limit of 4096
WARNING 04-20 15:53:25 scheduler.py:147] Input prompt (4134 tokens) is too long and exceeds limit of 4096
WARNING 04-20 15:53:54 scheduler.py:147] Input prompt (4847 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:09:42 scheduler.py:147] Input prompt (4193 tokens) is too long and exceeds limit of 4096
overall result: 0.00034698126301179735
INFO 04-20 16:12:54 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-7b-hf', tokenizer='meta-llama/Llama-2-7b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 04-20 16:13:00 llm_engine.py:223] # GPU blocks: 2806, # CPU blocks: 512
INFO 04-20 16:13:03 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-20 16:13:10 model_runner.py:437] Graph capturing finished in 6 secs.
WARNING 04-20 16:15:27 scheduler.py:147] Input prompt (4487 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:15:30 scheduler.py:147] Input prompt (4276 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:15:40 scheduler.py:147] Input prompt (4623 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:15:42 scheduler.py:147] Input prompt (4191 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:15:47 scheduler.py:147] Input prompt (5053 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:15:49 scheduler.py:147] Input prompt (4597 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:15:50 scheduler.py:147] Input prompt (4259 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:15:58 scheduler.py:147] Input prompt (4389 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:16:05 scheduler.py:147] Input prompt (4640 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:16:08 scheduler.py:147] Input prompt (4243 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:16:10 scheduler.py:147] Input prompt (4527 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:17:03 scheduler.py:147] Input prompt (4283 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:17:04 scheduler.py:147] Input prompt (4118 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:17:07 scheduler.py:147] Input prompt (4221 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:17:16 scheduler.py:147] Input prompt (4530 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:17:40 scheduler.py:147] Input prompt (4650 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:17:50 scheduler.py:147] Input prompt (6198 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:17:50 scheduler.py:147] Input prompt (4367 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:17:52 scheduler.py:147] Input prompt (4837 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:18:07 scheduler.py:147] Input prompt (4308 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:18:10 scheduler.py:147] Input prompt (4177 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:18:21 scheduler.py:147] Input prompt (4746 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:18:38 scheduler.py:147] Input prompt (4465 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:18:44 scheduler.py:147] Input prompt (4295 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:19:40 scheduler.py:147] Input prompt (5523 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:19:58 scheduler.py:147] Input prompt (4374 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:20:13 scheduler.py:147] Input prompt (4417 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:20:13 scheduler.py:147] Input prompt (5879 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:20:28 scheduler.py:147] Input prompt (4206 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:20:34 scheduler.py:147] Input prompt (4289 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:21:06 scheduler.py:147] Input prompt (4395 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:21:09 scheduler.py:147] Input prompt (4115 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:21:56 scheduler.py:147] Input prompt (4359 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:22:07 scheduler.py:147] Input prompt (4239 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:22:24 scheduler.py:147] Input prompt (4382 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:22:54 scheduler.py:147] Input prompt (4323 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:24:51 scheduler.py:147] Input prompt (4510 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:24:51 scheduler.py:147] Input prompt (4313 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:24:54 scheduler.py:147] Input prompt (4131 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:25:02 scheduler.py:147] Input prompt (4166 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:25:10 scheduler.py:147] Input prompt (4481 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:25:12 scheduler.py:147] Input prompt (4173 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:25:33 scheduler.py:147] Input prompt (4290 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:27:56 scheduler.py:147] Input prompt (4761 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:28:02 scheduler.py:147] Input prompt (4157 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:28:38 scheduler.py:147] Input prompt (4118 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:28:42 scheduler.py:147] Input prompt (4444 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:28:42 scheduler.py:147] Input prompt (4207 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:28:47 scheduler.py:147] Input prompt (4369 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:28:48 scheduler.py:147] Input prompt (4493 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:28:50 scheduler.py:147] Input prompt (4106 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:28:53 scheduler.py:147] Input prompt (4171 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:28:53 scheduler.py:147] Input prompt (4412 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:29:03 scheduler.py:147] Input prompt (4186 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:29:06 scheduler.py:147] Input prompt (4958 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:29:08 scheduler.py:147] Input prompt (5414 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:29:30 scheduler.py:147] Input prompt (4381 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:32:45 scheduler.py:147] Input prompt (4275 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:33:54 scheduler.py:147] Input prompt (4382 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:34:16 scheduler.py:147] Input prompt (4321 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:34:38 scheduler.py:147] Input prompt (4266 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:35:07 scheduler.py:147] Input prompt (4305 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:35:51 scheduler.py:147] Input prompt (4192 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:36:34 scheduler.py:147] Input prompt (4719 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:36:45 scheduler.py:147] Input prompt (4181 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:37:52 scheduler.py:147] Input prompt (4338 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:37:57 scheduler.py:147] Input prompt (4168 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:38:07 scheduler.py:147] Input prompt (4167 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:38:20 scheduler.py:147] Input prompt (4298 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:38:20 scheduler.py:147] Input prompt (4466 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:38:22 scheduler.py:147] Input prompt (5155 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:38:22 scheduler.py:147] Input prompt (4136 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:38:38 scheduler.py:147] Input prompt (4248 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:39:21 scheduler.py:147] Input prompt (4117 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:39:28 scheduler.py:147] Input prompt (4509 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:39:41 scheduler.py:147] Input prompt (4109 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:39:56 scheduler.py:147] Input prompt (4339 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:39:59 scheduler.py:147] Input prompt (4164 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:39:59 scheduler.py:147] Input prompt (4448 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:40:02 scheduler.py:147] Input prompt (4305 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:40:05 scheduler.py:147] Input prompt (4716 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:40:05 scheduler.py:147] Input prompt (4273 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:40:07 scheduler.py:147] Input prompt (4117 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:40:33 scheduler.py:147] Input prompt (4143 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:41:01 scheduler.py:147] Input prompt (4225 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:41:06 scheduler.py:147] Input prompt (4851 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:41:20 scheduler.py:147] Input prompt (4884 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:41:25 scheduler.py:147] Input prompt (4193 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:41:25 scheduler.py:147] Input prompt (4512 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:41:36 scheduler.py:147] Input prompt (4143 tokens) is too long and exceeds limit of 4096
WARNING 04-20 16:41:45 scheduler.py:147] Input prompt (4172 tokens) is too long and exceeds limit of 4096
overall result: 0.00034698126301179735
INFO 04-20 16:41:56 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-13b-hf', tokenizer='meta-llama/Llama-2-13b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 04-20 16:43:14 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 04-20 16:43:18 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-20 16:43:25 model_runner.py:437] Graph capturing finished in 7 secs.
overall result: 0.00034698126301179735
INFO 04-20 17:12:20 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-13b-hf', tokenizer='meta-llama/Llama-2-13b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 04-20 17:12:31 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 04-20 17:12:35 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-20 17:12:42 model_runner.py:437] Graph capturing finished in 7 secs.
overall result: 0.0
INFO 04-20 17:48:25 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-13b-hf', tokenizer='meta-llama/Llama-2-13b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 04-20 17:49:23 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 04-20 17:49:27 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-20 17:49:34 model_runner.py:437] Graph capturing finished in 7 secs.
WARNING 04-20 17:56:42 scheduler.py:147] Input prompt (4605 tokens) is too long and exceeds limit of 4096
WARNING 04-20 17:59:25 scheduler.py:147] Input prompt (4134 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:00:14 scheduler.py:147] Input prompt (4847 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:27:17 scheduler.py:147] Input prompt (4193 tokens) is too long and exceeds limit of 4096
overall result: 0.0006939625260235947
INFO 04-20 18:32:44 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-13b-hf', tokenizer='meta-llama/Llama-2-13b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 04-20 18:32:55 llm_engine.py:223] # GPU blocks: 827, # CPU blocks: 327
INFO 04-20 18:32:59 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-20 18:33:06 model_runner.py:437] Graph capturing finished in 7 secs.
WARNING 04-20 18:37:21 scheduler.py:147] Input prompt (4487 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:37:27 scheduler.py:147] Input prompt (4276 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:37:49 scheduler.py:147] Input prompt (4623 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:37:50 scheduler.py:147] Input prompt (4191 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:38:02 scheduler.py:147] Input prompt (5053 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:38:05 scheduler.py:147] Input prompt (4597 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:38:07 scheduler.py:147] Input prompt (4259 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:38:21 scheduler.py:147] Input prompt (4389 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:38:33 scheduler.py:147] Input prompt (4640 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:38:42 scheduler.py:147] Input prompt (4243 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:38:45 scheduler.py:147] Input prompt (4527 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:40:29 scheduler.py:147] Input prompt (4283 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:40:30 scheduler.py:147] Input prompt (4118 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:40:35 scheduler.py:147] Input prompt (4221 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:40:53 scheduler.py:147] Input prompt (4530 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:41:46 scheduler.py:147] Input prompt (4650 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:42:02 scheduler.py:147] Input prompt (6198 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:42:03 scheduler.py:147] Input prompt (4367 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:42:07 scheduler.py:147] Input prompt (4837 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:42:31 scheduler.py:147] Input prompt (4308 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:42:35 scheduler.py:147] Input prompt (4177 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:42:55 scheduler.py:147] Input prompt (4746 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:43:28 scheduler.py:147] Input prompt (4465 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:43:38 scheduler.py:147] Input prompt (4295 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:45:09 scheduler.py:147] Input prompt (5523 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:45:36 scheduler.py:147] Input prompt (4374 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:46:01 scheduler.py:147] Input prompt (4417 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:46:01 scheduler.py:147] Input prompt (5879 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:46:25 scheduler.py:147] Input prompt (4206 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:46:35 scheduler.py:147] Input prompt (4289 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:47:27 scheduler.py:147] Input prompt (4395 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:47:29 scheduler.py:147] Input prompt (4115 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:48:45 scheduler.py:147] Input prompt (4359 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:49:04 scheduler.py:147] Input prompt (4239 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:49:32 scheduler.py:147] Input prompt (4382 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:50:19 scheduler.py:147] Input prompt (4323 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:53:39 scheduler.py:147] Input prompt (4510 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:53:40 scheduler.py:147] Input prompt (4313 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:53:44 scheduler.py:147] Input prompt (4131 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:53:59 scheduler.py:147] Input prompt (4166 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:54:14 scheduler.py:147] Input prompt (4481 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:54:19 scheduler.py:147] Input prompt (4173 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:54:55 scheduler.py:147] Input prompt (4290 tokens) is too long and exceeds limit of 4096
WARNING 04-20 18:59:51 scheduler.py:147] Input prompt (4761 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:00:04 scheduler.py:147] Input prompt (4157 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:01:21 scheduler.py:147] Input prompt (4118 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:01:26 scheduler.py:147] Input prompt (4444 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:01:27 scheduler.py:147] Input prompt (4207 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:01:35 scheduler.py:147] Input prompt (4369 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:01:36 scheduler.py:147] Input prompt (4493 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:01:39 scheduler.py:147] Input prompt (4106 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:01:44 scheduler.py:147] Input prompt (4171 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:01:44 scheduler.py:147] Input prompt (4412 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:02:02 scheduler.py:147] Input prompt (4186 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:02:07 scheduler.py:147] Input prompt (4958 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:02:10 scheduler.py:147] Input prompt (5414 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:02:58 scheduler.py:147] Input prompt (4381 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:08:26 scheduler.py:147] Input prompt (4275 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:10:40 scheduler.py:147] Input prompt (4382 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:11:24 scheduler.py:147] Input prompt (4321 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:12:04 scheduler.py:147] Input prompt (4266 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:12:59 scheduler.py:147] Input prompt (4305 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:14:25 scheduler.py:147] Input prompt (4192 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:15:43 scheduler.py:147] Input prompt (4719 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:16:02 scheduler.py:147] Input prompt (4181 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:18:16 scheduler.py:147] Input prompt (4338 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:18:26 scheduler.py:147] Input prompt (4168 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:18:41 scheduler.py:147] Input prompt (4167 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:19:07 scheduler.py:147] Input prompt (4298 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:19:07 scheduler.py:147] Input prompt (4466 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:19:11 scheduler.py:147] Input prompt (5155 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:19:12 scheduler.py:147] Input prompt (4136 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:19:43 scheduler.py:147] Input prompt (4248 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:21:17 scheduler.py:147] Input prompt (4117 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:21:31 scheduler.py:147] Input prompt (4509 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:21:57 scheduler.py:147] Input prompt (4109 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:22:25 scheduler.py:147] Input prompt (4339 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:22:30 scheduler.py:147] Input prompt (4164 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:22:30 scheduler.py:147] Input prompt (4448 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:22:35 scheduler.py:147] Input prompt (4305 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:22:42 scheduler.py:147] Input prompt (4716 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:22:42 scheduler.py:147] Input prompt (4273 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:22:46 scheduler.py:147] Input prompt (4117 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:23:38 scheduler.py:147] Input prompt (4143 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:24:29 scheduler.py:147] Input prompt (4225 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:24:37 scheduler.py:147] Input prompt (4851 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:25:00 scheduler.py:147] Input prompt (4884 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:25:09 scheduler.py:147] Input prompt (4193 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:25:09 scheduler.py:147] Input prompt (4512 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:25:27 scheduler.py:147] Input prompt (4143 tokens) is too long and exceeds limit of 4096
WARNING 04-20 19:25:45 scheduler.py:147] Input prompt (4172 tokens) is too long and exceeds limit of 4096
overall result: 0.0006939625260235947
