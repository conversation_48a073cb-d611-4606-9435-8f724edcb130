import pandas as pd

def calculate_retrieval_metrics(csv_file):
    """Calculate retrieval accuracy metrics for different groups and top-k values."""
    # Load the CSV file
    df = pd.read_csv(csv_file)
    
    # Define the top-k values we want to analyze
    top_k_values = [5, 10, 15, 20]
    
    # Store results for each top-k
    all_metrics = {}
    
    for k in top_k_values:
        column_name = f'disambig_contrievermsm_2020_top{k}_contains_answer'
        metrics = {}
        
        # Overall retrieval accuracy
        metrics[f"Overall Retrieval Accuracy (Top-{k})"] = df[column_name].mean()
        
        # Relevant entity unambiguous questions
        df_unambiguous = df[df["relevant_entity_count"] <= 1]
        if not df_unambiguous.empty:
            metrics[f"Unambiguous Retrieval Accuracy (Top-{k})"] = df_unambiguous[column_name].mean()
        else:
            metrics[f"Unambiguous Retrieval Accuracy (Top-{k})"] = 0.0
            
        # Relevant entity ambiguous questions
        df_ambiguous = df[df["relevant_entity_count"] > 1]
        if not df_ambiguous.empty:
            metrics[f"Ambiguous Retrieval Accuracy (Top-{k})"] = df_ambiguous[column_name].mean()
        else:
            metrics[f"Ambiguous Retrieval Accuracy (Top-{k})"] = 0.0
            
        all_metrics.update(metrics)
    
    return all_metrics

if __name__ == "__main__":
    csv_file = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_long_tail.csv"
    
    # Calculate metrics
    metrics = calculate_retrieval_metrics(csv_file)
    
    # Print results
    print("\n=== Retrieval Accuracy Metrics ===")
    for metric_name, value in metrics.items():
        print(f"{metric_name}: {value:.1%}")