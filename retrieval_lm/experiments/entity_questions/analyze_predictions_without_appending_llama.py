import pandas as pd
import json

def load_json_lines(file_path):
    data = []
    with open(file_path, "r") as f:
        for line in f:
            if line.strip():  # Skip empty lines
                try:
                    data.append(json.loads(line))
                except json.JSONDecodeError as e:
                    print(f"Warning: Skipping line due to JSONDecodeError: {e} in file {file_path}")
                    print(f"Problematic line content: {line.strip()}")
    return data

def calculate_metrics(csv_file_path, json_file_path, correctness_column_name="correctness"):
    """
    Calculates the three metrics directly without appending correctness to the CSV.

    Args:
        csv_file_path (str): Path to the CSV file with entity data.
        json_file_path (str): Path to the JSONL file with predictions.
        correctness_column_name (str): Temporary name for correctness column in memory.

    Returns:
        dict: A dictionary containing the three calculated metrics.
    """
    # Load the CSV file
    df = pd.read_csv(csv_file_path)
    csv_length = len(df)
    print(f"\nAnalyzing files:")
    print(f"CSV file: {csv_file_path}")
    print(f"JSONL file: {json_file_path}") # Changed label to JSONL
    print(f"Number of rows in CSV: {csv_length}")

    # Load the JSON Lines file using the correct function
    predictions_data = load_json_lines(json_file_path)

    # Extract the actual prediction string (assuming it's in the 'output' key)
    # Use .get() for safety in case 'output' key is missing in some lines
    predictions = [item.get("output", "") for item in predictions_data]
    print(f"Number of predictions loaded: {len(predictions)}")

    # Ensure prediction list length equals number of rows in CSV
    if len(predictions) != csv_length:
        # Added more informative error message
        raise ValueError(
            f"Mismatch in lengths: CSV has {csv_length} rows but found "
            f"{len(predictions)} predictions in {json_file_path}. "
            "Check if the JSONL file corresponds correctly to the CSV "
            "or if there were errors loading lines."
        )

    # Add correctness in memory without modifying the CSV
    # Ensure both answer and prediction are strings and lowercase for comparison
    df[correctness_column_name] = [
        str(row["answer"]).lower() in str(predictions[i]).lower()
        for i, row in df.iterrows()
    ]

    # Calculate metrics
    metrics = {}

    # Overall correctness
    overall_correct = df[correctness_column_name].mean()
    metrics["Overall Correctness"] = overall_correct

    # Relevant entity unambiguous questions
    # Handle potential NaN values in 'relevant_entity_count' if they exist
    unambiguous_mask = df["relevant_entity_count"].fillna(0) <= 1
    df_relevant_entity_unambiguous = df[unambiguous_mask]
    if not df_relevant_entity_unambiguous.empty:
        relevant_unambiguous_correct = df_relevant_entity_unambiguous[correctness_column_name].mean()
    else:
        relevant_unambiguous_correct = float('nan') # Or 0.0, depending on preference
        print(f"Warning: No unambiguous questions found (relevant_entity_count <= 1) for {json_file_path}")
    metrics["Relevant Entity Unambiguous Correctness"] = relevant_unambiguous_correct

    # Relevant entity ambiguous questions
    ambiguous_mask = df["relevant_entity_count"] > 1
    df_relevant_entity_ambiguous = df[ambiguous_mask]
    if not df_relevant_entity_ambiguous.empty:
        relevant_ambiguous_correct = df_relevant_entity_ambiguous[correctness_column_name].mean()
    else:
        relevant_ambiguous_correct = float('nan') # Or 0.0
        print(f"Warning: No ambiguous questions found (relevant_entity_count > 1) for {json_file_path}")
    metrics["Relevant Entity Ambiguous Correctness"] = relevant_ambiguous_correct

    return metrics

# --- Keep the rest of your script (batch_process_metrics, file paths, etc.) the same ---

def batch_process_metrics(csv_file_path, prediction_files):
    """
    Processes multiple prediction files against a single CSV file and computes metrics for each.

    Args:
        csv_file_path (str): Path to the CSV file with entity data.
        prediction_files (list): List of paths to JSONL prediction files.

    Returns:
        dict: A dictionary where each key is a JSONL file path, and the value is a dictionary of metrics.
    """
    results = {}

    for json_file in prediction_files:
        try:
            # Call the calculate_metrics function for each prediction file
            metrics = calculate_metrics(csv_file_path, json_file)

            # Store the metrics in the results dictionary with the file name as the key
            results[json_file] = metrics
        except Exception as e:
            print(f"\n!!!! Failed to process file: {json_file} !!!!")
            print(f"Error: {e}")
            # Optionally store the error or skip the file
            results[json_file] = {"error": str(e)}


    return results

csv_file_path = "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_long_tail.csv"

# prediction_files = [
#     # Selfrag-7B with different ndocs (original)
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve10_contrievermsm_selfrag7b_results.jsonl",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve15_contrievermsm_selfrag7b_results.jsonl",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm_selfrag7b_results.jsonl",

#     # Selfrag-13B with different ndocs (original)
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve10_contrievermsm_selfrag13b_results.jsonl",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve15_contrievermsm_selfrag13b_results.jsonl",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm_selfrag13b_results.jsonl"]

# prediction_files = [

#     # Selfrag-7B with different ndocs
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/llama_runs_results/entity_questions_disambiguated_retrieve5_contrievermsm_selfrag13b_results.jsonl",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/llama_runs_results/entity_questions_disambiguated_retrieve10_contrievermsm_selfrag7b_results.jsonl",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/llama_runs_results/entity_questions_disambiguated_retrieve15_contrievermsm_selfrag7b_results.jsonl",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/llama_runs_results/entity_questions_disambiguated_retrieve20_contrievermsm_selfrag7b_results.jsonl",
    
#     # Selfrag-13B with different ndocs
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/llama_runs_results/entity_questions_disambiguated_retrieve5_contrievermsm_selfrag13b_results.jsonl",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/llama_runs_results/entity_questions_disambiguated_retrieve10_contrievermsm_selfrag13b_results.jsonl",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/llama_runs_results/entity_questions_disambiguated_retrieve15_contrievermsm_selfrag13b_results.jsonl",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/llama_runs_results/entity_questions_disambiguated_retrieve20_contrievermsm_selfrag13b_results.jsonl"
# ]
prediction_files = [
    # Llama2-13B with different ndocs
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/eq_disamb_gold_retrieve1_llama2_13b_results.jsonl",
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/eq_disamb_gold_retrieve6_llama2_13b_results.jsonl",
    
    # Llama2-13B-Instruct with different ndocs
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/eq_disamb_gold_retrieve1_llama2_13b_instruct_results.jsonl",
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/eq_disamb_gold_retrieve6_llama2_13b_instruct_results.jsonl",
    
    # SelfRAG-13B with different ndocs
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/eq_disamb_gold_retrieve1_selfrag13b_results.jsonl",
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/eq_disamb_gold_retrieve6_selfrag13b_results.jsonl",
    
    # Qwen2.5-7B-Instruct with different ndocs
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/eq_disamb_gold_retrieve1_qwen2_7b_instruct_results.jsonl",
    "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/eq_disamb_gold_retrieve6_qwen2_7b_instruct_results.jsonl"
]

# prediction_files = [
  
#     # Qwen2.5-7B-Instruct with different ndocs (disambiguated)
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_disambiguated_retrieve5_contrievermsm_qwen2_7b_instruct_results.jsonl",
#     '/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/llama_runs_results/entity_questions_disambiguated_retrieve10_contrievermsm_qwen2_7b_instruct_results.jsonl',
#     '/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/llama_runs_results/entity_questions_disambiguated_retrieve15_contrievermsm_qwen2_7b_instruct_results.jsonl',
#     '/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/llama_runs_results/entity_questions_disambiguated_retrieve20_contrievermsm_qwen2_7b_instruct_results.jsonl'
#     ]



# prediction_files = [
#     # Entity Questions - Original input, no retrieval
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_llama2_7b_vanilla_no_retrieval.json",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_llama2_13b_vanilla_no_retrieval.json",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_llama2_7b_instruct_no_retrieval.json",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_llama2_13b_instruct_no_retrieval.json",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_qwen2_7b_instruct_no_retrieval.jsonl",

#     # Entity Questions - Disambiguated input, no retrieval
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_disambig_llama2_7b_vanilla_no_retrieval.json",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_disambig_llama2_13b_vanilla_no_retrieval.json",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_disambig_llama2_7b_instruct_no_retrieval.json",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_disambig_llama2_13b_instruct_no_retrieval.json",
#     "/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_disambig_qwen2_7b_instruct_no_retrieval.jsonl"
# ]

# Call batch_process_metrics to compute metrics for all prediction files
results = batch_process_metrics(csv_file_path, prediction_files)

# Print results
print("\n--- Final Results ---")
for json_file, metrics in results.items():
    print(f"Metrics for {json_file}:")
    if "error" in metrics:
        print(f"  Error processing this file: {metrics['error']}")
    else:
        for metric_name, value in metrics.items():
            if isinstance(value, float) and not pd.isna(value):
                 print(f"  {metric_name}: {value:.1%}")
            else:
                 print(f"  {metric_name}: {value}") # Print NaN or other non-float values as is