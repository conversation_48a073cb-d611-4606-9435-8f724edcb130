==========================================
SLURM_JOB_ID = 79232
SLURM_NODELIST = gpunode01
==========================================
INFO 04-19 10:57:14 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-7b-hf', tokenizer='meta-llama/Llama-2-7b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 04-19 11:01:50 llm_engine.py:223] # GPU blocks: 3742, # CPU blocks: 512
INFO 04-19 11:01:53 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-19 11:01:57 model_runner.py:437] Graph capturing finished in 4 secs.
overall result: 0.001040943789035392
INFO 04-19 11:29:10 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-7b-hf', tokenizer='meta-llama/Llama-2-7b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 04-19 11:29:16 llm_engine.py:223] # GPU blocks: 3742, # CPU blocks: 512
INFO 04-19 11:29:18 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-19 11:29:23 model_runner.py:437] Graph capturing finished in 4 secs.
overall result: 0.001040943789035392
INFO 04-19 12:03:31 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-7b-hf', tokenizer='meta-llama/Llama-2-7b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 04-19 12:03:36 llm_engine.py:223] # GPU blocks: 3742, # CPU blocks: 512
INFO 04-19 12:03:39 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-19 12:03:43 model_runner.py:437] Graph capturing finished in 4 secs.
WARNING 04-19 12:10:29 scheduler.py:147] Input prompt (4605 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:13:57 scheduler.py:147] Input prompt (4847 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:39:59 scheduler.py:147] Input prompt (4193 tokens) is too long and exceeds limit of 4096
overall result: 0.00034698126301179735
INFO 04-19 12:45:09 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-7b-hf', tokenizer='meta-llama/Llama-2-7b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 04-19 12:45:14 llm_engine.py:223] # GPU blocks: 3742, # CPU blocks: 512
INFO 04-19 12:45:16 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-19 12:45:21 model_runner.py:437] Graph capturing finished in 4 secs.
WARNING 04-19 12:46:07 scheduler.py:147] Input prompt (4158 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:46:25 scheduler.py:147] Input prompt (4138 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:48:07 scheduler.py:147] Input prompt (4431 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:49:04 scheduler.py:147] Input prompt (4487 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:49:08 scheduler.py:147] Input prompt (4276 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:49:13 scheduler.py:147] Input prompt (4555 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:49:24 scheduler.py:147] Input prompt (4623 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:49:27 scheduler.py:147] Input prompt (4191 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:49:39 scheduler.py:147] Input prompt (5200 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:49:42 scheduler.py:147] Input prompt (4597 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:49:43 scheduler.py:147] Input prompt (4259 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:49:57 scheduler.py:147] Input prompt (4389 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:50:07 scheduler.py:147] Input prompt (4640 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:50:12 scheduler.py:147] Input prompt (4243 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:50:15 scheduler.py:147] Input prompt (4527 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:51:42 scheduler.py:147] Input prompt (4283 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:51:43 scheduler.py:147] Input prompt (4118 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:51:45 scheduler.py:147] Input prompt (4221 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:52:01 scheduler.py:147] Input prompt (4530 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:52:37 scheduler.py:147] Input prompt (4333 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:52:41 scheduler.py:147] Input prompt (4650 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:52:56 scheduler.py:147] Input prompt (6198 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:52:57 scheduler.py:147] Input prompt (4367 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:53:00 scheduler.py:147] Input prompt (4837 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:53:25 scheduler.py:147] Input prompt (4308 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:53:29 scheduler.py:147] Input prompt (4162 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:53:49 scheduler.py:147] Input prompt (4746 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:54:18 scheduler.py:147] Input prompt (4465 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:54:28 scheduler.py:147] Input prompt (4295 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:55:59 scheduler.py:147] Input prompt (4307 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:56:26 scheduler.py:147] Input prompt (4374 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:56:52 scheduler.py:147] Input prompt (4417 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:56:52 scheduler.py:147] Input prompt (5879 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:57:16 scheduler.py:147] Input prompt (4206 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:57:20 scheduler.py:147] Input prompt (4186 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:57:25 scheduler.py:147] Input prompt (4289 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:58:15 scheduler.py:147] Input prompt (4395 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:58:19 scheduler.py:147] Input prompt (4115 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:58:24 scheduler.py:147] Input prompt (4545 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:59:35 scheduler.py:147] Input prompt (4359 tokens) is too long and exceeds limit of 4096
WARNING 04-19 12:59:53 scheduler.py:147] Input prompt (4239 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:00:21 scheduler.py:147] Input prompt (4382 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:01:12 scheduler.py:147] Input prompt (4323 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:04:23 scheduler.py:147] Input prompt (4510 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:04:23 scheduler.py:147] Input prompt (4313 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:04:27 scheduler.py:147] Input prompt (4131 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:04:41 scheduler.py:147] Input prompt (4166 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:04:54 scheduler.py:147] Input prompt (4481 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:04:58 scheduler.py:147] Input prompt (4173 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:05:28 scheduler.py:147] Input prompt (4136 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:05:33 scheduler.py:147] Input prompt (4290 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:07:34 scheduler.py:147] Input prompt (4485 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:08:46 scheduler.py:147] Input prompt (4189 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:09:27 scheduler.py:147] Input prompt (4761 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:09:37 scheduler.py:147] Input prompt (4157 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:09:47 scheduler.py:147] Input prompt (4463 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:10:38 scheduler.py:147] Input prompt (4118 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:10:43 scheduler.py:147] Input prompt (4444 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:10:44 scheduler.py:147] Input prompt (4207 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:10:51 scheduler.py:147] Input prompt (4369 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:10:52 scheduler.py:147] Input prompt (4493 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:10:55 scheduler.py:147] Input prompt (4106 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:11:00 scheduler.py:147] Input prompt (4171 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:11:00 scheduler.py:147] Input prompt (4412 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:11:14 scheduler.py:147] Input prompt (4186 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:11:18 scheduler.py:147] Input prompt (4958 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:11:22 scheduler.py:147] Input prompt (5414 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:11:57 scheduler.py:147] Input prompt (4381 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:17:20 scheduler.py:147] Input prompt (4275 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:18:02 scheduler.py:147] Input prompt (4150 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:19:14 scheduler.py:147] Input prompt (4382 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:19:19 scheduler.py:147] Input prompt (4306 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:19:50 scheduler.py:147] Input prompt (4321 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:20:22 scheduler.py:147] Input prompt (4153 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:20:26 scheduler.py:147] Input prompt (4266 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:21:14 scheduler.py:147] Input prompt (4305 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:21:50 scheduler.py:147] Input prompt (4565 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:22:05 scheduler.py:147] Input prompt (4117 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:22:25 scheduler.py:147] Input prompt (4192 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:23:34 scheduler.py:147] Input prompt (4719 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:23:37 scheduler.py:147] Input prompt (4226 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:23:52 scheduler.py:147] Input prompt (4181 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:25:42 scheduler.py:147] Input prompt (4338 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:25:52 scheduler.py:147] Input prompt (4168 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:26:13 scheduler.py:147] Input prompt (4239 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:26:29 scheduler.py:147] Input prompt (4298 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:26:33 scheduler.py:147] Input prompt (5155 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:27:00 scheduler.py:147] Input prompt (4248 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:28:11 scheduler.py:147] Input prompt (4117 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:28:23 scheduler.py:147] Input prompt (4509 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:28:38 scheduler.py:147] Input prompt (4324 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:28:44 scheduler.py:147] Input prompt (4109 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:29:10 scheduler.py:147] Input prompt (4310 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:29:15 scheduler.py:147] Input prompt (4164 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:29:15 scheduler.py:147] Input prompt (4448 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:29:19 scheduler.py:147] Input prompt (4305 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:29:25 scheduler.py:147] Input prompt (4716 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:29:25 scheduler.py:147] Input prompt (4273 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:29:28 scheduler.py:147] Input prompt (4117 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:30:11 scheduler.py:147] Input prompt (4143 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:30:56 scheduler.py:147] Input prompt (4225 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:31:04 scheduler.py:147] Input prompt (4851 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:31:26 scheduler.py:147] Input prompt (4884 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:31:35 scheduler.py:147] Input prompt (4193 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:31:35 scheduler.py:147] Input prompt (4512 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:31:52 scheduler.py:147] Input prompt (4143 tokens) is too long and exceeds limit of 4096
WARNING 04-19 13:32:07 scheduler.py:147] Input prompt (4172 tokens) is too long and exceeds limit of 4096
overall result: 0.00034698126301179735
INFO 04-19 13:32:20 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-13b-hf', tokenizer='meta-llama/Llama-2-13b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 04-19 13:32:29 llm_engine.py:223] # GPU blocks: 1425, # CPU blocks: 327
INFO 04-19 13:32:32 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-19 13:32:37 model_runner.py:437] Graph capturing finished in 5 secs.
overall result: 0.00034698126301179735
INFO 04-19 14:21:07 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-13b-hf', tokenizer='meta-llama/Llama-2-13b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 04-19 14:21:16 llm_engine.py:223] # GPU blocks: 1425, # CPU blocks: 327
INFO 04-19 14:21:19 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-19 14:21:24 model_runner.py:437] Graph capturing finished in 5 secs.
overall result: 0.00034698126301179735
INFO 04-19 15:21:06 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-13b-hf', tokenizer='meta-llama/Llama-2-13b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 04-19 15:21:15 llm_engine.py:223] # GPU blocks: 1425, # CPU blocks: 327
INFO 04-19 15:21:18 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-19 15:21:23 model_runner.py:437] Graph capturing finished in 5 secs.
WARNING 04-19 15:33:14 scheduler.py:147] Input prompt (4605 tokens) is too long and exceeds limit of 4096
WARNING 04-19 15:39:14 scheduler.py:147] Input prompt (4847 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:24:39 scheduler.py:147] Input prompt (4193 tokens) is too long and exceeds limit of 4096
overall result: 0.0006939625260235947
INFO 04-19 16:33:37 llm_engine.py:73] Initializing an LLM engine with config: model='meta-llama/Llama-2-13b-hf', tokenizer='meta-llama/Llama-2-13b-hf', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 04-19 16:33:46 llm_engine.py:223] # GPU blocks: 1425, # CPU blocks: 327
INFO 04-19 16:33:49 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-19 16:33:54 model_runner.py:437] Graph capturing finished in 5 secs.
WARNING 04-19 16:35:13 scheduler.py:147] Input prompt (4158 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:35:45 scheduler.py:147] Input prompt (4138 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:38:44 scheduler.py:147] Input prompt (4431 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:40:23 scheduler.py:147] Input prompt (4487 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:40:31 scheduler.py:147] Input prompt (4276 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:40:39 scheduler.py:147] Input prompt (4555 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:40:59 scheduler.py:147] Input prompt (4623 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:41:05 scheduler.py:147] Input prompt (4191 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:41:25 scheduler.py:147] Input prompt (5200 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:41:31 scheduler.py:147] Input prompt (4597 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:41:33 scheduler.py:147] Input prompt (4259 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:41:58 scheduler.py:147] Input prompt (4389 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:42:17 scheduler.py:147] Input prompt (4640 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:42:26 scheduler.py:147] Input prompt (4243 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:42:31 scheduler.py:147] Input prompt (4527 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:45:05 scheduler.py:147] Input prompt (4283 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:45:06 scheduler.py:147] Input prompt (4118 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:45:10 scheduler.py:147] Input prompt (4221 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:45:38 scheduler.py:147] Input prompt (4530 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:46:39 scheduler.py:147] Input prompt (4333 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:46:46 scheduler.py:147] Input prompt (4650 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:47:11 scheduler.py:147] Input prompt (6198 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:47:13 scheduler.py:147] Input prompt (4367 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:47:19 scheduler.py:147] Input prompt (4837 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:48:00 scheduler.py:147] Input prompt (4308 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:48:08 scheduler.py:147] Input prompt (4162 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:48:40 scheduler.py:147] Input prompt (4746 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:49:33 scheduler.py:147] Input prompt (4465 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:49:50 scheduler.py:147] Input prompt (4295 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:52:25 scheduler.py:147] Input prompt (4307 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:53:13 scheduler.py:147] Input prompt (4374 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:53:56 scheduler.py:147] Input prompt (4417 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:53:56 scheduler.py:147] Input prompt (5879 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:54:38 scheduler.py:147] Input prompt (4206 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:54:46 scheduler.py:147] Input prompt (4186 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:54:54 scheduler.py:147] Input prompt (4289 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:56:11 scheduler.py:147] Input prompt (4395 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:56:20 scheduler.py:147] Input prompt (4115 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:56:28 scheduler.py:147] Input prompt (4545 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:58:27 scheduler.py:147] Input prompt (4359 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:58:53 scheduler.py:147] Input prompt (4239 tokens) is too long and exceeds limit of 4096
WARNING 04-19 16:59:41 scheduler.py:147] Input prompt (4382 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:01:05 scheduler.py:147] Input prompt (4323 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:06:38 scheduler.py:147] Input prompt (4510 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:06:39 scheduler.py:147] Input prompt (4313 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:06:46 scheduler.py:147] Input prompt (4131 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:07:11 scheduler.py:147] Input prompt (4166 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:07:35 scheduler.py:147] Input prompt (4481 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:07:38 scheduler.py:147] Input prompt (4173 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:08:30 scheduler.py:147] Input prompt (4136 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:08:37 scheduler.py:147] Input prompt (4290 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:12:12 scheduler.py:147] Input prompt (4485 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:14:23 scheduler.py:147] Input prompt (4189 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:15:37 scheduler.py:147] Input prompt (4761 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:15:55 scheduler.py:147] Input prompt (4157 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:16:13 scheduler.py:147] Input prompt (4463 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:17:44 scheduler.py:147] Input prompt (4118 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:17:53 scheduler.py:147] Input prompt (4444 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:17:54 scheduler.py:147] Input prompt (4207 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:18:08 scheduler.py:147] Input prompt (4369 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:18:10 scheduler.py:147] Input prompt (4493 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:18:15 scheduler.py:147] Input prompt (4106 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:18:22 scheduler.py:147] Input prompt (4171 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:18:22 scheduler.py:147] Input prompt (4412 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:18:52 scheduler.py:147] Input prompt (4186 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:19:00 scheduler.py:147] Input prompt (4958 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:19:07 scheduler.py:147] Input prompt (5414 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:20:11 scheduler.py:147] Input prompt (4381 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:29:20 scheduler.py:147] Input prompt (4275 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:30:35 scheduler.py:147] Input prompt (4150 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:32:40 scheduler.py:147] Input prompt (4382 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:32:48 scheduler.py:147] Input prompt (4306 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:33:42 scheduler.py:147] Input prompt (4321 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:34:38 scheduler.py:147] Input prompt (4153 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:34:46 scheduler.py:147] Input prompt (4266 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:36:08 scheduler.py:147] Input prompt (4305 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:37:12 scheduler.py:147] Input prompt (4565 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:37:39 scheduler.py:147] Input prompt (4117 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:38:15 scheduler.py:147] Input prompt (4192 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:40:20 scheduler.py:147] Input prompt (4719 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:40:26 scheduler.py:147] Input prompt (4226 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:40:52 scheduler.py:147] Input prompt (4181 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:44:08 scheduler.py:147] Input prompt (4338 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:44:24 scheduler.py:147] Input prompt (4168 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:45:00 scheduler.py:147] Input prompt (4239 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:45:28 scheduler.py:147] Input prompt (4298 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:45:35 scheduler.py:147] Input prompt (5155 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:46:22 scheduler.py:147] Input prompt (4248 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:48:26 scheduler.py:147] Input prompt (4117 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:48:46 scheduler.py:147] Input prompt (4509 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:49:13 scheduler.py:147] Input prompt (4324 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:49:23 scheduler.py:147] Input prompt (4109 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:50:08 scheduler.py:147] Input prompt (4310 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:50:16 scheduler.py:147] Input prompt (4164 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:50:16 scheduler.py:147] Input prompt (4448 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:50:24 scheduler.py:147] Input prompt (4305 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:50:33 scheduler.py:147] Input prompt (4716 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:50:33 scheduler.py:147] Input prompt (4273 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:50:39 scheduler.py:147] Input prompt (4117 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:51:54 scheduler.py:147] Input prompt (4143 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:53:11 scheduler.py:147] Input prompt (4225 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:53:26 scheduler.py:147] Input prompt (4851 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:54:06 scheduler.py:147] Input prompt (4884 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:54:20 scheduler.py:147] Input prompt (4193 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:54:20 scheduler.py:147] Input prompt (4512 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:54:51 scheduler.py:147] Input prompt (4143 tokens) is too long and exceeds limit of 4096
WARNING 04-19 17:55:17 scheduler.py:147] Input prompt (4172 tokens) is too long and exceeds limit of 4096
overall result: 0.00034698126301179735
