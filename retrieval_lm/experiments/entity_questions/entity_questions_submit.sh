#!/bin/bash
#SBATCH --job-name=entity_questions_L7B_L13B_orig
#SBATCH --output=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/slurm_outputs/entity_questions_L7B_L13B_orig_%j.out
#SBATCH --error=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/slurm_outputs/entity_questions_L7B_L13B_orig_%j.err
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=24:00:00
#SBATCH --partition=ampere  

# Activate conda environment
source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

# Llama2-7B-Instruct with different ndocs
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-7b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve5_contrievermsm_llama2_7b_instruct_results.jsonl \
    --metric match --ndocs 5 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-7b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve10_contrievermsm_llama2_7b_instruct_results.jsonl \
    --metric match --ndocs 10 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-7b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve15_contrievermsm_llama2_7b_instruct_results.jsonl \
    --metric match --ndocs 15 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-7b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm_llama2_7b_instruct_results.jsonl \
    --metric match --ndocs 20 --use_groundness --use_utility --use_seqscore \
    --dtype half

# Llama2-13B-Instruct with different ndocs
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-13b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve5_contrievermsm_llama2_13b_instruct_results.jsonl \
    --metric match --ndocs 5 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-13b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve10_contrievermsm_llama2_13b_instruct_results.jsonl \
    --metric match --ndocs 10 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-13b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve15_contrievermsm_llama2_13b_instruct_results.jsonl \
    --metric match --ndocs 15 --use_groundness --use_utility --use_seqscore \
    --dtype half

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-13b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm_llama2_13b_instruct_results.jsonl \
    --metric match --ndocs 20 --use_groundness --use_utility --use_seqscore \
    --dtype half