
Loading pt checkpoint shards:   0% Completed | 0/3 [00:00<?, ?it/s]
/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/model_executor/model_loader/weight_utils.py:425: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  state = torch.load(bin_file, map_location="cpu")

Loading pt checkpoint shards:  33% Completed | 1/3 [00:18<00:36, 18.20s/it]

Loading pt checkpoint shards:  67% Completed | 2/3 [00:45<00:23, 23.74s/it]

Loading pt checkpoint shards: 100% Completed | 3/3 [01:13<00:00, 25.36s/it]

Loading pt checkpoint shards: 100% Completed | 3/3 [01:13<00:00, 24.37s/it]


0it [00:00, ?it/s]
0it [00:00, ?it/s]
[rank0]: Traceback (most recent call last):
[rank0]:   File "/home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py", line 371, in <module>
[rank0]:     main()
[rank0]:   File "/home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py", line 329, in main
[rank0]:     pred, results, do_retrieve = generate(
[rank0]:   File "/home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py", line 313, in generate
[rank0]:     return call_model_rerank_w_scores_batch(prompt, evidences=evidences, model=model, max_new_tokens=max_new_tokens,
[rank0]:   File "/home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py", line 59, in call_model_rerank_w_scores_batch
[rank0]:     preds = model.generate([prompt], sampling_params)
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/utils.py", line 1063, in inner
[rank0]:     return fn(*args, **kwargs)
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/entrypoints/llm.py", line 345, in generate
[rank0]:     self._validate_and_add_requests(
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/entrypoints/llm.py", line 812, in _validate_and_add_requests
[rank0]:     self._add_request(
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/entrypoints/llm.py", line 830, in _add_request
[rank0]:     self.llm_engine.add_request(
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/utils.py", line 1063, in inner
[rank0]:     return fn(*args, **kwargs)
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/engine/llm_engine.py", line 820, in add_request
[rank0]:     self._add_processed_request(
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/engine/llm_engine.py", line 666, in _add_processed_request
[rank0]:     seq_group = self._create_sequence_group_with_sampling(
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/engine/llm_engine.py", line 849, in _create_sequence_group_with_sampling
[rank0]:     raise ValueError(f"Cannot request more than "
[rank0]: ValueError: Cannot request more than 20 logprobs.

Loading pt checkpoint shards:   0% Completed | 0/3 [00:00<?, ?it/s]
/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/model_executor/model_loader/weight_utils.py:425: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  state = torch.load(bin_file, map_location="cpu")

Loading pt checkpoint shards:  33% Completed | 1/3 [00:02<00:05,  2.59s/it]

Loading pt checkpoint shards:  67% Completed | 2/3 [00:06<00:03,  3.49s/it]

Loading pt checkpoint shards: 100% Completed | 3/3 [00:10<00:00,  3.80s/it]

Loading pt checkpoint shards: 100% Completed | 3/3 [00:10<00:00,  3.62s/it]


0it [00:00, ?it/s]
0it [00:00, ?it/s]
[rank0]: Traceback (most recent call last):
[rank0]:   File "/home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py", line 371, in <module>
[rank0]:     main()
[rank0]:   File "/home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py", line 329, in main
[rank0]:     pred, results, do_retrieve = generate(
[rank0]:   File "/home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py", line 313, in generate
[rank0]:     return call_model_rerank_w_scores_batch(prompt, evidences=evidences, model=model, max_new_tokens=max_new_tokens,
[rank0]:   File "/home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py", line 59, in call_model_rerank_w_scores_batch
[rank0]:     preds = model.generate([prompt], sampling_params)
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/utils.py", line 1063, in inner
[rank0]:     return fn(*args, **kwargs)
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/entrypoints/llm.py", line 345, in generate
[rank0]:     self._validate_and_add_requests(
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/entrypoints/llm.py", line 812, in _validate_and_add_requests
[rank0]:     self._add_request(
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/entrypoints/llm.py", line 830, in _add_request
[rank0]:     self.llm_engine.add_request(
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/utils.py", line 1063, in inner
[rank0]:     return fn(*args, **kwargs)
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/engine/llm_engine.py", line 820, in add_request
[rank0]:     self._add_processed_request(
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/engine/llm_engine.py", line 666, in _add_processed_request
[rank0]:     seq_group = self._create_sequence_group_with_sampling(
[rank0]:   File "/home/<USER>/.conda/envs/selfrag/lib/python3.8/site-packages/vllm/engine/llm_engine.py", line 849, in _create_sequence_group_with_sampling
[rank0]:     raise ValueError(f"Cannot request more than "
[rank0]: ValueError: Cannot request more than 20 logprobs.
huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...
To disable this warning, you can either:
	- Avoid using `tokenizers` before the fork if possible
	- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)
