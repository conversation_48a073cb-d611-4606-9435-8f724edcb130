usage: run_baseline_lm.py [-h] [--model_name MODEL_NAME] --input_file
                          INPUT_FILE [--retrieval_file RETRIEVAL_FILE]
                          [--mode MODE] [--device DEVICE]
                          [--max_new_tokens MAX_NEW_TOKENS] [--int8bit]
                          [--metric METRIC] [--top_n TOP_N]
                          [--result_fp RESULT_FP] [--task TASK]
                          [--prompt_name PROMPT_NAME]
                          [--batch_size BATCH_SIZE] [--dtype DTYPE]
                          [--world_size WORLD_SIZE] [--choices CHOICES]
                          [--instruction INSTRUCTION]
                          [--download_dir DOWNLOAD_DIR] [--api_key API_KEY]
run_baseline_lm.py: error: unrecognized arguments: --threshold 0.2 --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/eq_disamb_gold_retrieve1_llama2_13b_results.jsonl --ndocs 1 --use_groundness --use_utility --use_seqscore
usage: run_baseline_lm.py [-h] [--model_name MODEL_NAME] --input_file
                          INPUT_FILE [--retrieval_file RETRIEVAL_FILE]
                          [--mode MODE] [--device DEVICE]
                          [--max_new_tokens MAX_NEW_TOKENS] [--int8bit]
                          [--metric METRIC] [--top_n TOP_N]
                          [--result_fp RESULT_FP] [--task TASK]
                          [--prompt_name PROMPT_NAME]
                          [--batch_size BATCH_SIZE] [--dtype DTYPE]
                          [--world_size WORLD_SIZE] [--choices CHOICES]
                          [--instruction INSTRUCTION]
                          [--download_dir DOWNLOAD_DIR] [--api_key API_KEY]
run_baseline_lm.py: error: unrecognized arguments: --threshold 0.2 --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/eq_disamb_gold_retrieve6_llama2_13b_results.jsonl --ndocs 6 --use_groundness --use_utility --use_seqscore
usage: run_baseline_lm.py [-h] [--model_name MODEL_NAME] --input_file
                          INPUT_FILE [--retrieval_file RETRIEVAL_FILE]
                          [--mode MODE] [--device DEVICE]
                          [--max_new_tokens MAX_NEW_TOKENS] [--int8bit]
                          [--metric METRIC] [--top_n TOP_N]
                          [--result_fp RESULT_FP] [--task TASK]
                          [--prompt_name PROMPT_NAME]
                          [--batch_size BATCH_SIZE] [--dtype DTYPE]
                          [--world_size WORLD_SIZE] [--choices CHOICES]
                          [--instruction INSTRUCTION]
                          [--download_dir DOWNLOAD_DIR] [--api_key API_KEY]
run_baseline_lm.py: error: unrecognized arguments: --threshold 0.2 --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/eq_disamb_gold_retrieve1_llama2_13b_instruct_results.jsonl --ndocs 1 --use_groundness --use_utility --use_seqscore
usage: run_baseline_lm.py [-h] [--model_name MODEL_NAME] --input_file
                          INPUT_FILE [--retrieval_file RETRIEVAL_FILE]
                          [--mode MODE] [--device DEVICE]
                          [--max_new_tokens MAX_NEW_TOKENS] [--int8bit]
                          [--metric METRIC] [--top_n TOP_N]
                          [--result_fp RESULT_FP] [--task TASK]
                          [--prompt_name PROMPT_NAME]
                          [--batch_size BATCH_SIZE] [--dtype DTYPE]
                          [--world_size WORLD_SIZE] [--choices CHOICES]
                          [--instruction INSTRUCTION]
                          [--download_dir DOWNLOAD_DIR] [--api_key API_KEY]
run_baseline_lm.py: error: unrecognized arguments: --threshold 0.2 --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/eq_disamb_gold_retrieve6_llama2_13b_instruct_results.jsonl --ndocs 6 --use_groundness --use_utility --use_seqscore
