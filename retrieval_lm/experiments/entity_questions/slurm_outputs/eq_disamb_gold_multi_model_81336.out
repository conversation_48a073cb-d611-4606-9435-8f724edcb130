==========================================
SLURM_JOB_ID = 81336
SLURM_NODELIST = gpunode01
==========================================
WARNING 05-03 18:13:38 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 18:13:48 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 18:13:50 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-hf...
INFO 05-03 18:13:50 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 18:18:26 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 05-03 18:18:27 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 18:18:27 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 18:18:30 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 18:18:30 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 18:18:42 model_runner.py:1523] Graph capturing finished in 12 secs.
WARNING 05-03 18:19:32 scheduler.py:895] Input prompt (7205 tokens) is too long and exceeds limit of 4096
WARNING 05-03 18:21:59 scheduler.py:895] Input prompt (6816 tokens) is too long and exceeds limit of 4096
WARNING 05-03 18:22:11 scheduler.py:895] Input prompt (4141 tokens) is too long and exceeds limit of 4096
WARNING 05-03 18:23:46 scheduler.py:895] Input prompt (4653 tokens) is too long and exceeds limit of 4096
WARNING 05-03 18:24:08 scheduler.py:895] Input prompt (8743 tokens) is too long and exceeds limit of 4096
WARNING 05-03 18:25:42 scheduler.py:895] Input prompt (4269 tokens) is too long and exceeds limit of 4096
WARNING 05-03 18:32:55 scheduler.py:895] Input prompt (7145 tokens) is too long and exceeds limit of 4096
WARNING 05-03 18:36:02 scheduler.py:895] Input prompt (6929 tokens) is too long and exceeds limit of 4096
WARNING 05-03 18:36:31 scheduler.py:895] Input prompt (4850 tokens) is too long and exceeds limit of 4096
WARNING 05-03 18:37:03 scheduler.py:895] Input prompt (5010 tokens) is too long and exceeds limit of 4096
WARNING 05-03 18:42:05 scheduler.py:895] Input prompt (8757 tokens) is too long and exceeds limit of 4096
WARNING 05-03 18:42:09 scheduler.py:895] Input prompt (7091 tokens) is too long and exceeds limit of 4096
WARNING 05-03 18:42:53 scheduler.py:895] Input prompt (4890 tokens) is too long and exceeds limit of 4096
WARNING 05-03 18:46:37 scheduler.py:895] Input prompt (8819 tokens) is too long and exceeds limit of 4096
WARNING 05-03 18:48:12 scheduler.py:895] Input prompt (4569 tokens) is too long and exceeds limit of 4096
WARNING 05-03 18:51:43 scheduler.py:895] Input prompt (4880 tokens) is too long and exceeds limit of 4096
WARNING 05-03 18:52:56 scheduler.py:895] Input prompt (12208 tokens) is too long and exceeds limit of 4096
WARNING 05-03 18:53:00 scheduler.py:895] Input prompt (12020 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:00:32 scheduler.py:895] Input prompt (5259 tokens) is too long and exceeds limit of 4096
overall result: 0.6148507980569049
WARNING 05-03 19:01:16 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 19:01:27 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 19:01:30 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-hf...
INFO 05-03 19:01:30 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 19:02:18 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 05-03 19:02:20 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 19:02:20 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 19:02:23 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 19:02:23 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 19:02:35 model_runner.py:1523] Graph capturing finished in 12 secs.
WARNING 05-03 19:03:39 scheduler.py:895] Input prompt (8172 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:06:49 scheduler.py:895] Input prompt (7269 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:07:05 scheduler.py:895] Input prompt (5192 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:09:06 scheduler.py:895] Input prompt (5280 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:09:35 scheduler.py:895] Input prompt (9727 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:11:37 scheduler.py:895] Input prompt (5073 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:20:40 scheduler.py:895] Input prompt (7645 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:24:30 scheduler.py:895] Input prompt (7555 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:25:06 scheduler.py:895] Input prompt (5636 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:25:47 scheduler.py:895] Input prompt (6115 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:30:00 scheduler.py:895] Input prompt (4133 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:32:17 scheduler.py:895] Input prompt (9260 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:32:22 scheduler.py:895] Input prompt (7813 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:33:21 scheduler.py:895] Input prompt (5597 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:38:02 scheduler.py:895] Input prompt (9359 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:38:43 scheduler.py:895] Input prompt (4116 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:39:57 scheduler.py:895] Input prompt (5556 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:44:31 scheduler.py:895] Input prompt (5823 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:45:09 scheduler.py:895] Input prompt (4476 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:45:37 scheduler.py:895] Input prompt (4486 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:46:00 scheduler.py:895] Input prompt (12904 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:46:05 scheduler.py:895] Input prompt (12860 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:46:47 scheduler.py:895] Input prompt (4312 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:56:05 scheduler.py:895] Input prompt (6025 tokens) is too long and exceeds limit of 4096
overall result: 0.5357390700902152
WARNING 05-03 19:57:02 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 19:57:12 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 19:57:13 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-chat-hf...
INFO 05-03 19:57:13 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 19:57:20 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 05-03 19:57:21 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 19:57:21 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 19:57:24 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 19:57:24 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 19:57:37 model_runner.py:1523] Graph capturing finished in 12 secs.
WARNING 05-03 19:58:02 scheduler.py:895] Input prompt (7205 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:59:17 scheduler.py:895] Input prompt (6816 tokens) is too long and exceeds limit of 4096
WARNING 05-03 19:59:23 scheduler.py:895] Input prompt (4141 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:00:16 scheduler.py:895] Input prompt (4653 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:00:35 scheduler.py:895] Input prompt (8743 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:01:23 scheduler.py:895] Input prompt (4269 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:05:22 scheduler.py:895] Input prompt (7145 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:06:38 scheduler.py:895] Input prompt (6929 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:06:56 scheduler.py:895] Input prompt (4850 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:07:15 scheduler.py:895] Input prompt (5010 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:11:40 scheduler.py:895] Input prompt (8757 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:11:42 scheduler.py:895] Input prompt (7091 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:12:21 scheduler.py:895] Input prompt (4890 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:15:51 scheduler.py:895] Input prompt (8819 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:17:15 scheduler.py:895] Input prompt (4569 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:18:53 scheduler.py:895] Input prompt (4880 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:19:41 scheduler.py:895] Input prompt (12208 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:19:45 scheduler.py:895] Input prompt (12020 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:25:06 scheduler.py:895] Input prompt (5259 tokens) is too long and exceeds limit of 4096
overall result: 0.8157529493407356
WARNING 05-03 20:25:35 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 20:25:43 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 20:25:44 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-chat-hf...
INFO 05-03 20:25:45 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 20:26:30 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 05-03 20:26:32 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 20:26:32 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 20:26:35 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 20:26:35 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 20:26:48 model_runner.py:1523] Graph capturing finished in 13 secs.
WARNING 05-03 20:27:28 scheduler.py:895] Input prompt (8172 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:29:28 scheduler.py:895] Input prompt (7269 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:29:37 scheduler.py:895] Input prompt (5192 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:31:03 scheduler.py:895] Input prompt (5280 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:31:26 scheduler.py:895] Input prompt (9727 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:32:52 scheduler.py:895] Input prompt (5073 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:38:48 scheduler.py:895] Input prompt (7645 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:41:19 scheduler.py:895] Input prompt (7555 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:41:50 scheduler.py:895] Input prompt (5636 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:42:28 scheduler.py:895] Input prompt (6115 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:46:24 scheduler.py:895] Input prompt (4133 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:48:33 scheduler.py:895] Input prompt (9260 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:48:37 scheduler.py:895] Input prompt (7813 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:49:31 scheduler.py:895] Input prompt (5597 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:53:55 scheduler.py:895] Input prompt (9359 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:54:32 scheduler.py:895] Input prompt (4116 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:55:44 scheduler.py:895] Input prompt (5556 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:58:33 scheduler.py:895] Input prompt (5823 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:58:56 scheduler.py:895] Input prompt (4476 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:59:18 scheduler.py:895] Input prompt (4486 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:59:39 scheduler.py:895] Input prompt (12904 tokens) is too long and exceeds limit of 4096
WARNING 05-03 20:59:44 scheduler.py:895] Input prompt (12860 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:00:23 scheduler.py:895] Input prompt (4312 tokens) is too long and exceeds limit of 4096
WARNING 05-03 21:07:55 scheduler.py:895] Input prompt (6025 tokens) is too long and exceeds limit of 4096
overall result: 0.7699514226231784
