==========================================
SLURM_JOB_ID = 81340
SLURM_NODELIST = gpunode01
==========================================
WARNING 05-03 18:27:19 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
WARNING 05-03 18:27:23 config.py:1668] Casting torch.bfloat16 to torch.float16.
INFO 05-03 18:27:27 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='selfrag/selfrag_llama2_13b', speculative_config=None, tokenizer='selfrag/selfrag_llama2_13b', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=selfrag/selfrag_llama2_13b, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 18:27:28 model_runner.py:1056] Starting to load model selfrag/selfrag_llama2_13b...
INFO 05-03 18:27:28 weight_utils.py:243] Using model weights format ['*.bin']
INFO 05-03 18:28:42 model_runner.py:1067] Loading model weights took 24.2869 GB
INFO 05-03 18:28:43 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 18:28:43 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 18:28:46 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 18:28:46 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 18:29:00 model_runner.py:1523] Graph capturing finished in 14 secs.
WARNING 05-03 18:29:05 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
WARNING 05-03 18:29:08 config.py:1668] Casting torch.bfloat16 to torch.float16.
INFO 05-03 18:29:12 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='selfrag/selfrag_llama2_13b', speculative_config=None, tokenizer='selfrag/selfrag_llama2_13b', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=selfrag/selfrag_llama2_13b, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 18:29:13 model_runner.py:1056] Starting to load model selfrag/selfrag_llama2_13b...
INFO 05-03 18:29:14 weight_utils.py:243] Using model weights format ['*.bin']
INFO 05-03 18:29:25 model_runner.py:1067] Loading model weights took 24.2869 GB
INFO 05-03 18:29:26 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 18:29:26 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 18:29:29 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 18:29:29 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 18:29:43 model_runner.py:1523] Graph capturing finished in 14 secs.
