==========================================
SLURM_JOB_ID = 81314
SLURM_NODELIST = gpunode01
==========================================
WARNING 05-03 17:10:19 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 17:10:26 config.py:1664] Downcasting torch.float32 to torch.float16.
WARNING 05-03 17:10:32 arg_utils.py:957] Chunked prefill is enabled by default for models with max_model_len > 32K. Currently, chunked prefill might not work with some features or models. If you encounter any issues, please disable chunked prefill by setting --enable-chunked-prefill=False.
INFO 05-03 17:10:32 config.py:1021] Chunked prefill is enabled with max_num_batched_tokens=512.
INFO 05-03 17:10:32 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b', speculative_config=None, tokenizer='meta-llama/Llama-2-13b', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=128000, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b, num_scheduler_steps=1, chunked_prefill_enabled=True multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 17:10:35 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b...
WARNING 05-03 17:10:39 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 17:10:44 config.py:1664] Downcasting torch.float32 to torch.float16.
WARNING 05-03 17:10:49 arg_utils.py:957] Chunked prefill is enabled by default for models with max_model_len > 32K. Currently, chunked prefill might not work with some features or models. If you encounter any issues, please disable chunked prefill by setting --enable-chunked-prefill=False.
INFO 05-03 17:10:49 config.py:1021] Chunked prefill is enabled with max_num_batched_tokens=512.
INFO 05-03 17:10:49 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b', speculative_config=None, tokenizer='meta-llama/Llama-2-13b', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=128000, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b, num_scheduler_steps=1, chunked_prefill_enabled=True multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 17:10:51 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b...
WARNING 05-03 17:10:55 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 17:11:04 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 17:11:05 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-chat-hf...
INFO 05-03 17:11:06 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 17:12:21 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 05-03 17:12:23 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 17:12:23 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 17:12:26 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 17:12:26 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 17:12:40 model_runner.py:1523] Graph capturing finished in 14 secs.
WARNING 05-03 17:12:46 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 17:12:59 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='meta-llama/Llama-2-13b-chat-hf', speculative_config=None, tokenizer='meta-llama/Llama-2-13b-chat-hf', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=meta-llama/Llama-2-13b-chat-hf, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 17:13:00 model_runner.py:1056] Starting to load model meta-llama/Llama-2-13b-chat-hf...
INFO 05-03 17:13:00 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 17:13:06 model_runner.py:1067] Loading model weights took 24.2840 GB
INFO 05-03 17:13:07 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 17:13:07 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 17:13:11 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 17:13:11 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 17:13:24 model_runner.py:1523] Graph capturing finished in 13 secs.
WARNING 05-03 17:13:30 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
WARNING 05-03 17:13:34 config.py:1668] Casting torch.bfloat16 to torch.float16.
INFO 05-03 17:13:39 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='selfrag/selfrag_llama2_13b', speculative_config=None, tokenizer='selfrag/selfrag_llama2_13b', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=selfrag/selfrag_llama2_13b, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 17:13:41 model_runner.py:1056] Starting to load model selfrag/selfrag_llama2_13b...
INFO 05-03 17:13:41 weight_utils.py:243] Using model weights format ['*.bin']
INFO 05-03 17:18:37 model_runner.py:1067] Loading model weights took 24.2869 GB
INFO 05-03 17:18:38 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 17:18:38 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 17:18:42 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 17:18:42 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 17:18:55 model_runner.py:1523] Graph capturing finished in 13 secs.
WARNING 05-03 17:19:02 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
WARNING 05-03 17:19:07 config.py:1668] Casting torch.bfloat16 to torch.float16.
INFO 05-03 17:19:12 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='selfrag/selfrag_llama2_13b', speculative_config=None, tokenizer='selfrag/selfrag_llama2_13b', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=selfrag/selfrag_llama2_13b, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 17:19:14 model_runner.py:1056] Starting to load model selfrag/selfrag_llama2_13b...
INFO 05-03 17:19:14 weight_utils.py:243] Using model weights format ['*.bin']
INFO 05-03 17:19:26 model_runner.py:1067] Loading model weights took 24.2869 GB
INFO 05-03 17:19:27 gpu_executor.py:122] # GPU blocks: 1459, # CPU blocks: 327
INFO 05-03 17:19:27 gpu_executor.py:126] Maximum concurrency for 4096 tokens per request: 5.70x
INFO 05-03 17:19:30 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 17:19:30 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 17:19:43 model_runner.py:1523] Graph capturing finished in 13 secs.
WARNING 05-03 17:19:48 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 17:19:55 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='Qwen/Qwen2.5-7B-Instruct', speculative_config=None, tokenizer='Qwen/Qwen2.5-7B-Instruct', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.bfloat16, max_seq_len=32768, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=Qwen/Qwen2.5-7B-Instruct, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 17:19:56 model_runner.py:1056] Starting to load model Qwen/Qwen2.5-7B-Instruct...
INFO 05-03 17:19:57 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 17:20:41 model_runner.py:1067] Loading model weights took 14.2487 GB
INFO 05-03 17:20:45 gpu_executor.py:122] # GPU blocks: 27715, # CPU blocks: 4681
INFO 05-03 17:20:45 gpu_executor.py:126] Maximum concurrency for 32768 tokens per request: 13.53x
INFO 05-03 17:20:49 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 17:20:49 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 17:20:59 model_runner.py:1523] Graph capturing finished in 10 secs.
overall result: 0.8011797362942401
WARNING 05-03 17:45:25 cuda.py:22] You are using a deprecated `pynvml` package. Please install `nvidia-ml-py` instead, and make sure to uninstall `pynvml`. When both of them are installed, `pynvml` will take precedence and cause errors. See https://pypi.org/project/pynvml for more information.
INFO 05-03 17:45:34 llm_engine.py:237] Initializing an LLM engine (v0.6.3.post1) with config: model='Qwen/Qwen2.5-7B-Instruct', speculative_config=None, tokenizer='Qwen/Qwen2.5-7B-Instruct', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, rope_scaling=None, rope_theta=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.bfloat16, max_seq_len=32768, download_dir='/home/<USER>/.cache/huggingface', load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto, quantization_param_path=None, device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='outlines'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=Qwen/Qwen2.5-7B-Instruct, num_scheduler_steps=1, chunked_prefill_enabled=False multi_step_stream_outputs=True, enable_prefix_caching=False, use_async_output_proc=True, use_cached_outputs=False, mm_processor_kwargs=None)
INFO 05-03 17:45:36 model_runner.py:1056] Starting to load model Qwen/Qwen2.5-7B-Instruct...
INFO 05-03 17:45:36 weight_utils.py:243] Using model weights format ['*.safetensors']
INFO 05-03 17:45:40 model_runner.py:1067] Loading model weights took 14.2487 GB
INFO 05-03 17:45:44 gpu_executor.py:122] # GPU blocks: 27715, # CPU blocks: 4681
INFO 05-03 17:45:44 gpu_executor.py:126] Maximum concurrency for 32768 tokens per request: 13.53x
INFO 05-03 17:45:48 model_runner.py:1395] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 05-03 17:45:48 model_runner.py:1399] CUDA graphs can take additional 1~3 GiB memory per GPU. If you are running out of memory, consider decreasing `gpu_memory_utilization` or enforcing eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 05-03 17:45:58 model_runner.py:1523] Graph capturing finished in 10 secs.
overall result: 0.8344899375433726
