#!/bin/bash
#SBATCH --job-name=entity_questions_SR7B_SR13B_orig_disambig
#SBATCH --output=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/slurm_outputs/entity_questions_SR7B_SR13B_orig_disambig_%j.out
#SBATCH --error=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/slurm_outputs/entity_questions_SR7B_SR13B_orig_disambig_%j.err
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=24:00:00
#SBATCH --partition=ampere  


# Activate conda environment
source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag


python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name selfrag/selfrag_llama2_7b \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_long_tail_disambiguated_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_disambiguated_retrieve5_contrievermsm_selfrag7b_results.json \
    --metric match --ndocs 5 --use_groundness --use_utility --use_seqscore \
    --dtype half


python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name selfrag/selfrag_llama2_13b \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_long_tail_disambiguated_retrieve20_contrievermsm.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_disambiguated_retrieve5_contrievermsm_selfrag13b_results.json \
    --metric match --ndocs 5 --use_groundness --use_utility --use_seqscore \
    --dtype half

# Llama2-13B-Instruct with ndocs=1
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-13b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/eq_disamb_gold.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/eq_disamb_gold_retrieve1_llama2_13b_instruct_results.jsonl \
    --metric match --ndocs 1 --use_groundness --use_utility --use_seqscore \
    --dtype half

# Llama2-13B-Instruct with ndocs=6
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_short_form.py \
    --model_name meta-llama/Llama-2-13b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/eq_disamb_gold.json \
    --mode adaptive_retrieval --max_new_tokens 100 \
    --threshold 0.2 \
    --output_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/eq_disamb_gold_retrieve6_llama2_13b_instruct_results.jsonl \
    --metric match --ndocs 6 --use_groundness --use_utility --use_seqscore \
    --dtype half