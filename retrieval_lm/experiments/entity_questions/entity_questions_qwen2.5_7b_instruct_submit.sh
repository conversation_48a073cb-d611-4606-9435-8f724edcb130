#!/bin/bash
#SBATCH --job-name=entityqa_qwen2_7b
#SBATCH --output=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/slurm_outputs/entityqa_qwen2_7b_%j.out
#SBATCH --error=/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/slurm_outputs/entityqa_qwen2_7b_%j.err
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=24:00:00
#SBATCH --partition=ampere

source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

# Original version with different ndocs (10, 15, 20)
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name Qwen/Qwen2.5-7B-Instruct \
    --download_dir /home/<USER>/.cache/huggingface/hub \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve5_contrievermsm_qwen2_7b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 5 \
    --prompt_name "prompt_no_input_retrieval"


# Disambiguated version with different ndocs (10, 15, 20)
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name Qwen/Qwen2.5-7B-Instruct \
    --download_dir /home/<USER>/.cache/huggingface/hub \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_long_tail_disambiguated_retrieve20_contrievermsm.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_disambiguated_retrieve5_contrievermsm_qwen2_7b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 5 \
    --prompt_name "prompt_no_input_retrieval"

