#!/bin/bash
#SBATCH --job-name=entityqa_L2I7B_L2I13B_original
#SBATCH --output=entityqa_llama2_instruct_original_output.txt
#SBATCH --error=entityqa_llama2_instruct_original_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=18:00:00
#SBATCH --partition=ampere

source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

# Llama2-7B-Instruct with different ndocs
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-7b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve5_contrievermsm_llama2_7b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 5 \
    --prompt_name "llama_chat_prompt_retrieval"

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-7b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve10_contrievermsm_llama2_7b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 10 \
    --prompt_name "llama_chat_prompt_retrieval"

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-7b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve15_contrievermsm_llama2_7b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 15 \
    --prompt_name "llama_chat_prompt_retrieval"

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-7b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm_llama2_7b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 20 \
    --prompt_name "llama_chat_prompt_retrieval"

# Llama2-13B-Instruct with different ndocs
python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-13b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve5_contrievermsm_llama2_13b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 5 \
    --prompt_name "llama_chat_prompt_retrieval"

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-13b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve10_contrievermsm_llama2_13b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 10 \
    --prompt_name "llama_chat_prompt_retrieval"

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-13b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve15_contrievermsm_llama2_13b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 15 \
    --prompt_name "llama_chat_prompt_retrieval"

python /home/<USER>/selfrag_project/self-rag/retrieval_lm/run_baseline_lm.py \
    --model_name meta-llama/Llama-2-13b-chat-hf \
    --input_file /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm.json \
    --max_new_tokens 100 \
    --metric match \
    --result_fp /home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_original_retrieve20_contrievermsm_llama2_13b_instruct_results.jsonl \
    --task qa \
    --mode retrieval \
    --top_n 20 \
    --prompt_name "llama_chat_prompt_retrieval"