import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from metrics import match  # Now Python can find the metrics module
import json
import time  # Import the time module for adding delays
from openai import OpenAI
from tqdm import tqdm
import numpy as np
from metrics import match  # Import the same metric function used in baseline

def load_json(file):
    with open(file, 'r') as f:
        return json.load(f)

def save_json(data, fp):
    with open(fp, mode='w') as f:
        json.dump(data, f)

client = OpenAI(
    base_url="http://interweb.l3s.uni-hannover.de",
    api_key=os.getenv("57Gu2bPVYOWejCZnQk9PflzBvht0N5vWnJrsS6FgDH2EEYYyasPEMd1saJk7aOu8"),
)

# Load the dataset using jsonlines like the baseline
input_file = '/home/<USER>/selfrag_project/self-rag/retrieval_lm/experiments/entity_questions/entity_questions_long_tail_disambiguated_filtered_retrieve20_contrievermsm.json'
input_data = load_json(input_file)

# Process each instance
for item in tqdm(input_data):
    # Format instruction/question like baseline
    instruction = item.get('instruction', item.get('question'))

    # Create message with baseline-like formatting
    message_text = [{
        "role": "user",
        "content": "### Instruction:\n{}\n\n### Response:\n".format(instruction)
    }]

    # Run the GPT-4o model
    completion = client.chat.completions.create(
        model="gpt-4o",
        messages=message_text,
        temperature=0.0,  # Using 0 like baseline
        max_tokens=100,
        top_p=1,
        stop=None
    )

    # Store prediction
    item['preds'] = completion.choices[0].message.content

    # Calculate metric like baseline
    if 'golds' not in item:
        if 'answers' in item:
            item['golds'] = item['answers']
        elif 'preds' in item:
            item['golds'] = item['preds']

    # Calculate match metric
    item['metric_result'] = match(item['preds'], item['golds'])

    # Add a delay between requests to avoid hitting rate limits
    time.sleep(1)  # Adjust the delay time as needed (e.g., 1 second)

# Print overall result like baseline
print("overall result: {0}".format(
    np.mean([item["metric_result"] for item in input_data])))

# Save results in same format as baseline
save_json(input_data, 'entity_questions_disambig_gpt4o_no_retrieval_results.jsonl')
