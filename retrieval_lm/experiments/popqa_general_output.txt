WARNING 07-19 10:42:44 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 07-19 10:42:44 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_7b', tokenizer='selfrag/selfrag_llama2_7b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 07-19 10:43:31 llm_engine.py:223] # GPU blocks: 1929, # CPU blocks: 512
INFO 07-19 10:43:32 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 07-19 10:43:38 model_runner.py:437] Graph capturing finished in 5 secs.
Model prediction: Who wrote the script?

Label: <PERSON>[Utility:5]</s>
Model prediction: What country is Central?[No Retrieval]The answer is the Philippines.[Utility:5]</s>
Model prediction: Who directed the work?[Utility:5]</s>
Model prediction: Who produced the soundtrack for the film Amélie?[Retrieval]<paragraph>[Irrelevant]Yann Tiersen, Yann Pierre Tiersen[Utility:5]</s>
Model prediction: Who was in charge of writing?[No Retrieval]Peter Hyams.[Utility:5]</s>
Model prediction: What genre is an automobile magazine or car magazine?[Utility:5]</s>
Model prediction: What job does a poetess do?[Utility:5]</s>
Model prediction: What athletic activity is played with a ball and a hoop?[No Retrieval]Options:
- football
- baseball
- basketball
- volleyball
- soccer

The answer is basketball.[Utility:5]</s>
Model prediction: Who produced the documentary "Beth Murphy: A Life in Pursuit"?[Utility:5]</s>
Model prediction: Bob is associated with what kind of comedy?[Utility:5]</s>
