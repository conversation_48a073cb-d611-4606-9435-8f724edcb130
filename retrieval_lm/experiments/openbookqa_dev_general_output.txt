WARNING 07-18 13:51:14 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 07-18 13:51:14 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='/home/<USER>/model_cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 07-18 14:02:24 llm_engine.py:223] # GPU blocks: 265, # CPU blocks: 327
INFO 07-18 14:02:27 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 07-18 14:02:34 model_runner.py:437] Graph capturing finished in 7 secs.
Model prediction: C) air motion[Utility:5]</s>
Model prediction: D) harvesting the crops of corn[Utility:5]</s>
Model prediction: D) Rock[Utility:5]</s>
Model prediction: D) December[Utility:5]</s>
Model prediction: B) it takes it apart[Utility:5]</s>
Model prediction: C) sunlight to shine through bulbs[Utility:5]</s>
Model prediction: C) It is cold[Utility:5]</s>
Model prediction: D) loses a lot of strength.[Utility:5]</s>
Model prediction: D)  water[Utility:5]</s>
Model prediction: B) a rain puddle is suddenly gone[Utility:5]</s>
