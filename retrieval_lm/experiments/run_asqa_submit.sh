#!/bin/bash
 
#SBATCH --job-name=asqa
#SBATCH --nodes=1
#SBATCH --gpus=1
#SBATCH --output=asqa_13B_output.log  
#SBATCH --error=asqa_13B_error.log   
#SBATCH --partition=ampere  

source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

python run_long_form_static.py \
  --model_name selfrag/selfrag_llama2_13b \
  --ndocs 5 --max_new_tokens 300 --threshold 0.2 \
  --use_grounding --use_utility --use_seqscore \
  --task asqa --input_file asqa_eval_gtr_top100.json \
  --output_file asqa_results.json --max_depth 7 --mode always_retrieve \