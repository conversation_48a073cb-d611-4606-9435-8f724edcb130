from vllm import LLM, SamplingParams

model = LLM("selfrag/selfrag_llama2_13b", download_dir="/home/<USER>/model_cache", dtype="half")
sampling_params = SamplingParams(temperature=0.0, top_p=1.0, max_tokens=100, skip_special_tokens=False)

def format_prompt(input, paragraph=None):
  prompt = "### Instruction:\n{0}\n\n### Response:\n".format(input)
  if paragraph is not None:
    prompt += "[Retrieval]<paragraph>{0}</paragraph>".format(paragraph)
  return prompt



query_1 = "What is <PERSON>'s occupation?"

query_2 = "What is <PERSON>'s occupation?"

query_3 = "What is <PERSON>'s occupation?"

query_4 = "What is <PERSON><PERSON>'s occupation?"

query_5 = "What is <PERSON>'s occupation?"


queries = [query_1,query_2, query_3, query_4,query_5]

# for a query that doesn't require retrieval
preds = model.generate([format_prompt(query) for query in queries], sampling_params)
for pred in preds:
  print("Model prediction: {0}".format(pred.outputs[0].text))

