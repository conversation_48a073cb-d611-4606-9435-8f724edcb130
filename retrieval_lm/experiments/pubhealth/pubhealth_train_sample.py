from vllm import LLM, SamplingParams

model = LLM("selfrag/selfrag_llama2_7b", download_dir="/home/<USER>/model_cache", dtype="half")
sampling_params = SamplingParams(temperature=0.0, top_p=0.95, max_tokens=100, skip_special_tokens=False)

def format_prompt(input, paragraph=None):
    prompt = "### Instruction:\n{0}\n\n### Response:\n".format(input)
    if paragraph is not None:
        prompt += "[Retrieval]<paragraph>{0}</paragraph>".format(paragraph)
    return prompt

query_1 = """Instruction: You are provided with the first piece of an instance from the train split of the pubhealth dataset. Finish the second piece of the instance exactly as it appears in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: Juvenile bald eagle found on ground to be released Tuesday.

Label: true

Second Piece:"""

query_2 = '''Instruction: You are provided with the first piece of an instance from the train split of the pubhealth dataset. Finish the second piece of the instance exactly as it appears in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: """As a candidate, President <PERSON> """"declared that everyone deserves access to reproductive health care that includes abortion, and vowed that this 'right' would be at the heart of his health care reform plan if elected president.""""""

Label: true

Second Piece:'''


query_3 = """Instruction: You are provided with the first piece of an instance from the train split of the pubhealth dataset. Finish the second piece of the instance exactly as it appears in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: Doctors screen for cervical cancer too often: study

Label: true

Second Piece:"""

query_4 = """Instruction: You are provided with the first piece of an instance from the train split of the pubhealth dataset. Finish the second piece of the instance exactly as it appears in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: Halloween hanging stunt goes wrong and hangs a man for real.

Label: true

Second Piece:"""

query_5 = """Instruction: You are provided with the first piece of an instance from the train split of the pubhealth dataset. Finish the second piece of the instance exactly as it appears in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: Bubba Watson composed a widely shared message that criticized Black Lives Matter and included various conservative talking points.

Label: unproven

Second Piece:"""

query_6 = """Instruction: You are provided with the first piece of an instance from the train split of the pubhealth dataset. Finish the second piece of the instance exactly as it appears in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: Vegetarians are unaffected by COVID-19 because the virus is passed on through eating meat.

Label: false

Second Piece:"""

query_7 = """Instruction: You are provided with the first piece of an instance from the train split of the pubhealth dataset. Finish the second piece of the instance exactly as it appears in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: Novel 'patient-friendly' colonoscopy prep shows excellent efficacy and safety

Label: mixture

Second Piece:"""

query_8 = """Instruction: You are provided with the first piece of an instance from the train split of the pubhealth dataset. Finish the second piece of the instance exactly as it appears in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: Kerastem Reports Successful US Phase II Hair Growth Clinical Trial Results

Label: false

Second Piece:"""

query_9 = """Instruction: You are provided with the first piece of an instance from the train split of the pubhealth dataset. Finish the second piece of the instance exactly as it appears in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: Seventy percent of all Texans are overweight or obese.

Label: true

Second Piece:"""

query_10 = """Instruction: You are provided with the first piece of an instance from the train split of the pubhealth dataset. Finish the second piece of the instance exactly as it appears in the dataset. Only rely on the original form of the instance in the dataset to finish the second piece.

First piece: On resigning her Texas House seat.

Label: false

Second Piece:"""

queries = [query_1, query_2, query_3, query_4, query_5, query_6, query_7, query_8, query_9, query_10]

# for a query that doesn't require retrieval
preds = model.generate([format_prompt(query) for query in queries], sampling_params)
for pred in preds:
    print("Model prediction: {0}".format(pred.outputs[0].text))
