WARNING 10-29 15:45:44 config.py:467] Casting torch.bfloat16 to torch.float16.
INFO 10-29 15:45:44 llm_engine.py:73] Initializing an LLM engine with config: model='selfrag/selfrag_llama2_13b', tokenizer='selfrag/selfrag_llama2_13b', tokenizer_mode=auto, revision=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=4096, download_dir='.cache', load_format=auto, tensor_parallel_size=1, quantization=None, enforce_eager=False, seed=0)
INFO 10-29 15:46:57 llm_engine.py:223] # GPU blocks: 1427, # CPU blocks: 327
INFO 10-29 15:47:00 model_runner.py:394] Capturing the model for CUDA graphs. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 10-29 15:47:05 model_runner.py:437] Graph capturing finished in 5 secs.
average: 0.0
average: 0.7272727272727273
average: 0.7619047619047619
average: 0.7096774193548387
average: 0.7073170731707317
average: 0.7254901960784313
average: 0.7540983606557377
average: 0.7605633802816901
average: 0.7530864197530864
average: 0.7802197802197802
average: 0.7722772277227723
average: 0.7657657657657657
average: 0.7603305785123967
average: 0.7709923664122137
average: 0.7730496453900709
average: 0.7682119205298014
average: 0.7701863354037267
average: 0.7660818713450293
average: 0.7734806629834254
average: 0.774869109947644
average: 0.7661691542288557
average: 0.7725118483412322
average: 0.7782805429864253
average: 0.7835497835497836
average: 0.7883817427385892
average: 0.7888446215139442
average: 0.7816091954022989
average: 0.7785977859778598
average: 0.7758007117437722
average: 0.7697594501718213
average: 0.770764119601329
average: 0.77491961414791
average: 0.7725856697819314
average: 0.7764350453172205
average: 0.7712609970674487
average: 0.7749287749287749
average: 0.7728531855955678
average: 0.7735849056603774
average: 0.7742782152230971
average: 0.7621483375959079
average: 0.7556109725685786
average: 0.7566909975669099
average: 0.7577197149643705
average: 0.7563805104408353
average: 0.7573696145124716
average: 0.7605321507760532
average: 0.7635574837310195
average: 0.7622080679405521
average: 0.7567567567567568
average: 0.7576374745417516
average: 0.7584830339321357
average: 0.7573385518590998
average: 0.761996161228407
average: 0.7589453860640302
average: 0.7578558225508318
average: 0.7568058076225045
average: 0.7557932263814616
average: 0.7548161120840631
average: 0.7538726333907056
average: 0.7529610829103215
average: 0.7537437603993344
average: 0.7545008183306056
average: 0.7536231884057971
average: 0.7527733755942948
average: 0.7519500780031201
average: 0.7496159754224271
average: 0.7518910741301059
average: 0.7540983606557377
average: 0.7533039647577092
average: 0.7539797395079595
average: 0.7532097004279601
average: 0.7538677918424754
average: 0.7558945908460472
average: 0.7564979480164159
average: 0.7597840755735492
average: 0.7576564580559254
average: 0.7582128777923784
average: 0.7600518806744487
average: 0.7592829705505761
average: 0.7597977243994943
average: 0.7602996254681648
average: 0.7595561035758323
average: 0.761266747868453
average: 0.762936221419976
average: 0.7645659928656362
average: 0.7649823736780259
average: 0.7642276422764228
average: 0.7657864523536165
average: 0.7650397275822929
average: 0.7643097643097643
average: 0.7669256381798002
average: 0.7650933040614709
average: 0.7665580890336591
average: 0.7669172932330827
average: 0.7662061636556854
average: 0.7655099894847529
average: 0.7669094693028096
average: 0.7672502574665293
average: 0.7655453618756372
Final result: 0.7639311043566362
Retrieval Frequencies: 123.375
