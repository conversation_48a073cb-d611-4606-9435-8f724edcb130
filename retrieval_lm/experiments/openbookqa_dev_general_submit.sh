#!/bin/bash
#SBATCH --job-name=openbookqa_dev_general
#SBATCH --output=openbookqa_dev_general_output.txt
#SBATCH --error=openbookqa_dev_general_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=02:00:00

# Load your environment
source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

# Run your Python script
python openbookqa_dev_general.py
