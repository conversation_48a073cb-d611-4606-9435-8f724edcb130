#!/bin/bash
 
#SBATCH --job-name=arc_challenge_selfrag13b
#SBATCH --nodes=1
#SBATCH --gpus=1
#SBATCH --output=eval_arc_challenge_13B_output.log  
#SBATCH --error=eval_arc_challenge_13B_error.log   
#SBATCH --partition=ampere  

source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag


python run_short_form.py \
  --model_name selfrag/selfrag_llama2_13b \
  --input_file arc_challenge_processed.jsonl \
  --max_new_tokens 50 --threshold 0.2 \
  --output_file arc_challenge_output.jsonl \
  --metric match --ndocs 5 --use_groundness --use_utility --use_seqscore \
  --task arc_c