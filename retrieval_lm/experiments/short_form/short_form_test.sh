#!/bin/bash
 
#SBATCH --job-name=eval_pqa_rerun2
#SBATCH --nodes=1
#SBATCH --gpus=1
#SBATCH --output=eval_pqa_rerun2_output.log  
#SBATCH --error=eval_pqa_rerun2_error.log   
#SBATCH --partition=ampere  

source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

python run_short_form.py \
--model_name selfrag/selfrag_llama2_13b \
--input_file popqa_longtail_w_gs.jsonl \
--mode adaptive_retrieval --max_new_tokens 100 \
--threshold 0.2 \
--output_file popqa_longtail_output_w_gs_13b_with_retrieval_tokens.jsonl \
--metric match --ndocs 10 --use_groundness --use_utility --use_seqscore \
--dtype half