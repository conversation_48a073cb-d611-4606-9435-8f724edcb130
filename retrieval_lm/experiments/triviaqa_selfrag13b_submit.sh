#!/bin/bash
#SBATCH --job-name=triviaqa_selfrag13B
#SBATCH --output=triviaqa_selfrag13B_output.txt
#SBATCH --error=triviaqa_selfrag13B_error.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --gpus=1
#SBATCH --time=02:00:00
#SBATCH --partition=ampere 

# Activate conda environment
source /opt/conda/etc/profile.d/conda.sh
conda activate selfrag

# Run the Python script
python triviaqa_selfrag13b_predictions.py
